伟大的金融家，您好！

本文档是 `policy_liquidity_volatility_arbitrage` 项目的核心开发指导方案，整合了先前各项讨论和方案的精华。

**系统升级总览**

本项目 (`policy_liquidity_volatility_arbitrage`) 的核心目标是将您的量化分析系统从主要依赖新闻情感和传统资金流（如 `News-Emotional-Model` 项目所示），提升为一个综合考量政策导向、多层次资金博弈及市场波动状态的智能决策系统。

主要包含以下几个方面：
1.  **阶段0: 项目初始化与基础模块构建**：建立项目骨架，迁移和适配现有成熟模块，配置基础环境。
2.  **阶段1: 新闻与政策融合模块 (News & Policy Engine)**：构建强大的新闻与政策信息获取、解析和量化能力。
3.  **阶段2: 五级分层资金流分析模块 (Tiered Fund Flow Engine)**：引入精细化的五级资金分层模型，替代传统资金流分析。
4.  **阶段3: 波动率分析模块 (Volatility Engine)**：新增波动率分析维度，用于风险评估和策略优化。
5.  **阶段4: 核心决策引擎升级 (Decision Engine Enhancement)**：在 `policy_liquidity_volatility_arbitrage/main.py` (或独立的 `decision_engine/core.py`) 中构建新的多因子评分与决策模型。

---
**阶段0: 项目初始化与基础模块构建 (基于 `policy_liquidity_volatility_arbitrage` 项目)**

此阶段的目标是搭建新项目的骨架，为后续高级引擎的开发奠定坚实基础。

1.  **基础目录结构与文件创建:**
    *   **根目录**: `policy_liquidity_volatility_arbitrage/`
    *   **核心引擎**: `engines/`
        *   `engines/news_policy/` (存放新闻和政策获取、解析模块)
        *   `engines/tiered_fund_flow/` (存放五级分层资金流模块)
        *   `engines/volatility/` (存放波动率分析模块)
        *   `engines/sentiment/` (如果需要，可存放情感分析模块)
    *   **信号与决策**: `signal_generation/`, `decision_engine/`
    *   **执行**: `execution/` (如QMT适配器)
    *   **工具与配置**: `utils/`, `config/`, `data/`, `logs/`, `models/` (存放机器学习模型)
    *   **测试**: `tests/`
    *   **初始化文件**: 在各Python包目录下创建 `__init__.py`。
    *   **环境与依赖**: 创建/更新 `requirements.txt` (akshare, pandas, numpy, loguru, pytz, tqdm, torch, transformers, pyyaml, feedparser, arch, requests-html, beautifulsoup4, jieba等)，以及 `.gitignore`。

2.  **核心工具模块 (`utils/`) 创建与填充**:
    *   `utils/logger.py`: 配置全局日志记录。
    *   `utils/config_loader.py`: 加载 `config/config.yaml` 中的配置。
    *   `utils/data_utils.py`: 存放数据处理函数 (如 `convert_amount`, `normalize_by_market_value`, `get_latest_trading_date` 等)。
    *   `utils/error_utils.py`: 存放错误处理装饰器 (如 `@retry`, `@handle_api_error`, `@safe_execute`)。
    *   `utils/cache_utils.py`: 实现缓存逻辑 (内存、文件或Redis)。
    *   `utils/network_utils.py`: (可选) 封装网络请求，如异步获取。

3.  **配置中心化 (`config/config.yaml`)**:
    *   定义所有路径、API密钥（安全存储）、模型位置、RSS源、数据库连接、策略参数、各引擎权重和阈值等。

4.  **现有成熟模块迁移与适配 (从 `News-Emotional-Model` 或其他来源)**:
    *   **新闻获取**: 将 `News-Emotional-Model/news_fetcher.py` 的核心逻辑适配到 `engines/news_policy/fetcher.py`。
    *   **情感分析**: `News-Emotional-Model/sentiment_model.py` 迁移并适配到 `engines/sentiment/analyzer.py` (如果新系统直接使用情感分析)。
    *   **股票信息管理**: `News-Emotional-Model/stock_info_manager.py` 可考虑迁移或重构部分功能到 `utils/stock_utils.py` 或 `data/` 管理模块。
    *   **交易接口**: `News-Emotional-Model/qmt_trader.py` 适配到 `execution/qmt_adapter.py`，并从新配置加载。
    *   **旧资金流分析**: `News-Emotional-Model/fund_flow_analyzer.py` 的部分数据获取方法（如LHB, RZRQ原始数据）可供新的 `TieredFundFlowAnalyzer` 参考或复用，但其整体将被新引擎替代。

5.  **主入口点 (`policy_liquidity_volatility_arbitrage/main.py`)**:
    *   作为程序的启动和总调度器。
    *   负责加载配置、初始化日志、实例化各核心引擎和执行适配器。
    *   编排主要的执行流程：数据获取 -> 分析 -> 信号生成 -> 决策 -> 执行。
    *   原 `News-Emotional-Model/main.py` 中的 `StockScreener` 类逻辑将被分解到新的引擎和决策模块中。

---
**一、新闻与政策融合模块 (News & Policy Engine)**
(位于 `engines/news_policy/`)

此模块负责获取、解析和量化新闻及政策信息。

**1. 新闻与政策获取 (`engines/news_policy/fetcher.py`)**
   *   **`NewsPolicyFetcher` 类**:
        *   **`__init__(self, config)`**: 初始化，从配置加载新闻源、政策来源列表、关键词等。
            *   政策来源：国务院、央行、财政部、发改委、证监会、交易所公告、行业协会等。（参考您研究文档中 `政策开发与插入.md` 的信源）
            *   可使用 `requests-html` 进行异步抓取。
        *   **`fetch_official_documents(self, source_name, days_lookback=7)`**: (替代原`fetch_policy_documents`)
            *   **逻辑**: 针对特定官方来源（如国务院公报、部委网站），异步/同步爬取或通过API获取指定来源在回溯期内的政策文件（HTML, PDF, DOC）。
            *   **输出**: DataFrame，包含 `['publish_date', 'source', 'title', 'full_text', 'url', 'doc_type':'policy_document']`。
        *   **`fetch_policy_rss_feeds(self, days_lookback=7)`**:
            *   **逻辑**: 使用 `feedparser` 监控配置的政策RSS源。
            *   **输出**: DataFrame，包含 `['publish_date', 'source', 'title', 'summary', 'link', 'doc_type':'policy_rss']`。
        *   **`fetch_market_news(self, days_lookback=7)`**:
            *   **逻辑**: 从 `News-Emotional-Model/news_fetcher.py` 适配和优化，获取财经新闻。
            *   **输出**: DataFrame，包含 `['publish_date', 'source', 'title', 'content', 'keywords', 'doc_type':'news']`。
        *   **`get_market_information(self, days_lookback=7, include_news=True, include_policy_docs=True, include_policy_rss=True)`**:
            *   **逻辑**: 调用上述方法，合并所有信息源，统一格式。
            *   **输出**: 包含所有信息的DataFrame，带有 `doc_type` 列。

**2. 政策解析与量化 (`engines/news_policy/analyzer.py`)**
   *   **`PolicyAnalyzer` 类**:
        *   **`__init__(self, config, bert_model_path=None, industry_mapping_path=None)`**:
            *   从配置加载BERT模型路径（如BERT-Tiny或您选择的`ProsusAI/finbert`）、行业映射表路径。
            *   初始化NLP模型（如 `transformers.pipeline` 或自定义模型）。
            *   行业映射表：政策关键词/主题 -> 申万一级/二级行业，需预先构建。
        *   **`parse_document_text(self, doc_text, doc_source, doc_date, doc_type)`**: (替代原 `parse_policy_document`)
            *   **逻辑**:
                1.  针对 `policy_document` 和 `policy_rss` 类型：使用BERT模型进行NER，提取政策三元组 `(政策主体, 政策动作, 影响对象)`。提取关键词、关键短语。识别发文机构级别、政策类型（如指导意见、暂行条例、规划、补贴细则等）。
                2.  针对 `news` 类型：可复用情感分析的NER结果或进行特定实体提取。
            *   **输出**: 结构化字典，包含解析结果 `{'entities': [], 'keywords': [], 'main_subjects': [], 'actions': [], 'objects': [], 'source_level': '', 'policy_category': ''}`。
        *   **`calculate_policy_metrics(self, parsed_doc_info, stock_code=None, stock_industry=None)`**: (替代原 `calculate_policy_heat_and_impact`)
            *   **逻辑**: 仅针对政策类文档。
                1.  **政策热度 (PH)**: `PH = 发文部门权重 × 政策类型系数 × 关键词频率/重要性`。权重和系数需在配置中预定义。
                2.  **行业影响评估 (Industry Impact Score - HI)**: 根据政策影响对象和行业映射表，评估政策对各行业的潜在影响方向（+1 利好, -1 利空, 0 中性）和强度 (0-1)。
                3.  **个股政策关联度 (Policy Relevance Score - PRS)** (如果提供了`stock_code`和`stock_industry`): `PRS = HI_for_stock_industry × stock_industry_sensitivity + (个股被直接提及的加权调整)`。行业敏感度需预定义或学习。
            *   **输出**: 字典，包含 `{'policy_id', 'overall_heat': PH, 'industry_impacts': {'industry_code': {'direction': D, 'strength': S}}, 'stock_relevance': PRS (if applicable)}`。
        *   **`batch_process_documents(self, documents_df, stock_data_df=None)`**:
            *   **逻辑**: 对DataFrame中的每条文档调用 `parse_document_text`，对政策类文档调用 `calculate_policy_metrics`。如果提供了`stock_data_df`（含股票代码和行业），则计算个股关联度。
            *   **输出**: 包含分析结果的DataFrame。

**3. 新闻情感分析 (`engines/sentiment/analyzer.py`)**
    *   基本保持 `News-Emotional-Model/sentiment_model.py` 的 `SentimentAnalyzer` 类。
    *   确保可以处理 `NewsPolicyFetcher` 输出的DataFrame。
    *   `analyze_news_df` 方法应能专注于 `doc_type == 'news'` 的部分，并返回情感评分。

---
**二、五级分层资金流分析模块 (Tiered Fund Flow Engine)**
(位于 `engines/tiered_fund_flow/`)

此模块将提供更精细的资金流向分析。

**1. `TieredFundFlowAnalyzer` 类 (`engines/tiered_fund_flow/analyzer.py`)**:
    *   **`__init__(self, config)`**: 初始化，从配置加载API设置、模型路径（如果需要）。
    *   **`NorthboundAlternative` 子模块/内部类**:
        *   严格按照您提供的 `北向资金替代方案.md` 文档设计。
        *   **核心方法**: `_get_qfii_derived_flow()`, `_get_etf_premium_flow()`, `_analyze_connect_arbitrage_flow()`, `_get_custody_signal()`, `_calculate_swap_arbitrage()`。
        *   `get_synthetic_northbound_flow(self, start_date, end_date)`:
            *   **逻辑**: 综合以上各部分，计算每日合成北向资金流和置信度。
            *   **输出**: DataFrame `['date', 'synthetic_northbound_flow', 'confidence_level']`。
    *   **`get_margin_flow(self, stock_code, start_date, end_date)`**:
        *   **逻辑**: 使用 `ak.stock_margin_detail_sse` 和 `ak.stock_margin_detail_szse` 获取融资融券余额、买入额、偿还额等。计算每日融资净买入、融券净卖出。
        *   **输出**: DataFrame `['date', 'stock_code', 'margin_balance', 'margin_net_buy', 'short_balance', 'short_net_sell']`。
    *   **`get_institutional_flow(self, stock_code, start_date, end_date)`**:
        *   **逻辑**:
            1.  **公募基金**: `ak.fund_portfolio_hold_em` 查看季度持仓，结合基金净值规模变动估算。
            2.  **龙虎榜机构席位**: `ak.stock_lhb_detail_em`，按日统计机构专用席位的净买入额。
            3.  （可选）其他机构如社保的定期报告。
        *   **输出**: DataFrame `['date', 'stock_code', 'mutual_fund_flow_est', 'lhb_institutional_net_buy']`。
    *   **`get_hot_money_flow(self, stock_code, start_date, end_date)`**: (游资)
        *   **逻辑**:
            1.  深入分析 `ak.stock_lhb_detail_em`，识别著名游资席位列表（可配置或动态识别）。
            2.  统计这些席位在特定股票上的每日净买入额、上榜次数。
            3.  参考 `ak.stock_lhb_hy_em` (活跃营业部)。
        *   **输出**: DataFrame `['date', 'stock_code', 'hot_money_active_score', 'hot_money_net_buy']`。
    *   **`get_retail_flow(self, stock_code, start_date, end_date)`**: (散户)
        *   **逻辑**:
            1.  股东户数变化: `ak.stock_zh_a_gdhs_detail_em`，获取最新及历史数据，分析户数变化率及户均持股市值。数据频率可能较低。
            2.  **替代/补充**: 分析 `ak.stock_individual_fund_flow` 中的小单净流入数据（如果可靠）或Level-2行情中的散单数据。
        *   **输出**: DataFrame `['date', 'stock_code', 'shareholder_change_rate', 'avg_holding_value', 'small_order_net_flow']`。
    *   **`fetch_all_tiered_flows_for_stock(self, stock_code, start_date, end_date)`**:
        *   **逻辑**: 并行调用上述各方法获取该股票在回溯期内所有层级的资金流数据。
        *   **输出**: 一个字典，键为层面名称，值为对应的DataFrame。
    *   **`calculate_stock_tiered_flow_score(self, stock_code, tiered_flow_data_dict, stock_info)`**:
        *   `stock_info` 应包含市值、行业、价格变动等。
        *   **逻辑**:
            1.  对每一层资金流数据进行趋势分析、强度计算、归一化处理。
            2.  根据股票市值、行业特性、当前市场状态（可由波动率引擎或宏观指标判断），动态调整各层级资金流的评分权重。
            3.  分析层间互动：例如，北向与机构协同、主力与散户背离等模式。
            4.  综合各层级评分和互动模式，得到总的资金流评分 (0-1范围)。
        *   **输出**: 数值型评分。

---
**三、波动率分析模块 (Volatility Engine)**
(位于 `engines/volatility/`)

此模块负责计算和分析股票及市场的波动率。

**1. `VolatilityAnalyzer` 类 (`engines/volatility/analyzer.py`)**:
    *   **`__init__(self, config)`**: 初始化。
    *   **`get_historical_prices(self, stock_code, start_date, end_date)`**:
        *   **逻辑**: 使用 `ak.stock_zh_a_hist` 获取股票历史日K线数据。
        *   **输出**: DataFrame `['date', 'open', 'high', 'low', 'close', 'volume']`。
    *   **`calculate_historical_volatility(self, prices_df, window=20, annualize=True)`**:
        *   **逻辑**: 基于收盘价计算指定窗口的已实现波动率（对数收益率标准差），可选择年化。
        *   **输出**: 数值。
    *   **`calculate_garch_volatility(self, prices_df, p=1, q=1)` (高级)**:
        *   **逻辑**: 拟合GARCH(p,q)模型，预测下一期条件波动率。需要 `arch` 库。
        *   **输出**: 数值（预测波动率）。
    *   **`get_implied_volatility(self, stock_code, current_date)` (如果适用)**:
        *   **逻辑**: 如果股票有期权交易，从 `ak.option_sina_sse_spot_price_sina` 或类似接口获取期权价格，使用Black-Scholes或类似模型反推隐含波动率。
        *   **输出**: 数值或字典（按不同期权）。
    *   **`get_stock_volatility_profile(self, stock_code, start_date, end_date)`**:
        *   **逻辑**: 获取单个股票的综合波动率指标。调用上述方法。
        *   **输出**: 字典 `{'historical_vol_20d': val, 'historical_vol_60d': val, 'garch_forecast': val, 'implied_vol': val}`。
    *   **`analyze_market_volatility_regime(self, market_index_code='sh000001', start_date, end_date)`**:
        *   **逻辑**: 分析大盘指数（如上证综指、沪深300）的历史波动率，或直接获取VIX指数（`ak.idx_vix`）。判断市场整体波动状态（如高、中、低）。
        *   **输出**: 市场波动状态描述（如 'High_Volatility', 'Low_Volatility', 'Trending_Up', 'Trending_Down'）。

---
**四、核心决策引擎升级 (Decision Engine Enhancement)**
(位于 `policy_liquidity_volatility_arbitrage/main.py` 或 `decision_engine/core.py`)

**1. `DecisionEngine` 类 (如果独立创建) 或 `StockScreener` 类 (`main.py` 中重构)**:
    *   **`__init__(self, config, news_policy_fetcher, policy_analyzer, sentiment_analyzer, tiered_fund_flow_analyzer, volatility_analyzer, stock_info_manager)`**:
        *   实例化或接收所有分析引擎的实例。
    *   **`generate_stock_features(self, stock_code, stock_info, market_info)`**:
        *   `stock_info` 包含行业、市值等。`market_info` 包含市场波动状态、当前日期等。
        *   **逻辑**:
            1.  **新闻与政策**:
                *   获取与该股票相关的最新新闻和政策文档。
                *   `parsed_docs = self.policy_analyzer.batch_process_documents(relevant_docs_df, stock_info_for_this_stock_df)`
                *   `sentiment_scores = self.sentiment_analyzer.analyze_news_df(parsed_docs[parsed_docs['doc_type']=='news'])`
                *   计算综合新闻政策得分 (`news_policy_score`)：结合情感分、政策热度、个股政策关联度。
            2.  **资金流**:
                *   `tiered_flow_data_dict = self.tiered_fund_flow_analyzer.fetch_all_tiered_flows_for_stock(stock_code, ...)`
                *   `tiered_flow_score = self.tiered_fund_flow_analyzer.calculate_stock_tiered_flow_score(stock_code, tiered_flow_data_dict, stock_info)`
            3.  **波动率**:
                *   `volatility_profile = self.volatility_analyzer.get_stock_volatility_profile(stock_code, ...)`
                *   `volatility_factor = self._calculate_volatility_adjustment_factor(volatility_profile, market_info['market_vol_regime'])` (例如，对高波动股票进行惩罚，或在特定市场状态下寻找波动率机会)。
            4.  **其他基础因子**: (可从原 `StockScreener` 迁移并调整)
                *   机构调研 (`get_institutional_research`)
                *   大宗交易 (`get_block_trade`)
        *   **输出**: 包含所有计算出的特征和评分的字典。
    *   **`calculate_final_score(self, stock_features_dict, market_info)`**:
        *   **逻辑**:
            *   根据 `market_info['market_vol_regime']` 和其他宏观判断，动态调整各因子（`news_policy_score`, `tiered_flow_score`, research_score, block_trade_score等）的权重。
            *   `final_score = (news_policy_score * W_NP + tiered_flow_score * W_TF + ...) * volatility_factor`
        *   **输出**: 最终综合评分。
    *   **`get_recommendations(self, stock_pool, top_n=30)`**:
        *   **数据获取阶段**:
            1.  获取市场整体信息（如波动率状态）。
            2.  并行处理 `stock_pool` 中的每只股票，调用 `generate_stock_features()`。
        *   **评分阶段**:
            1.  对每只股票调用 `calculate_final_score()`。
        *   **结果处理**: 排序、保存推荐列表（包含各主要分项得分，供分析）。
        *   **输出**: 推荐股票列表 (DataFrame)。

---
**五、实施建议与后续步骤**

1.  **迭代开发**: 严格按照阶段0到阶段4的顺序逐步实现和测试各模块。
2.  **配置驱动**: 所有关键参数、路径、API密钥、模型选择等均通过 `config/config.yaml` 管理。
3.  **数据管理**: 规划 `data/` 目录，用于存放行业映射表、预处理的词典、缓存的API数据、模型文件等。
4.  **日志与监控**: 在所有模块中加入详细的日志记录。
5.  **测试**: 为每个工具函数和引擎的核心方法编写单元测试。在各阶段完成后进行集成测试。
6.  **回测框架**: 在核心引擎基本可用后，对接或构建回测框架，验证策略有效性。
7.  **性能优化**: 对数据获取、NLP处理、资金流计算等耗时环节进行性能分析和优化（如异步IO、批量处理、缓存、模型剪枝/量化）。
8.  **模型训练与更新**:
    *   NLP模型（BERT）如果使用预训练模型，考虑是否需要在金融语料上进行微调。
    *   行业映射表、政策权重、因子权重等需要定期回顾和更新。

**关于北向资金替代方案的具体实现**：
在 `engines/tiered_fund_flow/analyzer.py` 中，`NorthboundAlternative` 子模块将严格按照您提供的 `北向资金替代方案.md` 文档进行设计。其 `get_synthetic_northbound_flow()` 方法将整合QFII/RQFII持仓、海外A股ETF资金流、港股通双向流量及AH股溢价，以及辅助验证数据如离岸人民币汇率，最终输出合成的、带有置信度的北向资金流估算值。这个估算值将作为五级资金分层模型中"合成北向资金"这一层的数据输入。

此方案详细规划了各模块的功能和交互，为后续开发提供了清晰的蓝图。
