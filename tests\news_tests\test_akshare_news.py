"""
测试AKShare新闻和公告相关API
"""

import akshare as ak
import pandas as pd

def test_news_economic_pboc():
    """测试央行公告API"""
    print("测试 news_economic_pboc API...")
    
    try:
        # 获取央行公告
        df = ak.news_economic_pboc()
        
        # 打印数据信息
        print(f"数据形状: {df.shape}")
        print(f"列名: {df.columns.tolist()}")
        print("\n前5行数据:")
        print(df.head())
        
        return df
    except Exception as e:
        print(f"错误: {str(e)}")
        return None

def test_news_report_csrc():
    """测试证监会公告API"""
    print("\n测试 news_report_csrc API...")
    
    try:
        # 获取证监会公告
        df = ak.news_report_csrc()
        
        # 打印数据信息
        print(f"数据形状: {df.shape}")
        print(f"列名: {df.columns.tolist()}")
        print("\n前5行数据:")
        print(df.head())
        
        return df
    except Exception as e:
        print(f"错误: {str(e)}")
        return None

def test_news_economic_baidu():
    """测试百度财经新闻API"""
    print("\n测试 news_economic_baidu API...")
    
    try:
        # 获取百度财经新闻
        df = ak.news_economic_baidu()
        
        # 打印数据信息
        print(f"数据形状: {df.shape}")
        print(f"列名: {df.columns.tolist()}")
        print("\n前5行数据:")
        print(df.head())
        
        return df
    except Exception as e:
        print(f"错误: {str(e)}")
        return None

def test_news_economic_cctv():
    """测试央视财经新闻API"""
    print("\n测试 news_economic_cctv API...")
    
    try:
        # 获取央视财经新闻
        df = ak.news_economic_cctv()
        
        # 打印数据信息
        print(f"数据形状: {df.shape}")
        print(f"列名: {df.columns.tolist()}")
        print("\n前5行数据:")
        print(df.head())
        
        return df
    except Exception as e:
        print(f"错误: {str(e)}")
        return None

def test_news_economic_10jqka():
    """测试同花顺财经新闻API"""
    print("\n测试 news_economic_10jqka API...")
    
    try:
        # 获取同花顺财经新闻
        df = ak.news_economic_10jqka()
        
        # 打印数据信息
        print(f"数据形状: {df.shape}")
        print(f"列名: {df.columns.tolist()}")
        print("\n前5行数据:")
        print(df.head())
        
        return df
    except Exception as e:
        print(f"错误: {str(e)}")
        return None

def test_news_economic_sina():
    """测试新浪财经新闻API"""
    print("\n测试 news_economic_sina API...")
    
    try:
        # 获取新浪财经新闻
        df = ak.news_economic_sina()
        
        # 打印数据信息
        print(f"数据形状: {df.shape}")
        print(f"列名: {df.columns.tolist()}")
        print("\n前5行数据:")
        print(df.head())
        
        return df
    except Exception as e:
        print(f"错误: {str(e)}")
        return None

if __name__ == "__main__":
    # 测试央行公告API
    pboc = test_news_economic_pboc()
    
    # 测试证监会公告API
    csrc = test_news_report_csrc()
    
    # 测试百度财经新闻API
    baidu = test_news_economic_baidu()
    
    # 测试央视财经新闻API
    cctv = test_news_economic_cctv()
    
    # 测试同花顺财经新闻API
    jqka = test_news_economic_10jqka()
    
    # 测试新浪财经新闻API
    sina = test_news_economic_sina()
