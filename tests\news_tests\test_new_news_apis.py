"""
测试新添加的新闻接口
"""

import os
import pandas as pd
from datetime import datetime, timedelta
import time

# 创建必要的目录
os.makedirs('logs', exist_ok=True)
os.makedirs('data/cache/policy', exist_ok=True)

# 导入政策数据源
from data_sources.policy_data import PolicyDataSource

def test_function(func, *args, **kwargs):
    """通用测试函数"""
    print(f"\n测试 {func.__name__}...")
    
    # 不使用缓存获取数据
    start_time = time.time()
    data = func(*args, **kwargs, use_cache=False)
    end_time = time.time()
    
    print(f"获取数据成功，耗时: {end_time - start_time:.2f}秒")
    print(f"数据数量: {len(data)}")
    if not data.empty:
        print("数据预览:")
        print(data.head(2))
    else:
        print("数据为空")
    
    # 使用缓存获取数据
    start_time = time.time()
    data_cached = func(*args, **kwargs, use_cache=True)
    end_time = time.time()
    
    print(f"从缓存获取数据，耗时: {end_time - start_time:.2f}秒")
    print(f"数据数量: {len(data_cached)}")
    
    return data

def main():
    """主测试函数"""
    print("开始测试新添加的新闻接口...")
    
    policy_data = PolicyDataSource()
    
    # 测试获取个股新闻-东方财富
    stock_news_em = test_function(policy_data.get_stock_news_em, symbol="300059")
    
    # 测试获取财经内容精选-财新网
    news_main_cx = test_function(policy_data.get_news_main_cx)
    
    print("\n新闻接口测试完成!")

if __name__ == "__main__":
    main()
