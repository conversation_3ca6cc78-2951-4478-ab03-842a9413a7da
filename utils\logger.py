"""
Logger module for policy_liquidity_volatility_arbitrage.
Configures global logging.
"""

import os
import sys
from datetime import datetime
from loguru import logger

def setup_logger(log_dir="logs", log_level="INFO"):
    """
    Set up the logger for the application.
    
    Args:
        log_dir (str): Directory to store log files.
        log_level (str): Log level (DEBUG, INFO, WARNING, ERROR, CRITICAL).
        
    Returns:
        logger: Configured logger instance.
    """
    # Create log directory if it doesn't exist
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    # Generate log filename with timestamp
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = os.path.join(log_dir, f"policy_liquidity_volatility_{timestamp}.log")
    
    # Remove default logger
    logger.remove()
    
    # Add console logger
    logger.add(
        sys.stderr,
        level=log_level,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>"
    )
    
    # Add file logger
    logger.add(
        log_file,
        level=log_level,
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
        rotation="10 MB",  # Rotate when file reaches 10MB
        retention="30 days",  # Keep logs for 30 days
        compression="zip"  # Compress rotated logs
    )
    
    logger.info(f"Logger initialized. Log file: {log_file}")
    return logger

# Default logger instance
default_logger = setup_logger()
