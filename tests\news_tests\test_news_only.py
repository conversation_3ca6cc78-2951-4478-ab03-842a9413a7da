"""
测试新闻数据源（仅新闻部分）
"""

import os
import pandas as pd
from datetime import datetime, timedelta
import time

# 创建必要的目录
os.makedirs('logs', exist_ok=True)
os.makedirs('data/cache/policy', exist_ok=True)

# 导入政策数据源
from data_sources.policy_data import PolicyDataSource

def test_function(func, *args, **kwargs):
    """通用测试函数"""
    print(f"\n测试 {func.__name__}...")
    
    # 不使用缓存获取数据
    start_time = time.time()
    data = func(*args, **kwargs, use_cache=False)
    end_time = time.time()
    
    print(f"获取数据成功，耗时: {end_time - start_time:.2f}秒")
    print(f"数据数量: {len(data)}")
    if not data.empty:
        print("数据预览:")
        print(data.head(2))
    else:
        print("数据为空")
    
    # 使用缓存获取数据
    start_time = time.time()
    data_cached = func(*args, **kwargs, use_cache=True)
    end_time = time.time()
    
    print(f"从缓存获取数据，耗时: {end_time - start_time:.2f}秒")
    print(f"数据数量: {len(data_cached)}")
    
    return data

def main():
    """主测试函数"""
    print("开始测试新闻数据源...")
    
    policy_data = PolicyDataSource()
    
    # 测试获取财经早餐
    financial_breakfast = test_function(policy_data.get_financial_breakfast)
    
    # 测试获取全球财经快讯-东财财富
    global_news_em = test_function(policy_data.get_global_news_em)
    
    # 测试获取全球财经快讯-新浪财经
    global_news_sina = test_function(policy_data.get_global_news_sina)
    
    # 测试获取快讯-富途牛牛
    global_news_futu = test_function(policy_data.get_global_news_futu)
    
    # 测试获取全球财经直播-同花顺财经
    global_news_ths = test_function(policy_data.get_global_news_ths)
    
    # 测试获取电报-财联社
    global_news_cls = test_function(policy_data.get_global_news_cls, symbol="全部")
    
    # 测试获取证券原创-新浪财经
    broker_news_sina = test_function(policy_data.get_broker_news_sina, page="1")
    
    print("\n新闻数据源测试完成!")

if __name__ == "__main__":
    main()
