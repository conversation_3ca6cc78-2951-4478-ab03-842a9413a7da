# 政策-流动性分层-波动率套利系统文档

## 1. 系统概述

本系统是一个综合考量政策导向、多层次资金博弈及市场波动状态的智能决策系统。它从主要依赖新闻情感和传统资金流的分析方法，升级为一个更全面、更精细的量化分析系统。系统通过获取和分析政策信息、新闻数据和市场数据，生成投资建议和交易信号。

## 2. 系统架构

系统主要包含以下几个核心模块：

1. **数据源模块 (data_sources)**：负责获取政策、新闻和市场数据
2. **新闻与政策融合模块 (engines/news_policy)**：分析政策和新闻数据，提取有价值的信息
3. **五级分层资金流分析模块 (engines/tiered_fund_flow)**：分析不同层次的资金流向
4. **波动率分析模块 (engines/volatility)**：分析市场波动状态，识别波动率机会
5. **情感分析模块 (engines/sentiment)**：分析新闻和政策的情感倾向
6. **决策引擎 (decision_engine)**：综合各模块的分析结果，生成投资建议
7. **执行模块 (execution)**：执行交易决策

## 3. 文件夹和脚本说明

### 3.1 数据源模块 (data_sources/)

#### 3.1.1 政策数据源 (policy_data.py)

**功能**：获取政策和新闻数据，包括国务院政策、发改委政策、财经早餐、全球财经快讯等。

**主要方法**：
- `get_gov_policy()`: 获取国务院政策文件库政策
- `get_ndrc_policy()`: 获取发改委政策
- `get_policy_content()`: 获取政策详细内容
- `get_financial_breakfast()`: 获取财经早餐-东财财富
- `get_global_news_em()`: 获取全球财经快讯-东财财富
- `get_global_news_sina()`: 获取全球财经快讯-新浪财经
- `get_global_news_futu()`: 获取快讯-富途牛牛
- `get_global_news_ths()`: 获取全球财经直播-同花顺财经
- `get_global_news_cls()`: 获取电报-财联社
- `get_broker_news_sina()`: 获取证券原创-新浪财经
- `get_stock_news_em()`: 获取个股新闻-东方财富
- `get_news_main_cx()`: 获取财经内容精选-财新网

#### 3.1.2 新闻处理器 (news_processor.py)

**功能**：处理和分析新闻数据，包括去重、相似度分析和热度计算。

**主要方法**：
- `process_news()`: 处理新闻列表，去重并计算热度
- `_calculate_text_similarity()`: 计算文本相似度
- `_extract_keywords()`: 提取关键词
- `_calculate_news_score()`: 计算新闻热度分数
- `save_processed_news()`: 保存处理后的新闻
- `load_processed_news()`: 加载处理后的新闻
- `update_processed_news()`: 更新处理后的新闻

#### 3.1.3 新闻监控器 (news_monitor.py)

**功能**：提供24小时滚动获取新闻和板块/个股关注功能。

**主要方法**：
- `fetch_news_from_all_sources()`: 从所有来源获取新闻
- `fetch_and_process_news()`: 获取并处理新闻
- `analyze_hot_topics()`: 分析热点话题
- `generate_daily_report()`: 生成每日报告
- `start_monitoring()`: 开始监控
- `stop_monitoring()`: 停止监控

#### 3.1.4 市场数据源 (market_data.py)

**功能**：获取A股市场基础数据，包括股票列表、行业分类、股票历史行情和指数历史行情。

**主要方法**：
- `get_stock_list()`: 获取A股股票列表
- `get_stock_industry()`: 获取股票行业分类
- `get_stock_history()`: 获取股票历史行情数据
- `get_index_history()`: 获取指数历史行情数据

### 3.2 引擎模块 (engines/)

#### 3.2.1 新闻与政策分析模块 (news_policy/)

- **fetcher.py**: 获取新闻和政策数据
- **analyzer.py**: 分析新闻和政策数据

#### 3.2.2 情感分析模块 (sentiment/)

- **analyzer.py**: 分析新闻和政策的情感倾向

#### 3.2.3 五级分层资金流分析模块 (tiered_fund_flow/)

- **analyzer.py**: 分析不同层次的资金流向

#### 3.2.4 波动率分析模块 (volatility/)

- **analyzer.py**: 分析市场波动状态，识别波动率机会

### 3.3 决策引擎 (decision_engine/)

- **core.py**: 综合各模块的分析结果，生成投资建议

### 3.4 执行模块 (execution/)

- **qmt_adapter.py**: 适配QMT交易系统，执行交易决策

### 3.5 工具模块 (utils/)

- **cache_utils.py**: 缓存工具
- **config_loader.py**: 配置加载工具
- **data_utils.py**: 数据处理工具
- **error_utils.py**: 错误处理工具
- **logger.py**: 日志工具
- **network_utils.py**: 网络请求工具
- **stock_utils.py**: 股票相关工具

### 3.6 测试模块 (tests/)

- **news_tests/**: 新闻模块测试
  - **test_akshare_api.py**: AKShare API测试
  - **test_akshare_news.py**: AKShare新闻测试
  - **test_market_data.py**: 市场数据测试
  - **test_new_news_apis.py**: 新闻API测试
  - **test_news_monitor.py**: 新闻监控测试
  - **test_news_processor.py**: 新闻处理测试
  - **test_policy_data.py**: 政策数据测试
  - **test_report_generation.py**: 报告生成测试

### 3.7 文档模块 (docs/)

- **development_progress_report.md**: 开发成果报告
- **news_system_analysis_report.md**: 系统分析报告
- **system_organization_report.md**: 系统整理报告

### 3.8 示例模块 (examples/)

- **news_monitor_example.py**: 新闻监控示例
- **basic_usage.py**: 基本使用示例

### 3.9 主程序 (main.py)

**功能**：系统入口点，协调各模块工作，生成投资建议。

**主要类和方法**：
- `StockScreener`: 主要股票筛选器类
  - `analyze_market()`: 分析市场状况
  - `generate_recommendations()`: 生成股票推荐
  - `execute_trades()`: 执行交易
  - `run()`: 运行股票筛选器

## 4. 数据流

1. **数据获取**：通过数据源模块获取政策、新闻和市场数据
2. **数据处理**：通过新闻处理器处理和分析新闻数据
3. **数据监控**：通过新闻监控器监控新闻和政策数据
4. **数据分析**：通过各个引擎模块分析数据
5. **决策生成**：通过决策引擎生成投资建议
6. **交易执行**：通过执行模块执行交易决策

## 5. 冗余脚本分析

经过分析，以下脚本可能存在冗余或重复功能：

1. **测试脚本**：`tests/news_tests/`目录下的多个测试脚本功能有所重叠，可以考虑合并或重构
2. **数据源脚本**：`data_sources/policy_data.py`中的某些方法功能相似，可以考虑抽象为通用方法
3. **示例脚本**：`examples/`目录下的示例脚本可以更加精简和统一

## 6. 待完成项目

根据数据源规划文档，以下项目尚未完成：

1. **地方政策数据源**：各省政务服务网的政策数据抓取功能
2. **高级处理工具**：如FinBERT等深度学习模型的集成
3. **24小时监测功能优化**：提高稳定性和效率
4. **分布式架构**：实现分布式任务调度和数据处理
