{"model_configs": {"finbert": {"model_name": "ProsusAI/finbert", "local_path": "models/finbert", "task": "sentiment-analysis", "labels": ["negative", "neutral", "positive"]}, "financial_sentiment": {"model_name": "mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis", "local_path": "models/financial-sentiment", "task": "sentiment-analysis", "labels": ["NEGATIVE", "POSITIVE"]}, "chinese_bert": {"model_name": "bert-base-chinese", "local_path": "models/chinese-bert", "task": "classification", "labels": ["negative", "neutral", "positive"]}}, "default_model": "<PERSON><PERSON>", "fallback_model": "chinese_bert", "cache_dir": "models/cache", "max_length": 512, "batch_size": 16}