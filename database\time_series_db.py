"""
时序数据库客户端

实现InfluxDB数据库的访问，并提供CSV回退方案
"""

import logging
import json
import os
import csv
from typing import Dict, Any, List, Optional, Union
from datetime import datetime
import pandas as pd

from .db_client import DBClient
from .data_types import TIME_SERIES_DATA_TYPES

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('time_series_db')

class InfluxDBClient(DBClient):
    """InfluxDB数据库客户端"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化InfluxDB数据库客户端
        
        Args:
            config: 配置信息
        """
        super().__init__(config)
        
        # 如果配置了使用CSV回退，则初始化CSV存储
        self.use_csv_fallback = config.get('use_csv_fallback', True)
        self.csv_dir = config.get('csv_dir', os.path.join('data', 'time_series'))
        
        # 尝试连接数据库
        self.connect()
    
    def connect(self) -> bool:
        """
        连接数据库
        
        Returns:
            连接结果
        """
        try:
            # 尝试导入influxdb
            from influxdb import InfluxDBClient as InfluxClient
            
            # 连接InfluxDB
            self.connection = InfluxClient(
                host=self.config.get('host', 'localhost'),
                port=self.config.get('port', 8086),
                username=self.config.get('user', 'admin'),
                password=self.config.get('password', 'admin'),
                database=self.config.get('database', 'financial_system')
            )
            
            # 创建数据库（如果不存在）
            dbs = self.connection.get_list_database()
            if {'name': self.config.get('database', 'financial_system')} not in dbs:
                self.connection.create_database(self.config.get('database', 'financial_system'))
            
            self._log_info("InfluxDB数据库连接成功")
            return True
        
        except ImportError:
            self._log_error("influxdb模块未安装，无法连接InfluxDB", Exception("Module not found"))
            
            # 如果配置了使用CSV回退，则使用CSV存储
            if self.use_csv_fallback:
                self._init_csv_storage()
                self._log_info("使用CSV存储作为InfluxDB的回退")
                return True
            
            return False
        
        except Exception as e:
            self._log_error("InfluxDB数据库连接失败", e)
            
            # 如果配置了使用CSV回退，则使用CSV存储
            if self.use_csv_fallback:
                self._init_csv_storage()
                self._log_info("使用CSV存储作为InfluxDB的回退")
                return True
            
            return False
    
    def _init_csv_storage(self):
        """初始化CSV存储"""
        # 确保CSV目录存在
        os.makedirs(self.csv_dir, exist_ok=True)
    
    def disconnect(self) -> bool:
        """
        断开数据库连接
        
        Returns:
            断开结果
        """
        try:
            if self.connection:
                self.connection.close()
                self.connection = None
            
            self._log_info("InfluxDB连接已断开")
            return True
        
        except Exception as e:
            self._log_error("断开InfluxDB连接失败", e)
            return False
    
    def is_connected(self) -> bool:
        """
        检查数据库连接状态
        
        Returns:
            连接状态
        """
        if self.connection:
            try:
                # 尝试执行简单查询
                self.connection.ping()
                return True
            except:
                return False
        
        # 如果使用CSV回退，则始终返回True
        return self.use_csv_fallback
    
    def query(self, data_type: str, query_params: Dict[str, Any], options: Optional[Dict[str, Any]] = None) -> Any:
        """
        查询数据
        
        Args:
            data_type: 数据类型
            query_params: 查询参数
            options: 选项参数
        
        Returns:
            查询结果
        """
        options = options or {}
        
        if data_type not in TIME_SERIES_DATA_TYPES:
            raise ValueError(f"未知的时序数据类型: {data_type}")
        
        measurement = TIME_SERIES_DATA_TYPES[data_type].get('measurement')
        if not measurement:
            raise ValueError(f"数据类型 {data_type} 没有指定测量名称")
        
        # 使用InfluxDB或CSV存储查询数据
        if self.connection:
            try:
                return self._query_influxdb(measurement, query_params, options)
            except Exception as e:
                self._log_error(f"InfluxDB查询失败: {measurement}", e)
                if self.use_csv_fallback:
                    return self._query_csv(measurement, query_params, options)
                raise
        elif self.use_csv_fallback:
            return self._query_csv(measurement, query_params, options)
        else:
            raise ConnectionError("InfluxDB未连接且未启用CSV回退")
    
    def _query_influxdb(self, measurement: str, query_params: Dict[str, Any], options: Dict[str, Any]) -> Any:
        """
        使用InfluxDB查询数据
        
        Args:
            measurement: 测量名称
            query_params: 查询参数
            options: 选项参数
        
        Returns:
            查询结果
        """
        # 构建查询语句
        query = f"SELECT * FROM {measurement}"
        
        # 添加WHERE子句
        where_clauses = []
        for key, value in query_params.items():
            if key == 'time_start':
                where_clauses.append(f"time >= '{value}'")
            elif key == 'time_end':
                where_clauses.append(f"time <= '{value}'")
            else:
                where_clauses.append(f"\"{key}\" = '{value}'")
        
        if where_clauses:
            query += f" WHERE {' AND '.join(where_clauses)}"
        
        # 添加ORDER BY子句
        order_by = options.get('order_by', 'time DESC')
        query += f" ORDER BY {order_by}"
        
        # 添加LIMIT子句
        if 'limit' in options:
            query += f" LIMIT {options['limit']}"
        
        # 执行查询
        result = self.connection.query(query)
        
        # 处理结果
        points = []
        for point in result.get_points():
            points.append(point)
        
        return points
    
    def _query_csv(self, measurement: str, query_params: Dict[str, Any], options: Dict[str, Any]) -> Any:
        """
        使用CSV查询数据
        
        Args:
            measurement: 测量名称
            query_params: 查询参数
            options: 选项参数
        
        Returns:
            查询结果
        """
        # 构建CSV文件路径
        csv_path = self._get_csv_path(measurement, query_params)
        
        # 检查文件是否存在
        if not os.path.exists(csv_path):
            return []
        
        try:
            # 读取CSV文件
            df = pd.read_csv(csv_path)
            
            # 应用过滤条件
            for key, value in query_params.items():
                if key == 'time_start':
                    df = df[df['time'] >= value]
                elif key == 'time_end':
                    df = df[df['time'] <= value]
                elif key in df.columns:
                    df = df[df[key] == value]
            
            # 应用排序
            order_by = options.get('order_by', 'time DESC')
            if 'DESC' in order_by:
                sort_column = order_by.replace(' DESC', '')
                df = df.sort_values(by=sort_column, ascending=False)
            else:
                sort_column = order_by.replace(' ASC', '')
                df = df.sort_values(by=sort_column, ascending=True)
            
            # 应用限制
            if 'limit' in options:
                df = df.head(options['limit'])
            
            # 转换为字典列表
            return df.to_dict('records')
        
        except Exception as e:
            self._log_error(f"CSV查询失败: {csv_path}", e)
            return []
    
    def save(self, data_type: str, data: Any, options: Optional[Dict[str, Any]] = None) -> Any:
        """
        保存数据
        
        Args:
            data_type: 数据类型
            data: 要保存的数据
            options: 选项参数
        
        Returns:
            保存结果
        """
        options = options or {}
        
        if data_type not in TIME_SERIES_DATA_TYPES:
            raise ValueError(f"未知的时序数据类型: {data_type}")
        
        measurement = TIME_SERIES_DATA_TYPES[data_type].get('measurement')
        if not measurement:
            raise ValueError(f"数据类型 {data_type} 没有指定测量名称")
        
        # 使用InfluxDB或CSV存储保存数据
        if self.connection:
            try:
                return self._save_influxdb(measurement, data_type, data, options)
            except Exception as e:
                self._log_error(f"InfluxDB保存失败: {measurement}", e)
                if self.use_csv_fallback:
                    return self._save_csv(measurement, data_type, data, options)
                raise
        elif self.use_csv_fallback:
            return self._save_csv(measurement, data_type, data, options)
        else:
            raise ConnectionError("InfluxDB未连接且未启用CSV回退")
    
    def _save_influxdb(self, measurement: str, data_type: str, data: Any, options: Dict[str, Any]) -> Any:
        """
        使用InfluxDB保存数据
        
        Args:
            measurement: 测量名称
            data_type: 数据类型
            data: 要保存的数据
            options: 选项参数
        
        Returns:
            保存结果
        """
        # 获取标签和字段
        tags = TIME_SERIES_DATA_TYPES[data_type].get('tags', [])
        fields = TIME_SERIES_DATA_TYPES[data_type].get('fields', [])
        
        # 处理单条数据或多条数据
        points = []
        if isinstance(data, dict):
            points.append(self._prepare_point(measurement, data, tags, fields))
        elif isinstance(data, list) and all(isinstance(item, dict) for item in data):
            for item in data:
                points.append(self._prepare_point(measurement, item, tags, fields))
        else:
            raise ValueError("数据必须是字典或字典列表")
        
        # 写入数据
        self.connection.write_points(points)
        
        return data
    
    def _prepare_point(self, measurement: str, data: Dict[str, Any], tags: List[str], fields: List[str]) -> Dict[str, Any]:
        """
        准备数据点
        
        Args:
            measurement: 测量名称
            data: 数据
            tags: 标签字段
            fields: 值字段
        
        Returns:
            数据点
        """
        point = {
            "measurement": measurement,
            "tags": {},
            "fields": {},
            "time": data.get('time', datetime.now().isoformat())
        }
        
        # 添加标签
        for tag in tags:
            if tag in data:
                point["tags"][tag] = data[tag]
        
        # 添加字段
        for field in fields:
            if field in data:
                point["fields"][field] = data[field]
        
        return point
    
    def _save_csv(self, measurement: str, data_type: str, data: Any, options: Dict[str, Any]) -> Any:
        """
        使用CSV保存数据
        
        Args:
            measurement: 测量名称
            data_type: 数据类型
            data: 要保存的数据
            options: 选项参数
        
        Returns:
            保存结果
        """
        # 获取标签和字段
        tags = TIME_SERIES_DATA_TYPES[data_type].get('tags', [])
        fields = TIME_SERIES_DATA_TYPES[data_type].get('fields', [])
        
        # 构建CSV文件路径
        csv_path = self._get_csv_path(measurement, data if isinstance(data, dict) else {})
        
        # 确保目录存在
        os.makedirs(os.path.dirname(csv_path), exist_ok=True)
        
        # 处理单条数据或多条数据
        rows = []
        if isinstance(data, dict):
            rows.append(self._prepare_row(data, tags, fields))
        elif isinstance(data, list) and all(isinstance(item, dict) for item in data):
            for item in data:
                rows.append(self._prepare_row(item, tags, fields))
        else:
            raise ValueError("数据必须是字典或字典列表")
        
        # 检查文件是否存在
        file_exists = os.path.exists(csv_path)
        
        # 写入CSV文件
        mode = 'a' if file_exists else 'w'
        with open(csv_path, mode, newline='') as f:
            # 获取所有列
            columns = ['time'] + tags + fields
            
            writer = csv.DictWriter(f, fieldnames=columns)
            
            # 如果文件不存在，写入表头
            if not file_exists:
                writer.writeheader()
            
            # 写入数据
            for row in rows:
                writer.writerow(row)
        
        return data
    
    def _prepare_row(self, data: Dict[str, Any], tags: List[str], fields: List[str]) -> Dict[str, Any]:
        """
        准备CSV行
        
        Args:
            data: 数据
            tags: 标签字段
            fields: 值字段
        
        Returns:
            CSV行
        """
        row = {
            "time": data.get('time', datetime.now().isoformat())
        }
        
        # 添加标签
        for tag in tags:
            if tag in data:
                row[tag] = data[tag]
            else:
                row[tag] = ""
        
        # 添加字段
        for field in fields:
            if field in data:
                row[field] = data[field]
            else:
                row[field] = ""
        
        return row
    
    def _get_csv_path(self, measurement: str, data: Dict[str, Any]) -> str:
        """
        获取CSV文件路径
        
        Args:
            measurement: 测量名称
            data: 数据
        
        Returns:
            CSV文件路径
        """
        # 使用测量名称和主要标签构建文件路径
        path_parts = [self.csv_dir, measurement]
        
        # 如果有code标签，添加到路径
        if 'code' in data:
            path_parts.append(data['code'])
        
        # 构建目录路径
        dir_path = os.path.join(*path_parts)
        
        # 确保目录存在
        os.makedirs(dir_path, exist_ok=True)
        
        # 构建文件名
        # 如果有日期，使用年月作为文件名
        if 'time' in data and len(data['time']) >= 7:
            file_name = data['time'][:7].replace('-', '') + '.csv'  # YYYYMM.csv
        else:
            # 否则使用当前年月
            file_name = datetime.now().strftime('%Y%m') + '.csv'
        
        return os.path.join(dir_path, file_name)
    
    def delete(self, data_type: str, query_params: Dict[str, Any], options: Optional[Dict[str, Any]] = None) -> bool:
        """
        删除数据
        
        Args:
            data_type: 数据类型
            query_params: 查询参数
            options: 选项参数
        
        Returns:
            删除结果
        """
        options = options or {}
        
        if data_type not in TIME_SERIES_DATA_TYPES:
            raise ValueError(f"未知的时序数据类型: {data_type}")
        
        measurement = TIME_SERIES_DATA_TYPES[data_type].get('measurement')
        if not measurement:
            raise ValueError(f"数据类型 {data_type} 没有指定测量名称")
        
        if not query_params:
            raise ValueError("删除操作必须指定查询参数")
        
        # 使用InfluxDB或CSV存储删除数据
        if self.connection:
            try:
                return self._delete_influxdb(measurement, query_params, options)
            except Exception as e:
                self._log_error(f"InfluxDB删除失败: {measurement}", e)
                if self.use_csv_fallback:
                    return self._delete_csv(measurement, query_params, options)
                raise
        elif self.use_csv_fallback:
            return self._delete_csv(measurement, query_params, options)
        else:
            raise ConnectionError("InfluxDB未连接且未启用CSV回退")
    
    def _delete_influxdb(self, measurement: str, query_params: Dict[str, Any], options: Dict[str, Any]) -> bool:
        """
        使用InfluxDB删除数据
        
        Args:
            measurement: 测量名称
            query_params: 查询参数
            options: 选项参数
        
        Returns:
            删除结果
        """
        # 构建删除语句
        query = f"DELETE FROM {measurement}"
        
        # 添加WHERE子句
        where_clauses = []
        for key, value in query_params.items():
            if key == 'time_start':
                where_clauses.append(f"time >= '{value}'")
            elif key == 'time_end':
                where_clauses.append(f"time <= '{value}'")
            else:
                where_clauses.append(f"\"{key}\" = '{value}'")
        
        if where_clauses:
            query += f" WHERE {' AND '.join(where_clauses)}"
        
        # 执行删除
        self.connection.query(query)
        
        return True
    
    def _delete_csv(self, measurement: str, query_params: Dict[str, Any], options: Dict[str, Any]) -> bool:
        """
        使用CSV删除数据
        
        Args:
            measurement: 测量名称
            query_params: 查询参数
            options: 选项参数
        
        Returns:
            删除结果
        """
        # 构建CSV文件路径
        csv_path = self._get_csv_path(measurement, query_params)
        
        # 检查文件是否存在
        if not os.path.exists(csv_path):
            return False
        
        try:
            # 读取CSV文件
            df = pd.read_csv(csv_path)
            
            # 应用过滤条件
            mask = pd.Series(True, index=df.index)
            for key, value in query_params.items():
                if key == 'time_start':
                    mask = mask & (df['time'] >= value)
                elif key == 'time_end':
                    mask = mask & (df['time'] <= value)
                elif key in df.columns:
                    mask = mask & (df[key] == value)
            
            # 删除匹配的行
            df = df[~mask]
            
            # 写回CSV文件
            df.to_csv(csv_path, index=False)
            
            return True
        
        except Exception as e:
            self._log_error(f"CSV删除失败: {csv_path}", e)
            return False
