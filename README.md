# 政策-流动性分层-波动率套利系统

## 项目概述

本项目是一个综合考量政策导向、多层次资金博弈及市场波动状态的智能决策系统。它从主要依赖新闻情感和传统资金流的分析方法，升级为一个更全面、更精细的量化分析系统。

## 系统架构

系统主要包含以下几个核心模块：

1. **新闻与政策融合模块 (News & Policy Engine)**：构建强大的新闻与政策信息获取、解析和量化能力。
2. **五级分层资金流分析模块 (Tiered Fund Flow Engine)**：引入精细化的五级资金分层模型，替代传统资金流分析。
3. **波动率分析模块 (Volatility Engine)**：新增波动率分析维度，用于风险评估和策略优化。
4. **核心决策引擎 (Decision Engine)**：构建新的多因子评分与决策模型。

## 目录结构

```
policy_liquidity_volatility_arbitrage/
├── data_sources/                 # 数据源模块
│   ├── policy_data.py            # 政策和新闻数据源
│   ├── news_processor.py         # 新闻处理器
│   ├── news_monitor.py           # 新闻监控器
│   └── market_data.py            # 市场数据源
├── engines/                      # 核心引擎
│   ├── news_policy/              # 新闻和政策分析模块
│   ├── tiered_fund_flow/         # 五级分层资金流模块
│   ├── volatility/               # 波动率分析模块
│   └── sentiment/                # 情感分析模块
├── signal_generation/            # 信号生成
├── decision_engine/              # 决策引擎
├── execution/                    # 执行模块
├── utils/                        # 工具函数
├── config/                       # 配置文件
├── data/                         # 数据文件
│   ├── cache/                    # 缓存数据
│   └── news_monitor/             # 新闻监控数据
├── docs/                         # 文档目录
│   ├── development_progress_report.md  # 开发成果报告
│   └── news_system_analysis_report.md  # 系统分析报告
├── logs/                         # 日志文件
├── models/                       # 模型文件
├── tests/                        # 测试文件
│   └── news_tests/               # 新闻模块测试
├── main.py                       # 主入口
├── requirements.txt              # 依赖列表
└── README.md                     # 项目说明
```

## 安装与配置

### 环境要求

- Python 3.8+
- 依赖包：见 `requirements.txt`

### 安装步骤

1. 克隆仓库：
   ```
   git clone <repository_url>
   cd policy_liquidity_volatility_arbitrage
   ```

2. 安装依赖：
   ```
   pip install -r requirements.txt
   ```

3. 配置：
   - 编辑 `config/config.yaml` 文件，根据需要调整参数

## 使用方法

### 获取政策和新闻数据

```python
from data_sources.policy_data import PolicyDataSource

# 创建政策数据源
policy_data = PolicyDataSource()

# 获取国务院政策
gov_policies = policy_data.get_gov_policy(page=1, limit=10)
print(f"获取到 {len(gov_policies)} 条国务院政策")

# 获取财经早餐
breakfast = policy_data.get_financial_breakfast()
print(f"获取到 {len(breakfast)} 条财经早餐")
```

### 处理新闻数据

```python
from data_sources.policy_data import PolicyDataSource
from data_sources.news_processor import NewsProcessor

# 创建政策数据源和新闻处理器
policy_data = PolicyDataSource()
news_processor = NewsProcessor()

# 获取新闻数据
news_list = []
news_list.extend(policy_data.get_financial_breakfast().to_dict('records'))
news_list.extend(policy_data.get_global_news_em().to_dict('records'))

# 处理新闻数据
processed_news = news_processor.process_news(news_list)
print(f"处理后的新闻数量: {len(processed_news)}")
```

### 启动新闻监控

```python
from data_sources.news_monitor import NewsMonitor

# 创建新闻监控器
monitor = NewsMonitor()

# 开始监控
monitor.start_monitoring()

# 生成每日报告
report_file = monitor.generate_daily_report()
print(f"报告生成成功: {report_file}")
```

### 系统运行

```
python main.py --mode analyze
```

### 参数说明

- `--config`: 配置文件路径，默认为 `config/config.yaml`
- `--mode`: 运行模式，可选 `analyze` 或 `backtest`，默认为 `analyze`
- `--execute`: 是否执行交易，默认为 `False`
- `--log-level`: 日志级别，可选 `DEBUG`、`INFO`、`WARNING`、`ERROR`，默认为 `INFO`

## 开发计划

系统开发分为以下几个阶段：

1. **阶段0**: 项目初始化与基础模块构建 ✓
2. **阶段1**: 新闻与政策融合模块开发 ✓
   - 政策数据源实现 ✓
   - 新闻数据源实现 ✓
   - 新闻处理与分析 ✓
   - 24小时滚动监控 ✓
3. **阶段2**: 五级分层资金流分析模块开发
4. **阶段3**: 波动率分析模块开发
5. **阶段4**: 核心决策引擎升级

## 当前进展

目前已完成新闻与政策融合模块的开发，并取得了波动率分析模块和资金流分析模块的重要进展：

1. **政策数据源**：实现了从国务院政策文件库和发改委网站获取政策信息
2. **新闻数据源**：实现了从9个财经媒体获取新闻数据
3. **新闻处理**：实现了新闻去重、相似度分析和热度计算
4. **新闻监控**：实现了24小时滚动获取新闻和板块/个股关注功能
5. **报告生成**：实现了每日新闻热点分析报告生成
6. **波动率分析模块**：已完成单元测试，可以计算市场、行业和个股波动率
7. **资金流分析模块**：已完成单元测试，北向资金监测功能已升级为北向南向资金监测功能
8. **资金流-波动率集成**：已完成集成测试，实现了资金流与波动率的联动分析

详细信息请参阅 [开发成果报告](docs/development_progress_report.md)、[系统分析报告](docs/news_system_analysis_report.md) 和 [测试进度报告](docs/test_progress_report.md)。

## 分支结构

本项目使用Git进行版本控制，采用以下分支结构：

- **main**：主分支，包含稳定的生产版本代码
- **develop**：开发分支，包含最新的开发代码
- **feature/xxx**：功能分支，用于开发新功能
  - **feature/nlp-model-upgrade**：NLP模型升级分支
  - **feature/news-monitor-enhancement**：新闻监控增强分支
  - **feature/system-integration**：系统集成分支
- **release/x.x.x**：发布分支，用于准备新版本发布
- **hotfix/xxx**：热修复分支，用于修复生产环境中的紧急问题

## 开发流程

1. 从`develop`分支创建新的功能分支
2. 在功能分支上进行开发和测试
3. 完成功能开发后，将功能分支合并回`develop`分支
4. 准备发布时，从`develop`分支创建`release`分支
5. 在`release`分支上进行最终测试和修复
6. 发布完成后，将`release`分支合并到`main`和`develop`分支

## 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建 Pull Request

## 许可证

私有项目，未经授权不得使用、复制或分发。
