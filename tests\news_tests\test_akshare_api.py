"""
测试AKShare API
"""

import akshare as ak
import pandas as pd

def test_stock_sector_spot():
    """测试板块行情API"""
    print("测试 stock_sector_spot API...")
    
    try:
        # 获取板块行情
        df = ak.stock_sector_spot()
        
        # 打印数据信息
        print(f"数据形状: {df.shape}")
        print(f"列名: {df.columns.tolist()}")
        print("\n前5行数据:")
        print(df.head())
        
        return df
    except Exception as e:
        print(f"错误: {str(e)}")
        return None

def test_stock_sector_detail():
    """测试板块详情API"""
    print("\n测试 stock_sector_detail API...")
    
    try:
        # 获取板块详情
        df = ak.stock_sector_detail(sector="板块代码")
        
        # 打印数据信息
        print(f"数据形状: {df.shape}")
        print(f"列名: {df.columns.tolist()}")
        print("\n前5行数据:")
        print(df.head())
        
        return df
    except Exception as e:
        print(f"错误: {str(e)}")
        return None

def test_stock_zh_a_hist():
    """测试股票历史行情API"""
    print("\n测试 stock_zh_a_hist API...")
    
    try:
        # 获取股票历史行情
        df = ak.stock_zh_a_hist(symbol="sh600000", start_date="20230101", end_date="20230110", adjust="qfq")
        
        # 打印数据信息
        print(f"数据形状: {df.shape}")
        print(f"列名: {df.columns.tolist()}")
        print("\n前5行数据:")
        print(df.head())
        
        return df
    except Exception as e:
        print(f"错误: {str(e)}")
        return None

def test_stock_zh_index_daily():
    """测试指数历史行情API"""
    print("\n测试 stock_zh_index_daily API...")
    
    try:
        # 获取指数历史行情
        df = ak.stock_zh_index_daily(symbol="sh000001")
        
        # 打印数据信息
        print(f"数据形状: {df.shape}")
        print(f"列名: {df.columns.tolist()}")
        print("\n前5行数据:")
        print(df.head())
        
        return df
    except Exception as e:
        print(f"错误: {str(e)}")
        return None

if __name__ == "__main__":
    # 测试板块行情API
    sector_spot = test_stock_sector_spot()
    
    # 如果成功获取板块行情，测试板块详情API
    if sector_spot is not None and not sector_spot.empty:
        # 获取第一个板块代码
        sector_code = sector_spot.iloc[0]['label']
        print(f"\n使用板块代码: {sector_code}")
        
        # 修改测试函数
        def test_with_code():
            try:
                df = ak.stock_sector_detail(sector=sector_code)
                print(f"数据形状: {df.shape}")
                print(f"列名: {df.columns.tolist()}")
                print("\n前5行数据:")
                print(df.head())
                return df
            except Exception as e:
                print(f"错误: {str(e)}")
                return None
        
        # 测试板块详情API
        test_with_code()
    
    # 测试股票历史行情API
    test_stock_zh_a_hist()
    
    # 测试指数历史行情API
    test_stock_zh_index_daily()
