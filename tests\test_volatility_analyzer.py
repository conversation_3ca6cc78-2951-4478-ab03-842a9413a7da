#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
波动率分析模块单元测试
"""

import os
import sys
import unittest
import json
from unittest.mock import MagicMock, patch
from datetime import datetime, timedelta

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入被测模块
from data_sources.volatility_analyzer import VolatilityAnalyzer
from core.data_storage import DataStorage, StorageLevel

class TestVolatilityAnalyzer(unittest.TestCase):
    """波动率分析模块单元测试类"""

    def setUp(self):
        """测试前的准备工作"""
        # 创建波动率分析模块实例
        self.volatility_analyzer = VolatilityAnalyzer()

        # 替换数据存储为模拟对象
        self.mock_data_storage = MagicMock(spec=DataStorage)
        self.volatility_analyzer.data_storage = self.mock_data_storage

        # 设置模拟数据
        self.setup_mock_data()

    def setup_mock_data(self):
        """设置模拟数据"""
        # 模拟市场数据
        self.mock_market_data = {
            '000001': {  # 上证指数
                'name': '上证指数',
                'close': [3000, 3050, 3100, 3080, 3120, 3150, 3130, 3160, 3200, 3180],
                'date': [(datetime.now() - timedelta(days=i)).strftime('%Y-%m-%d') for i in range(10, 0, -1)]
            },
            '000300': {  # 沪深300
                'name': '沪深300',
                'close': [4000, 4050, 4100, 4080, 4120, 4150, 4130, 4160, 4200, 4180],
                'date': [(datetime.now() - timedelta(days=i)).strftime('%Y-%m-%d') for i in range(10, 0, -1)]
            },
            '000905': {  # 中证500
                'name': '中证500',
                'close': [6000, 6050, 6100, 6080, 6120, 6150, 6130, 6160, 6200, 6180],
                'date': [(datetime.now() - timedelta(days=i)).strftime('%Y-%m-%d') for i in range(10, 0, -1)]
            }
        }

        # 模拟行业数据
        self.mock_sector_data = {
            '银行': {
                'stocks': ['601398', '601288', '601939'],
                'close': [1000, 1050, 1100, 1080, 1120, 1150, 1130, 1160, 1200, 1180],
                'date': [(datetime.now() - timedelta(days=i)).strftime('%Y-%m-%d') for i in range(10, 0, -1)]
            },
            '医药': {
                'stocks': ['600276', '600196', '603259'],
                'close': [2000, 2050, 2100, 2080, 2120, 2150, 2130, 2160, 2200, 2180],
                'date': [(datetime.now() - timedelta(days=i)).strftime('%Y-%m-%d') for i in range(10, 0, -1)]
            }
        }

        # 模拟个股数据
        self.mock_stock_data = {
            '600519': {  # 贵州茅台
                'name': '贵州茅台',
                'close': [1800, 1850, 1900, 1880, 1920, 1950, 1930, 1960, 2000, 1980],
                'date': [(datetime.now() - timedelta(days=i)).strftime('%Y-%m-%d') for i in range(10, 0, -1)]
            },
            '300750': {  # 宁德时代
                'name': '宁德时代',
                'close': [400, 410, 420, 415, 425, 430, 425, 435, 440, 435],
                'date': [(datetime.now() - timedelta(days=i)).strftime('%Y-%m-%d') for i in range(10, 0, -1)]
            }
        }

        # 模拟政策数据
        self.mock_policy_data = [
            {
                'id': '1',
                'title': '关于进一步加强金融支持实体经济发展的指导意见',
                'date': (datetime.now() - timedelta(days=5)).strftime('%Y-%m-%d'),
                'source': '国务院',
                'importance': 0.8,
                'sentiment': 0.6,
                'keywords': ['金融', '实体经济', '支持'],
                'summary': '加强金融对实体经济的支持，降低融资成本，提高金融服务效率。'
            },
            {
                'id': '2',
                'title': '关于促进消费扩容提质加快形成强大国内市场的实施意见',
                'date': (datetime.now() - timedelta(days=10)).strftime('%Y-%m-%d'),
                'source': '国家发改委',
                'importance': 0.7,
                'sentiment': 0.5,
                'keywords': ['消费', '国内市场', '扩容'],
                'summary': '促进消费扩容提质，加快形成强大国内市场，提振消费信心。'
            }
        ]

        # 模拟资金流数据
        self.mock_fund_flow_data = {
            'market': {
                datetime.now().strftime('%Y-%m-%d'): {
                    'north': {
                        'net_flow': 50.0,  # 亿元
                        'inflow': 150.0,
                        'outflow': 100.0
                    }
                }
            },
            'sector': {
                datetime.now().strftime('%Y-%m-%d'): {
                    '银行': {
                        'net_inflow': 5.0,  # 亿元
                        'net_inflow_ratio': 0.8,
                        'super_large_inflow': 3.0,
                        'large_inflow': 2.0
                    },
                    '医药': {
                        'net_inflow': 8.0,
                        'net_inflow_ratio': 1.2,
                        'super_large_inflow': 5.0,
                        'large_inflow': 3.0
                    }
                }
            },
            'stock': {
                datetime.now().strftime('%Y-%m-%d'): {
                    '600519': {  # 贵州茅台
                        'net_inflow': 10000,  # 万元
                        'super_large_inflow': 6000,
                        'large_inflow': 4000
                    },
                    '300750': {  # 宁德时代
                        'net_inflow': 15000,
                        'super_large_inflow': 10000,
                        'large_inflow': 5000
                    }
                }
            }
        }

        # 设置数据存储的返回值
        self.mock_data_storage.load.side_effect = self.mock_load

    def mock_load(self, module_name, data_name, level=None):
        """模拟数据加载"""
        if module_name == 'market_data' and data_name == 'index_data':
            return self.mock_market_data
        elif module_name == 'market_data' and data_name == 'sector_data':
            return self.mock_sector_data
        elif module_name == 'market_data' and data_name == 'stock_data':
            return self.mock_stock_data
        elif module_name == 'policy_analyzer' and data_name == 'policy_data':
            return self.mock_policy_data
        elif module_name == 'fund_flow_analyzer' and data_name == 'fund_flow_data':
            return self.mock_fund_flow_data
        elif module_name == 'volatility_analyzer' and data_name == 'volatility_data':
            return {
                'market': {},
                'sector': {},
                'stock': {},
                'signals': []
            }
        else:
            return None

    def test_calculate_market_volatility(self):
        """测试市场波动率计算"""
        # 调用被测方法
        result = self.volatility_analyzer.calculate_market_volatility()

        # 验证结果
        self.assertEqual(result['status'], 'success')
        self.assertIn('indices_count', result)
        self.assertGreater(result['indices_count'], 0)
        self.assertIn('signals_count', result)

    def test_calculate_sector_volatility(self):
        """测试行业波动率计算"""
        # 调用被测方法
        result = self.volatility_analyzer.calculate_sector_volatility(sectors=['银行', '医药'])

        # 验证结果
        self.assertEqual(result['status'], 'success')
        self.assertIn('sectors_count', result)
        self.assertGreater(result['sectors_count'], 0)
        self.assertIn('signals_count', result)

    def test_calculate_stock_volatility(self):
        """测试个股波动率计算"""
        # 调用被测方法
        result = self.volatility_analyzer.calculate_stock_volatility(stock_codes=['600519', '300750'])

        # 验证结果
        self.assertEqual(result['status'], 'success')
        self.assertIn('stocks_count', result)
        self.assertGreater(result['stocks_count'], 0)
        self.assertIn('signals_count', result)

    def test_calculate_policy_volatility_premium(self):
        """测试政策波动率溢价计算"""
        # 调用被测方法
        result = self.volatility_analyzer.calculate_policy_volatility_premium()

        # 验证结果
        self.assertIn('status', result)  # 检查状态字段存在
        self.assertIn('message', result)  # 检查消息字段存在

    def test_calculate_fund_flow_volatility_coupling(self):
        """测试资金流-波动率耦合分析"""
        # 调用被测方法
        result = self.volatility_analyzer.calculate_fund_flow_volatility_coupling()

        # 验证结果
        self.assertIn('status', result)  # 检查状态字段存在
        self.assertIn('message', result)  # 检查消息字段存在

    def test_generate_volatility_report(self):
        """测试波动率分析报告生成"""
        # 调用被测方法
        result = self.volatility_analyzer.generate_volatility_report()

        # 验证结果
        self.assertEqual(result['status'], 'success')
        self.assertIn('report', result)

if __name__ == '__main__':
    unittest.main()
