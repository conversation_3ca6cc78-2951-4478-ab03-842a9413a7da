"""
Error handling utilities for policy_liquidity_volatility_arbitrage.
"""

import time
import functools
from utils.logger import logger

def retry(max_retries=3, delay=1, backoff=2, exceptions=(Exception,)):
    """
    Retry decorator with exponential backoff.
    
    Args:
        max_retries (int): Maximum number of retries.
        delay (int): Initial delay in seconds.
        backoff (int): Backoff multiplier.
        exceptions (tuple): Exceptions to catch.
        
    Returns:
        function: Decorated function.
    """
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            mtries, mdelay = max_retries, delay
            while mtries > 0:
                try:
                    return func(*args, **kwargs)
                except exceptions as e:
                    mtries -= 1
                    if mtries == 0:
                        logger.error(f"Function {func.__name__} failed after {max_retries} retries. Error: {str(e)}")
                        raise
                    
                    logger.warning(f"Function {func.__name__} failed. Retrying in {mdelay} seconds... ({max_retries - mtries}/{max_retries})")
                    time.sleep(mdelay)
                    mdelay *= backoff
            return func(*args, **kwargs)
        return wrapper
    return decorator

def handle_api_error(func):
    """
    Decorator to handle API errors.
    
    Args:
        func (function): Function to decorate.
        
    Returns:
        function: Decorated function.
    """
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            logger.error(f"API error in {func.__name__}: {str(e)}")
            # Return a default value based on the function's expected return type
            # This is a simple approach; in a real system, you might want to be more specific
            return None
    return wrapper

def safe_execute(default_return=None, log_exception=True):
    """
    Decorator to safely execute a function and return a default value on error.
    
    Args:
        default_return: Default value to return on error.
        log_exception (bool): Whether to log the exception.
        
    Returns:
        function: Decorated function.
    """
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                if log_exception:
                    logger.error(f"Error in {func.__name__}: {str(e)}")
                return default_return
        return wrapper
    return decorator
