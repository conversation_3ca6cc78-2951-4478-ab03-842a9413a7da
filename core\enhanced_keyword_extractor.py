"""
增强关键词提取器
实现智能化的关键词提取和分类系统
"""

import os
import re
import json
import jieba
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from collections import Counter
import logging

from utils.logger import logger

class EnhancedKeywordExtractor:
    """增强关键词提取器类"""
    
    def __init__(self):
        """初始化关键词提取器"""
        
        # 关键词分类体系
        self.keyword_categories = {
            "政策类": {
                "keywords": ["货币政策", "财政政策", "监管政策", "产业政策", "税收政策", "金融政策", 
                           "央行", "降准", "降息", "加息", "QE", "量化宽松", "紧缩", "宽松"],
                "weight": 2.0
            },
            "行业类": {
                "keywords": ["银行", "保险", "证券", "房地产", "医药", "科技", "新能源", "消费", 
                           "制造业", "互联网", "电商", "汽车", "钢铁", "煤炭", "有色", "化工"],
                "weight": 1.5
            },
            "市场类": {
                "keywords": ["牛市", "熊市", "震荡", "突破", "回调", "反弹", "上涨", "下跌", 
                           "涨停", "跌停", "放量", "缩量", "成交量", "换手率"],
                "weight": 1.3
            },
            "情绪类": {
                "keywords": ["利好", "利空", "中性", "乐观", "悲观", "谨慎", "看多", "看空", 
                           "买入", "卖出", "持有", "观望", "风险", "机会"],
                "weight": 1.8
            },
            "事件类": {
                "keywords": ["并购", "重组", "IPO", "分红", "增持", "减持", "回购", "定增", 
                           "停牌", "复牌", "退市", "ST", "摘帽", "业绩", "财报"],
                "weight": 1.6
            }
        }
        
        # 停用词列表
        self.stop_words = {
            "的", "了", "在", "是", "我", "有", "和", "就", "不", "人", "都", "一", "一个", 
            "上", "也", "很", "到", "说", "要", "去", "你", "会", "着", "没有", "看", "好", 
            "自己", "这", "那", "它", "他", "她", "们", "个", "来", "对", "时", "地", "等"
        }
        
        # 金融专用词典
        self.finance_dict = {
            "A股", "港股", "美股", "创业板", "科创板", "主板", "中小板", "新三板",
            "沪深300", "上证50", "中证500", "创业板指", "深证成指",
            "北向资金", "南向资金", "外资", "机构", "散户", "游资", "主力",
            "PE", "PB", "ROE", "ROA", "毛利率", "净利率", "负债率",
            "涨幅", "跌幅", "振幅", "换手", "流通", "总市值", "流通市值"
        }
        
        # 加载自定义词典
        self._load_custom_dict()
        
        logger.info("增强关键词提取器初始化完成")
    
    def _load_custom_dict(self):
        """加载自定义词典"""
        try:
            # 添加金融专用词到jieba词典
            for word in self.finance_dict:
                jieba.add_word(word)
            
            # 添加分类关键词到jieba词典
            for category, data in self.keyword_categories.items():
                for keyword in data["keywords"]:
                    jieba.add_word(keyword)
            
            logger.info("自定义词典加载完成")
        except Exception as e:
            logger.error(f"加载自定义词典失败: {str(e)}")
    
    def extract_keywords(self, text: str, title: str = "", 
                        publish_date: str = None, max_keywords: int = 20) -> List[Dict[str, Any]]:
        """
        提取关键词
        
        Args:
            text: 文本内容
            title: 标题
            publish_date: 发布日期
            max_keywords: 最大关键词数量
            
        Returns:
            List[Dict]: 关键词列表，包含词语、分数、类别等信息
        """
        try:
            # 预处理文本
            processed_text = self._preprocess_text(text)
            processed_title = self._preprocess_text(title) if title else ""
            
            # 分词
            words = list(jieba.cut(processed_text))
            title_words = list(jieba.cut(processed_title)) if processed_title else []
            
            # 过滤停用词和短词
            filtered_words = [word for word in words 
                            if len(word) >= 2 and word not in self.stop_words]
            filtered_title_words = [word for word in title_words 
                                  if len(word) >= 2 and word not in self.stop_words]
            
            # 计算词频
            word_freq = Counter(filtered_words)
            title_word_freq = Counter(filtered_title_words)
            
            # 计算TF-IDF和综合分数
            keywords = []
            for word, freq in word_freq.items():
                # 基础TF分数
                tf_score = freq / len(filtered_words)
                
                # 类别权重
                category, category_weight = self._get_word_category(word)
                
                # 位置权重（标题中的词权重更高）
                position_weight = 2.0 if word in title_word_freq else 1.0
                
                # 时效性权重
                time_weight = self._calculate_time_weight(publish_date)
                
                # 综合分数
                final_score = tf_score * category_weight * position_weight * time_weight
                
                keywords.append({
                    'word': word,
                    'frequency': freq,
                    'tf_score': tf_score,
                    'category': category,
                    'category_weight': category_weight,
                    'position_weight': position_weight,
                    'time_weight': time_weight,
                    'final_score': final_score,
                    'in_title': word in title_word_freq
                })
            
            # 按分数排序并返回前N个
            keywords.sort(key=lambda x: x['final_score'], reverse=True)
            return keywords[:max_keywords]
            
        except Exception as e:
            logger.error(f"关键词提取失败: {str(e)}")
            return []
    
    def _preprocess_text(self, text: str) -> str:
        """预处理文本"""
        if not text:
            return ""
        
        # 去除HTML标签
        text = re.sub(r'<[^>]+>', '', text)
        
        # 去除特殊字符，保留中文、英文、数字
        text = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9\s]', ' ', text)
        
        # 去除多余空格
        text = re.sub(r'\s+', ' ', text).strip()
        
        return text
    
    def _get_word_category(self, word: str) -> Tuple[str, float]:
        """获取词语类别和权重"""
        for category, data in self.keyword_categories.items():
            if word in data["keywords"]:
                return category, data["weight"]
        
        # 如果不在预定义类别中，检查是否为金融词汇
        if word in self.finance_dict:
            return "金融类", 1.4
        
        return "其他", 1.0
    
    def _calculate_time_weight(self, publish_date: str) -> float:
        """计算时效性权重"""
        if not publish_date:
            return 1.0
        
        try:
            # 解析日期
            if isinstance(publish_date, str):
                pub_date = datetime.strptime(publish_date[:10], '%Y-%m-%d')
            else:
                pub_date = publish_date
            
            # 计算天数差
            days_diff = (datetime.now() - pub_date).days
            
            # 时效性权重：越新的内容权重越高
            if days_diff <= 1:
                return 2.0  # 当天或昨天
            elif days_diff <= 7:
                return 1.5  # 一周内
            elif days_diff <= 30:
                return 1.2  # 一月内
            else:
                return 1.0  # 一月以上
                
        except Exception as e:
            logger.warning(f"时效性权重计算失败: {str(e)}")
            return 1.0
    
    def categorize_keywords(self, keywords: List[Dict[str, Any]]) -> Dict[str, List[Dict[str, Any]]]:
        """按类别分组关键词"""
        categorized = {}
        
        for keyword in keywords:
            category = keyword.get('category', '其他')
            if category not in categorized:
                categorized[category] = []
            categorized[category].append(keyword)
        
        return categorized
    
    def get_keyword_summary(self, keywords: List[Dict[str, Any]]) -> Dict[str, Any]:
        """获取关键词摘要统计"""
        if not keywords:
            return {}
        
        categorized = self.categorize_keywords(keywords)
        
        summary = {
            'total_keywords': len(keywords),
            'categories': {},
            'top_keywords': keywords[:5],
            'avg_score': np.mean([kw['final_score'] for kw in keywords]),
            'max_score': max([kw['final_score'] for kw in keywords]),
            'title_keywords': [kw for kw in keywords if kw.get('in_title', False)]
        }
        
        for category, kws in categorized.items():
            summary['categories'][category] = {
                'count': len(kws),
                'avg_score': np.mean([kw['final_score'] for kw in kws]),
                'top_keywords': kws[:3]
            }
        
        return summary
    
    def deduplicate_keywords(self, keywords: List[Dict[str, Any]], 
                           similarity_threshold: float = 0.8) -> List[Dict[str, Any]]:
        """去重相似关键词"""
        if not keywords:
            return keywords
        
        # 简单的基于编辑距离的去重
        deduplicated = []
        
        for keyword in keywords:
            is_duplicate = False
            word = keyword['word']
            
            for existing in deduplicated:
                existing_word = existing['word']
                
                # 计算相似度（简单的字符重叠度）
                similarity = self._calculate_similarity(word, existing_word)
                
                if similarity > similarity_threshold:
                    # 如果相似，保留分数更高的
                    if keyword['final_score'] > existing['final_score']:
                        deduplicated.remove(existing)
                        deduplicated.append(keyword)
                    is_duplicate = True
                    break
            
            if not is_duplicate:
                deduplicated.append(keyword)
        
        return deduplicated
    
    def _calculate_similarity(self, word1: str, word2: str) -> float:
        """计算两个词的相似度"""
        if word1 == word2:
            return 1.0
        
        # 简单的字符重叠度计算
        set1 = set(word1)
        set2 = set(word2)
        
        intersection = len(set1 & set2)
        union = len(set1 | set2)
        
        return intersection / union if union > 0 else 0.0
