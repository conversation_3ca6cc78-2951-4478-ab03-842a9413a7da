"""
多因子耦合分析引擎
分析政策、新闻、资金流、波动率之间的复杂关联关系
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Any, Tuple, Optional
from datetime import datetime, timedelta
import logging
from scipy import stats
from scipy.stats import pearsonr, spearmanr
from sklearn.preprocessing import StandardScaler
from sklearn.decomposition import PCA
from sklearn.cluster import KMeans
from sklearn.ensemble import RandomForestRegressor
import networkx as nx

from utils.logger import logger

class MultiFactorCouplingEngine:
    """多因子耦合分析引擎"""
    
    def __init__(self):
        self.scaler = StandardScaler()
        self.pca = PCA(n_components=0.95)  # 保留95%的方差
        self.coupling_network = nx.Graph()
        self.factor_weights = {}
        self.coupling_history = []
        
        logger.info("多因子耦合分析引擎初始化完成")
    
    def analyze_coupling(self, data_streams: Dict[str, pd.DataFrame]) -> Dict[str, Any]:
        """
        分析多因子耦合关系
        
        Args:
            data_streams: 数据流字典，包含policy, news, fund_flow, volatility等
            
        Returns:
            耦合分析结果
        """
        logger.info("开始多因子耦合分析...")
        
        # 1. 数据预处理和特征提取
        features = self.extract_features(data_streams)
        
        # 2. 相关性分析
        correlation_analysis = self.analyze_correlations(features)
        
        # 3. 因果关系分析
        causality_analysis = self.analyze_causality(features)
        
        # 4. 网络分析
        network_analysis = self.build_coupling_network(features, correlation_analysis)
        
        # 5. 动态耦合分析
        dynamic_analysis = self.analyze_dynamic_coupling(features)
        
        # 6. 预测模型
        prediction_model = self.build_prediction_model(features)
        
        # 7. 综合评分
        coupling_score = self.calculate_coupling_score(
            correlation_analysis, causality_analysis, network_analysis
        )
        
        result = {
            'timestamp': datetime.now().isoformat(),
            'features_summary': self.summarize_features(features),
            'correlation_analysis': correlation_analysis,
            'causality_analysis': causality_analysis,
            'network_analysis': network_analysis,
            'dynamic_analysis': dynamic_analysis,
            'prediction_model': prediction_model,
            'coupling_score': coupling_score,
            'insights': self.generate_insights(correlation_analysis, causality_analysis, network_analysis)
        }
        
        # 保存历史记录
        self.coupling_history.append(result)
        
        logger.info("多因子耦合分析完成")
        return result
    
    def extract_features(self, data_streams: Dict[str, pd.DataFrame]) -> pd.DataFrame:
        """
        从各数据流中提取特征
        
        Args:
            data_streams: 原始数据流
            
        Returns:
            特征矩阵
        """
        features = {}
        
        # 政策特征
        if 'policy' in data_streams and not data_streams['policy'].empty:
            policy_features = self.extract_policy_features(data_streams['policy'])
            features.update(policy_features)
        
        # 新闻特征
        if 'news' in data_streams and not data_streams['news'].empty:
            news_features = self.extract_news_features(data_streams['news'])
            features.update(news_features)
        
        # 资金流特征
        if 'fund_flow' in data_streams and not data_streams['fund_flow'].empty:
            fund_flow_features = self.extract_fund_flow_features(data_streams['fund_flow'])
            features.update(fund_flow_features)
        
        # 波动率特征
        if 'volatility' in data_streams and not data_streams['volatility'].empty:
            volatility_features = self.extract_volatility_features(data_streams['volatility'])
            features.update(volatility_features)
        
        # 转换为DataFrame
        if features:
            # 确保所有特征序列长度一致
            min_length = min(len(v) for v in features.values() if hasattr(v, '__len__'))
            for key in features:
                if hasattr(features[key], '__len__') and len(features[key]) > min_length:
                    features[key] = features[key][:min_length]
                elif not hasattr(features[key], '__len__'):
                    features[key] = [features[key]] * min_length
            
            features_df = pd.DataFrame(features)
            features_df.index = pd.date_range(end=datetime.now(), periods=len(features_df), freq='H')
            return features_df
        else:
            return pd.DataFrame()
    
    def extract_policy_features(self, policy_data: pd.DataFrame) -> Dict[str, List[float]]:
        """提取政策特征"""
        features = {}
        
        # 政策发布频率
        features['policy_frequency'] = [len(policy_data)]
        
        # 政策重要性评分（基于关键词）
        important_keywords = ['降准', '降息', '减税', '刺激', '支持', '鼓励']
        policy_importance = 0
        for _, row in policy_data.iterrows():
            text = str(row.get('title', '')) + str(row.get('content', ''))
            importance = sum(1 for keyword in important_keywords if keyword in text)
            policy_importance += importance
        
        features['policy_importance'] = [policy_importance / max(len(policy_data), 1)]
        
        # 政策情绪倾向
        positive_keywords = ['支持', '鼓励', '促进', '优化', '便利']
        negative_keywords = ['限制', '禁止', '严格', '收紧', '监管']
        
        positive_count = sum(1 for _, row in policy_data.iterrows() 
                           for keyword in positive_keywords 
                           if keyword in str(row.get('title', '')) + str(row.get('content', '')))
        negative_count = sum(1 for _, row in policy_data.iterrows() 
                           for keyword in negative_keywords 
                           if keyword in str(row.get('title', '')) + str(row.get('content', '')))
        
        features['policy_sentiment'] = [(positive_count - negative_count) / max(len(policy_data), 1)]
        
        return features
    
    def extract_news_features(self, news_data: pd.DataFrame) -> Dict[str, List[float]]:
        """提取新闻特征"""
        features = {}
        
        # 新闻数量
        features['news_volume'] = [len(news_data)]
        
        # 新闻情绪（如果有sentiment字段）
        if 'sentiment' in news_data.columns:
            sentiment_scores = []
            for sentiment in news_data['sentiment']:
                if sentiment == 'positive':
                    sentiment_scores.append(1)
                elif sentiment == 'negative':
                    sentiment_scores.append(-1)
                else:
                    sentiment_scores.append(0)
            features['news_sentiment'] = [np.mean(sentiment_scores) if sentiment_scores else 0]
        else:
            features['news_sentiment'] = [0]
        
        # 新闻热度（基于标题长度和关键词）
        hot_keywords = ['涨停', '跌停', '暴涨', '暴跌', '突破', '创新高', '创新低']
        heat_score = 0
        for _, row in news_data.iterrows():
            title = str(row.get('title', ''))
            heat_score += len(title) / 50  # 标题长度权重
            heat_score += sum(2 for keyword in hot_keywords if keyword in title)  # 关键词权重
        
        features['news_heat'] = [heat_score / max(len(news_data), 1)]
        
        return features
    
    def extract_fund_flow_features(self, fund_flow_data: pd.DataFrame) -> Dict[str, List[float]]:
        """提取资金流特征"""
        features = {}
        
        # 北向资金净流入
        if 'net_inflow' in fund_flow_data.columns:
            features['northbound_flow'] = [fund_flow_data['net_inflow'].sum()]
        else:
            features['northbound_flow'] = [0]
        
        # 主力资金净流入
        if 'main_net_inflow' in fund_flow_data.columns:
            features['main_fund_flow'] = [fund_flow_data['main_net_inflow'].sum()]
        else:
            features['main_fund_flow'] = [0]
        
        # 资金流向集中度
        if 'net_inflow' in fund_flow_data.columns:
            flow_std = fund_flow_data['net_inflow'].std()
            features['fund_concentration'] = [flow_std if not pd.isna(flow_std) else 0]
        else:
            features['fund_concentration'] = [0]
        
        # 资金流向变化率
        if 'net_inflow' in fund_flow_data.columns and len(fund_flow_data) > 1:
            flow_change = fund_flow_data['net_inflow'].pct_change().mean()
            features['fund_change_rate'] = [flow_change if not pd.isna(flow_change) else 0]
        else:
            features['fund_change_rate'] = [0]
        
        return features
    
    def extract_volatility_features(self, volatility_data: pd.DataFrame) -> Dict[str, List[float]]:
        """提取波动率特征"""
        features = {}
        
        # 平均波动率
        if 'volatility' in volatility_data.columns:
            features['avg_volatility'] = [volatility_data['volatility'].mean()]
        else:
            features['avg_volatility'] = [0]
        
        # 波动率变化
        if 'volatility' in volatility_data.columns and len(volatility_data) > 1:
            vol_change = volatility_data['volatility'].pct_change().mean()
            features['volatility_change'] = [vol_change if not pd.isna(vol_change) else 0]
        else:
            features['volatility_change'] = [0]
        
        # 波动率峰值
        if 'volatility' in volatility_data.columns:
            features['volatility_peak'] = [volatility_data['volatility'].max()]
        else:
            features['volatility_peak'] = [0]
        
        return features
    
    def analyze_correlations(self, features: pd.DataFrame) -> Dict[str, Any]:
        """分析特征间的相关性"""
        if features.empty or len(features) < 2:
            return {'correlation_matrix': {}, 'significant_correlations': []}
        
        # 计算相关性矩阵
        correlation_matrix = features.corr()
        
        # 找出显著相关性
        significant_correlations = []
        for i in range(len(correlation_matrix.columns)):
            for j in range(i+1, len(correlation_matrix.columns)):
                corr_value = correlation_matrix.iloc[i, j]
                if abs(corr_value) > 0.5 and not pd.isna(corr_value):
                    significant_correlations.append({
                        'factor1': correlation_matrix.columns[i],
                        'factor2': correlation_matrix.columns[j],
                        'correlation': corr_value,
                        'strength': 'strong' if abs(corr_value) > 0.7 else 'moderate'
                    })
        
        return {
            'correlation_matrix': correlation_matrix.to_dict(),
            'significant_correlations': significant_correlations
        }
    
    def analyze_causality(self, features: pd.DataFrame) -> Dict[str, Any]:
        """分析因果关系（简化版格兰杰因果检验）"""
        causality_results = []
        
        if features.empty or len(features) < 10:
            return {'causality_pairs': causality_results}
        
        # 对每对特征进行简化的因果分析
        feature_names = features.columns.tolist()
        
        for i, factor1 in enumerate(feature_names):
            for j, factor2 in enumerate(feature_names):
                if i != j:
                    try:
                        # 简化的因果检验：检查滞后相关性
                        series1 = features[factor1].dropna()
                        series2 = features[factor2].dropna()
                        
                        if len(series1) > 5 and len(series2) > 5:
                            # 计算滞后1期的相关性
                            if len(series1) > 1 and len(series2) > 1:
                                lag_corr = pearsonr(series1[:-1], series2[1:])[0]
                                
                                if abs(lag_corr) > 0.3 and not pd.isna(lag_corr):
                                    causality_results.append({
                                        'cause': factor1,
                                        'effect': factor2,
                                        'lag_correlation': lag_corr,
                                        'strength': 'strong' if abs(lag_corr) > 0.5 else 'weak'
                                    })
                    except Exception as e:
                        logger.warning(f"因果分析失败: {factor1} -> {factor2}, 错误: {str(e)}")
        
        return {'causality_pairs': causality_results}
    
    def build_coupling_network(self, features: pd.DataFrame, correlation_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """构建耦合网络"""
        # 清空之前的网络
        self.coupling_network.clear()
        
        # 添加节点
        for feature in features.columns:
            self.coupling_network.add_node(feature)
        
        # 添加边（基于显著相关性）
        for corr in correlation_analysis.get('significant_correlations', []):
            self.coupling_network.add_edge(
                corr['factor1'], 
                corr['factor2'], 
                weight=abs(corr['correlation'])
            )
        
        # 网络分析
        network_metrics = {}
        
        if len(self.coupling_network.nodes()) > 0:
            # 度中心性
            degree_centrality = nx.degree_centrality(self.coupling_network)
            
            # 介数中心性
            if len(self.coupling_network.nodes()) > 2:
                betweenness_centrality = nx.betweenness_centrality(self.coupling_network)
            else:
                betweenness_centrality = {node: 0 for node in self.coupling_network.nodes()}
            
            # 聚类系数
            clustering_coefficient = nx.clustering(self.coupling_network)
            
            network_metrics = {
                'degree_centrality': degree_centrality,
                'betweenness_centrality': betweenness_centrality,
                'clustering_coefficient': clustering_coefficient,
                'network_density': nx.density(self.coupling_network),
                'connected_components': nx.number_connected_components(self.coupling_network)
            }
        
        return network_metrics
    
    def analyze_dynamic_coupling(self, features: pd.DataFrame) -> Dict[str, Any]:
        """分析动态耦合关系"""
        if features.empty or len(features) < 5:
            return {'dynamic_patterns': [], 'coupling_evolution': {}}
        
        # 滑动窗口分析
        window_size = min(5, len(features) // 2)
        dynamic_patterns = []
        
        for i in range(len(features) - window_size + 1):
            window_data = features.iloc[i:i+window_size]
            window_corr = window_data.corr()
            
            # 计算窗口内的平均相关性强度
            corr_values = []
            for j in range(len(window_corr.columns)):
                for k in range(j+1, len(window_corr.columns)):
                    corr_val = window_corr.iloc[j, k]
                    if not pd.isna(corr_val):
                        corr_values.append(abs(corr_val))
            
            avg_coupling_strength = np.mean(corr_values) if corr_values else 0
            
            dynamic_patterns.append({
                'window_start': i,
                'window_end': i + window_size - 1,
                'coupling_strength': avg_coupling_strength,
                'timestamp': features.index[i + window_size - 1] if hasattr(features.index, '__getitem__') else i
            })
        
        # 耦合演化趋势
        coupling_evolution = {}
        if dynamic_patterns:
            strengths = [p['coupling_strength'] for p in dynamic_patterns]
            coupling_evolution = {
                'trend': 'increasing' if strengths[-1] > strengths[0] else 'decreasing',
                'volatility': np.std(strengths),
                'max_coupling': max(strengths),
                'min_coupling': min(strengths)
            }
        
        return {
            'dynamic_patterns': dynamic_patterns,
            'coupling_evolution': coupling_evolution
        }
    
    def build_prediction_model(self, features: pd.DataFrame) -> Dict[str, Any]:
        """构建预测模型"""
        if features.empty or len(features) < 5:
            return {'model_available': False, 'feature_importance': {}}
        
        try:
            # 准备数据
            X = features.iloc[:-1]  # 特征
            y = features.iloc[1:].mean(axis=1)  # 目标：下一期的平均特征值
            
            if len(X) < 3:
                return {'model_available': False, 'feature_importance': {}}
            
            # 训练随机森林模型
            rf_model = RandomForestRegressor(n_estimators=50, random_state=42)
            rf_model.fit(X, y)
            
            # 特征重要性
            feature_importance = dict(zip(X.columns, rf_model.feature_importances_))
            
            # 模型评分
            score = rf_model.score(X, y)
            
            return {
                'model_available': True,
                'feature_importance': feature_importance,
                'model_score': score,
                'most_important_factor': max(feature_importance.keys(), key=feature_importance.get)
            }
            
        except Exception as e:
            logger.error(f"预测模型构建失败: {str(e)}")
            return {'model_available': False, 'feature_importance': {}}
    
    def calculate_coupling_score(self, correlation_analysis: Dict[str, Any], 
                                causality_analysis: Dict[str, Any], 
                                network_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """计算综合耦合评分"""
        
        # 相关性评分
        corr_score = len(correlation_analysis.get('significant_correlations', [])) * 10
        
        # 因果关系评分
        causality_score = len(causality_analysis.get('causality_pairs', [])) * 15
        
        # 网络复杂度评分
        network_score = 0
        if network_analysis:
            density = network_analysis.get('network_density', 0)
            components = network_analysis.get('connected_components', 1)
            network_score = density * 50 + (1 / components) * 20
        
        # 综合评分
        total_score = corr_score + causality_score + network_score
        
        # 评级
        if total_score >= 80:
            rating = 'strong'
        elif total_score >= 40:
            rating = 'moderate'
        else:
            rating = 'weak'
        
        return {
            'total_score': total_score,
            'correlation_score': corr_score,
            'causality_score': causality_score,
            'network_score': network_score,
            'rating': rating
        }
    
    def generate_insights(self, correlation_analysis: Dict[str, Any], 
                         causality_analysis: Dict[str, Any], 
                         network_analysis: Dict[str, Any]) -> List[str]:
        """生成洞察和建议"""
        insights = []
        
        # 相关性洞察
        strong_correlations = [c for c in correlation_analysis.get('significant_correlations', []) 
                             if c.get('strength') == 'strong']
        if strong_correlations:
            insights.append(f"发现{len(strong_correlations)}对强相关因子，表明市场因子间存在显著联动")
        
        # 因果关系洞察
        strong_causality = [c for c in causality_analysis.get('causality_pairs', []) 
                          if c.get('strength') == 'strong']
        if strong_causality:
            insights.append(f"识别出{len(strong_causality)}个强因果关系，可用于预测分析")
        
        # 网络结构洞察
        if network_analysis:
            density = network_analysis.get('network_density', 0)
            if density > 0.5:
                insights.append("因子网络密度较高，市场联动性强")
            elif density < 0.2:
                insights.append("因子网络密度较低，各因子相对独立")
        
        # 中心性洞察
        if network_analysis and 'degree_centrality' in network_analysis:
            centrality = network_analysis['degree_centrality']
            if centrality:
                most_central = max(centrality.keys(), key=centrality.get)
                insights.append(f"'{most_central}'是网络中心节点，对其他因子影响最大")
        
        if not insights:
            insights.append("当前数据显示因子间耦合关系较弱，建议增加观察期")
        
        return insights
    
    def summarize_features(self, features: pd.DataFrame) -> Dict[str, Any]:
        """总结特征统计信息"""
        if features.empty:
            return {}
        
        summary = {}
        for column in features.columns:
            series = features[column].dropna()
            if len(series) > 0:
                summary[column] = {
                    'mean': float(series.mean()),
                    'std': float(series.std()),
                    'min': float(series.min()),
                    'max': float(series.max()),
                    'count': len(series)
                }
        
        return summary
