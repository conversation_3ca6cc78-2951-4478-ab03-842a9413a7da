"""
智能FinBERT分析引擎
集成多个预训练模型，提供高质量的金融文本分析
"""

import os
import json
import torch
import numpy as np
import pandas as pd
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
import logging
from transformers import (
    AutoTokenizer, 
    AutoModelForSequenceClassification,
    pipeline
)

from utils.logger import logger

class IntelligentFinBERTEngine:
    """智能FinBERT分析引擎"""
    
    def __init__(self, config_path: str = "config/finbert_config.json"):
        self.config_path = config_path
        self.config = self.load_config()
        self.models = {}
        self.tokenizers = {}
        self.pipelines = {}
        self.device = self.get_device()
        
        # 初始化模型
        self.initialize_models()
        
        logger.info(f"智能FinBERT引擎初始化完成，使用设备: {self.device}")
    
    def load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            return config
        except FileNotFoundError:
            logger.warning(f"配置文件不存在: {self.config_path}，使用默认配置")
            return self.get_default_config()
        except Exception as e:
            logger.error(f"加载配置文件失败: {str(e)}")
            return self.get_default_config()
    
    def get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            "model_configs": {
                "finbert": {
                    "model_name": "ProsusAI/finbert",
                    "local_path": "models/finbert",
                    "task": "sentiment-analysis",
                    "labels": ["negative", "neutral", "positive"]
                }
            },
            "default_model": "finbert",
            "max_length": 512,
            "batch_size": 16
        }
    
    def get_device(self) -> str:
        """获取计算设备"""
        if torch.cuda.is_available():
            return "cuda"
        elif torch.backends.mps.is_available():  # Apple Silicon
            return "mps"
        else:
            return "cpu"
    
    def initialize_models(self):
        """初始化所有模型"""
        for model_name, model_config in self.config["model_configs"].items():
            try:
                self.load_model(model_name, model_config)
                logger.info(f"模型加载成功: {model_name}")
            except Exception as e:
                logger.error(f"模型加载失败: {model_name}, 错误: {str(e)}")
    
    def load_model(self, model_name: str, model_config: Dict[str, Any]):
        """加载单个模型"""
        try:
            # 尝试从本地路径加载
            local_path = model_config.get("local_path")
            model_path = model_config["model_name"]
            
            if local_path and os.path.exists(local_path):
                model_path = local_path
                logger.info(f"从本地加载模型: {model_path}")
            else:
                logger.info(f"从HuggingFace加载模型: {model_path}")
            
            # 加载tokenizer和模型
            tokenizer = AutoTokenizer.from_pretrained(model_path)
            model = AutoModelForSequenceClassification.from_pretrained(model_path)
            
            # 移动到指定设备
            model.to(self.device)
            model.eval()
            
            # 创建pipeline
            pipe = pipeline(
                model_config["task"],
                model=model,
                tokenizer=tokenizer,
                device=0 if self.device == "cuda" else -1,
                return_all_scores=True
            )
            
            # 保存到实例变量
            self.tokenizers[model_name] = tokenizer
            self.models[model_name] = model
            self.pipelines[model_name] = pipe
            
        except Exception as e:
            logger.error(f"加载模型失败: {model_name}, 错误: {str(e)}")
            raise
    
    def analyze_sentiment(self, texts: List[str], model_name: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        分析文本情感
        
        Args:
            texts: 文本列表
            model_name: 指定模型名称，默认使用配置中的默认模型
            
        Returns:
            情感分析结果列表
        """
        if not texts:
            return []
        
        # 选择模型
        if model_name is None:
            model_name = self.config.get("default_model", "finbert")
        
        if model_name not in self.pipelines:
            logger.error(f"模型不存在: {model_name}")
            return []
        
        try:
            pipeline = self.pipelines[model_name]
            
            # 批量处理
            results = []
            batch_size = self.config.get("batch_size", 16)
            
            for i in range(0, len(texts), batch_size):
                batch_texts = texts[i:i + batch_size]
                batch_results = pipeline(batch_texts)
                
                # 处理结果
                for j, text_results in enumerate(batch_results):
                    text = batch_texts[j]
                    
                    # 提取最高分数的标签
                    best_result = max(text_results, key=lambda x: x['score'])
                    
                    # 构建结果
                    result = {
                        'text': text,
                        'model': model_name,
                        'sentiment': best_result['label'],
                        'confidence': best_result['score'],
                        'all_scores': {item['label']: item['score'] for item in text_results},
                        'timestamp': datetime.now().isoformat()
                    }
                    
                    results.append(result)
            
            logger.info(f"情感分析完成: {len(texts)}条文本，模型: {model_name}")
            return results
            
        except Exception as e:
            logger.error(f"情感分析失败: {str(e)}")
            return []
    
    def analyze_financial_impact(self, texts: List[str]) -> List[Dict[str, Any]]:
        """
        分析金融影响
        
        Args:
            texts: 文本列表
            
        Returns:
            金融影响分析结果
        """
        results = []
        
        # 使用多个模型进行分析
        available_models = list(self.pipelines.keys())
        
        for text in texts:
            text_result = {
                'text': text,
                'models_analysis': {},
                'ensemble_result': {},
                'financial_indicators': {},
                'timestamp': datetime.now().isoformat()
            }
            
            # 多模型分析
            model_predictions = []
            for model_name in available_models:
                try:
                    sentiment_result = self.analyze_sentiment([text], model_name)[0]
                    text_result['models_analysis'][model_name] = sentiment_result
                    model_predictions.append(sentiment_result)
                except Exception as e:
                    logger.warning(f"模型 {model_name} 分析失败: {str(e)}")
            
            # 集成结果
            if model_predictions:
                ensemble_result = self.ensemble_predictions(model_predictions)
                text_result['ensemble_result'] = ensemble_result
                
                # 金融指标分析
                financial_indicators = self.extract_financial_indicators(text, ensemble_result)
                text_result['financial_indicators'] = financial_indicators
            
            results.append(text_result)
        
        return results
    
    def ensemble_predictions(self, predictions: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        集成多个模型的预测结果
        
        Args:
            predictions: 多个模型的预测结果
            
        Returns:
            集成后的结果
        """
        if not predictions:
            return {}
        
        # 收集所有标签的分数
        label_scores = {}
        
        for pred in predictions:
            all_scores = pred.get('all_scores', {})
            for label, score in all_scores.items():
                if label not in label_scores:
                    label_scores[label] = []
                label_scores[label].append(score)
        
        # 计算平均分数
        avg_scores = {}
        for label, scores in label_scores.items():
            avg_scores[label] = np.mean(scores)
        
        # 找到最高分数的标签
        best_label = max(avg_scores.keys(), key=lambda x: avg_scores[x])
        best_score = avg_scores[best_label]
        
        # 计算一致性（标准差的倒数）
        consistency_scores = {}
        for label, scores in label_scores.items():
            if len(scores) > 1:
                consistency_scores[label] = 1.0 / (np.std(scores) + 1e-6)
            else:
                consistency_scores[label] = 1.0
        
        return {
            'sentiment': best_label,
            'confidence': best_score,
            'avg_scores': avg_scores,
            'consistency': consistency_scores.get(best_label, 0.0),
            'model_count': len(predictions)
        }
    
    def extract_financial_indicators(self, text: str, sentiment_result: Dict[str, Any]) -> Dict[str, Any]:
        """
        提取金融指标
        
        Args:
            text: 原始文本
            sentiment_result: 情感分析结果
            
        Returns:
            金融指标
        """
        indicators = {
            'market_impact': 'neutral',
            'urgency': 'low',
            'sector_relevance': [],
            'key_entities': [],
            'risk_level': 'low'
        }
        
        sentiment = sentiment_result.get('sentiment', 'neutral').lower()
        confidence = sentiment_result.get('confidence', 0.0)
        
        # 市场影响评估
        if confidence > 0.8:
            if sentiment in ['positive', 'pos']:
                indicators['market_impact'] = 'bullish'
            elif sentiment in ['negative', 'neg']:
                indicators['market_impact'] = 'bearish'
        
        # 紧急程度评估
        urgent_keywords = ['突发', '紧急', '重大', '暴跌', '暴涨', '停牌', '重组']
        if any(keyword in text for keyword in urgent_keywords):
            indicators['urgency'] = 'high'
        elif confidence > 0.7:
            indicators['urgency'] = 'medium'
        
        # 风险等级评估
        risk_keywords = ['风险', '亏损', '下跌', '危机', '警告']
        if any(keyword in text for keyword in risk_keywords) and sentiment in ['negative', 'neg']:
            indicators['risk_level'] = 'high'
        elif confidence > 0.6:
            indicators['risk_level'] = 'medium'
        
        # 行业相关性（简化版）
        sector_keywords = {
            '银行': ['银行', '金融', '贷款', '存款'],
            '科技': ['科技', '互联网', '人工智能', '芯片'],
            '医药': ['医药', '生物', '疫苗', '药品'],
            '地产': ['房地产', '地产', '住房', '楼市'],
            '能源': ['石油', '天然气', '煤炭', '新能源']
        }
        
        for sector, keywords in sector_keywords.items():
            if any(keyword in text for keyword in keywords):
                indicators['sector_relevance'].append(sector)
        
        return indicators
    
    def batch_analyze_news(self, news_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        批量分析新闻数据
        
        Args:
            news_data: 新闻数据列表，每个元素包含title, content等字段
            
        Returns:
            分析结果列表
        """
        results = []
        
        for news_item in news_data:
            try:
                # 提取文本
                title = news_item.get('title', '')
                content = news_item.get('content', '')
                text = f"{title}. {content}".strip()
                
                if not text:
                    continue
                
                # 分析文本
                analysis_result = self.analyze_financial_impact([text])[0]
                
                # 添加原始新闻信息
                analysis_result.update({
                    'news_id': news_item.get('id'),
                    'news_title': title,
                    'news_content': content,
                    'news_time': news_item.get('time'),
                    'news_source': news_item.get('source')
                })
                
                results.append(analysis_result)
                
            except Exception as e:
                logger.error(f"新闻分析失败: {str(e)}")
                continue
        
        logger.info(f"批量新闻分析完成: {len(results)}/{len(news_data)}")
        return results
    
    def get_model_status(self) -> Dict[str, Any]:
        """获取模型状态"""
        status = {
            'device': self.device,
            'loaded_models': list(self.models.keys()),
            'available_pipelines': list(self.pipelines.keys()),
            'config': self.config
        }
        
        # 检查模型内存使用
        if self.device == 'cuda':
            try:
                status['gpu_memory'] = {
                    'allocated': torch.cuda.memory_allocated() / 1024**3,  # GB
                    'cached': torch.cuda.memory_reserved() / 1024**3  # GB
                }
            except:
                pass
        
        return status

# 全局实例
_finbert_engine = None

def get_finbert_engine() -> IntelligentFinBERTEngine:
    """获取全局FinBERT引擎实例"""
    global _finbert_engine
    if _finbert_engine is None:
        _finbert_engine = IntelligentFinBERTEngine()
    return _finbert_engine
