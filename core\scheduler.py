"""
中央调度器模块

负责系统各组件的任务调度、资源分配和状态监控
"""

import os
import time
import logging
import threading
import schedule
import queue
import signal
import sys
import json
from datetime import datetime, timedelta
from enum import Enum
from typing import Dict, List, Callable, Any, Optional, Union
import pandas as pd
import numpy as np

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/scheduler.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('central_scheduler')

class MarketState(Enum):
    """市场状态枚举"""
    PRE_MARKET = "pre_market"  # 开盘前
    MARKET_OPEN = "market_open"  # 交易中
    MARKET_CLOSE = "market_close"  # 收盘后
    WEEKEND = "weekend"  # 周末
    HOLIDAY = "holiday"  # 节假日

class TaskPriority(Enum):
    """任务优先级枚举"""
    HIGH = 0  # 高优先级
    MEDIUM = 1  # 中优先级
    LOW = 2  # 低优先级

class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = "pending"  # 等待执行
    RUNNING = "running"  # 执行中
    COMPLETED = "completed"  # 已完成
    FAILED = "failed"  # 执行失败
    CANCELLED = "cancelled"  # 已取消

class Task:
    """任务类"""

    def __init__(self,
                 task_id: str,
                 task_type: str,
                 module: str,
                 function: str,
                 params: Dict[str, Any] = None,
                 priority: TaskPriority = TaskPriority.MEDIUM,
                 schedule_time: Optional[datetime] = None,
                 timeout: int = 300):
        """
        初始化任务

        Args:
            task_id: 任务ID
            task_type: 任务类型
            module: 模块名称
            function: 函数名称
            params: 函数参数
            priority: 任务优先级
            schedule_time: 计划执行时间
            timeout: 超时时间(秒)
        """
        self.task_id = task_id
        self.task_type = task_type
        self.module = module
        self.function = function
        self.params = params or {}
        self.priority = priority
        self.schedule_time = schedule_time or datetime.now()
        self.timeout = timeout

        self.status = TaskStatus.PENDING
        self.created_at = datetime.now()
        self.started_at = None
        self.completed_at = None
        self.result = None
        self.error = None
        self.retry_count = 0
        self.max_retries = 3

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'task_id': self.task_id,
            'task_type': self.task_type,
            'module': self.module,
            'function': self.function,
            'params': self.params,
            'priority': self.priority.name,
            'schedule_time': self.schedule_time.isoformat() if self.schedule_time else None,
            'timeout': self.timeout,
            'status': self.status.name,
            'created_at': self.created_at.isoformat(),
            'started_at': self.started_at.isoformat() if self.started_at else None,
            'completed_at': self.completed_at.isoformat() if self.completed_at else None,
            'result': str(self.result) if self.result else None,
            'error': str(self.error) if self.error else None,
            'retry_count': self.retry_count,
            'max_retries': self.max_retries
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Task':
        """从字典创建任务"""
        task = cls(
            task_id=data['task_id'],
            task_type=data['task_type'],
            module=data['module'],
            function=data['function'],
            params=data['params'],
            priority=TaskPriority[data['priority']],
            schedule_time=datetime.fromisoformat(data['schedule_time']) if data['schedule_time'] else None,
            timeout=data['timeout']
        )
        task.status = TaskStatus[data['status']]
        task.created_at = datetime.fromisoformat(data['created_at'])
        task.started_at = datetime.fromisoformat(data['started_at']) if data['started_at'] else None
        task.completed_at = datetime.fromisoformat(data['completed_at']) if data['completed_at'] else None
        task.result = data['result']
        task.error = data['error']
        task.retry_count = data['retry_count']
        task.max_retries = data['max_retries']
        return task

class CentralScheduler:
    """中央调度器类"""

    def __init__(self, config_path: str = 'config/scheduler_config.json'):
        """
        初始化中央调度器

        Args:
            config_path: 配置文件路径
        """
        self.config_path = config_path
        self.config = self._load_config()

        # 创建必要的目录
        os.makedirs('logs', exist_ok=True)
        os.makedirs('data/scheduler', exist_ok=True)

        # 任务队列 (按优先级排序)
        self.task_queue = queue.PriorityQueue()

        # 任务字典 (用于快速查找)
        self.tasks = {}

        # 模块注册表
        self.modules = {}

        # 工作线程
        self.workers = []
        self.max_workers = self.config.get('max_workers', 5)

        # 运行状态
        self.is_running = False

        # 监控线程
        self.monitor_thread = None

        # 当前市场状态
        self.market_state = self._get_market_state()

        # 资源分配策略
        self.resource_allocation = self._get_resource_allocation()

        # 注册信号处理器
        self._register_signal_handlers()

        logger.info(f"中央调度器初始化完成，最大工作线程数: {self.max_workers}")

    def _load_config(self) -> Dict[str, Any]:
        """加载配置"""
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                # 默认配置
                default_config = {
                    'max_workers': 5,
                    'task_timeout': 300,
                    'monitor_interval': 60,
                    'market_hours': {
                        'open': '09:30',
                        'close': '15:00'
                    },
                    'resource_allocation': {
                        'pre_market': {
                            'news': 0.3,
                            'policy': 0.3,
                            'market_data': 0.3,
                            'analysis': 0.1
                        },
                        'market_open': {
                            'news': 0.2,
                            'policy': 0.1,
                            'market_data': 0.5,
                            'analysis': 0.2
                        },
                        'market_close': {
                            'news': 0.2,
                            'policy': 0.2,
                            'market_data': 0.2,
                            'analysis': 0.4
                        },
                        'weekend': {
                            'news': 0.3,
                            'policy': 0.3,
                            'market_data': 0.1,
                            'analysis': 0.3
                        },
                        'holiday': {
                            'news': 0.3,
                            'policy': 0.3,
                            'market_data': 0.1,
                            'analysis': 0.3
                        }
                    }
                }

                # 保存默认配置
                os.makedirs(os.path.dirname(self.config_path), exist_ok=True)
                with open(self.config_path, 'w', encoding='utf-8') as f:
                    json.dump(default_config, f, indent=4)

                return default_config
        except Exception as e:
            logger.error(f"加载配置失败: {str(e)}")
            return {
                'max_workers': 5,
                'task_timeout': 300,
                'monitor_interval': 60
            }

    def _register_signal_handlers(self):
        """注册信号处理器"""
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)

    def _signal_handler(self, sig, frame):
        """信号处理器"""
        logger.info(f"接收到信号 {sig}，正在优雅退出...")
        self.stop()
        sys.exit(0)

    def _get_market_state(self) -> MarketState:
        """获取当前市场状态"""
        now = datetime.now()
        weekday = now.weekday()

        # 周末
        if weekday >= 5:
            return MarketState.WEEKEND

        # TODO: 添加节假日检查

        # 解析市场开盘和收盘时间
        market_open = datetime.strptime(self.config['market_hours']['open'], '%H:%M').time()
        market_close = datetime.strptime(self.config['market_hours']['close'], '%H:%M').time()

        current_time = now.time()

        if current_time < market_open:
            return MarketState.PRE_MARKET
        elif market_open <= current_time <= market_close:
            return MarketState.MARKET_OPEN
        else:
            return MarketState.MARKET_CLOSE

    def _get_resource_allocation(self) -> Dict[str, float]:
        """获取当前市场状态下的资源分配策略"""
        state = self.market_state.value
        return self.config['resource_allocation'].get(state, {
            'news': 0.25,
            'policy': 0.25,
            'market_data': 0.25,
            'analysis': 0.25
        })

    def register_module(self, module_name: str, module_instance: Any):
        """
        注册模块

        Args:
            module_name: 模块名称
            module_instance: 模块实例
        """
        self.modules[module_name] = module_instance
        logger.info(f"模块注册成功: {module_name}")

    def add_task(self, task: Task) -> str:
        """
        添加任务

        Args:
            task: 任务对象

        Returns:
            task_id: 任务ID
        """
        # 将任务添加到队列和字典
        self.task_queue.put((task.priority.value, task.created_at.timestamp(), task))
        self.tasks[task.task_id] = task

        logger.info(f"任务添加成功: {task.task_id}, 类型: {task.task_type}, 模块: {task.module}, 函数: {task.function}")

        return task.task_id

    def cancel_task(self, task_id: str) -> bool:
        """
        取消任务

        Args:
            task_id: 任务ID

        Returns:
            bool: 是否成功取消
        """
        if task_id in self.tasks:
            task = self.tasks[task_id]
            if task.status == TaskStatus.PENDING:
                task.status = TaskStatus.CANCELLED
                logger.info(f"任务已取消: {task_id}")
                return True
            else:
                logger.warning(f"无法取消任务，状态为: {task.status.name}")
                return False
        else:
            logger.warning(f"任务不存在: {task_id}")
            return False

    def get_task_status(self, task_id: str) -> Optional[TaskStatus]:
        """
        获取任务状态

        Args:
            task_id: 任务ID

        Returns:
            TaskStatus: 任务状态
        """
        if task_id in self.tasks:
            return self.tasks[task_id].status
        else:
            logger.warning(f"任务不存在: {task_id}")
            return None

    def get_task_result(self, task_id: str) -> Optional[Any]:
        """
        获取任务结果

        Args:
            task_id: 任务ID

        Returns:
            Any: 任务结果
        """
        if task_id in self.tasks:
            task = self.tasks[task_id]
            if task.status == TaskStatus.COMPLETED:
                return task.result
            else:
                logger.warning(f"任务未完成，状态为: {task.status.name}")
                return None
        else:
            logger.warning(f"任务不存在: {task_id}")
            return None

    def start(self):
        """启动调度器"""
        if self.is_running:
            logger.warning("调度器已经在运行")
            return

        logger.info("启动调度器...")
        self.is_running = True

        # 创建工作线程
        for i in range(self.max_workers):
            worker = threading.Thread(target=self._worker_thread, args=(i,))
            worker.daemon = True
            worker.start()
            self.workers.append(worker)

        # 创建监控线程
        self.monitor_thread = threading.Thread(target=self._monitor_thread)
        self.monitor_thread.daemon = True
        self.monitor_thread.start()

        logger.info(f"调度器启动成功，工作线程数: {len(self.workers)}")

    def stop(self):
        """停止调度器"""
        if not self.is_running:
            logger.warning("调度器未运行")
            return

        logger.info("停止调度器...")
        self.is_running = False

        # 等待工作线程结束
        for worker in self.workers:
            worker.join(timeout=5)

        # 等待监控线程结束
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)

        self.workers = []
        self.monitor_thread = None

        logger.info("调度器已停止")

    def _worker_thread(self, worker_id: int):
        """
        工作线程

        Args:
            worker_id: 工作线程ID
        """
        logger.info(f"工作线程 {worker_id} 启动")

        while self.is_running:
            try:
                # 尝试从队列获取任务，等待1秒
                try:
                    _, _, task = self.task_queue.get(timeout=1)
                except queue.Empty:
                    continue

                # 检查任务是否已取消
                if task.status == TaskStatus.CANCELLED:
                    self.task_queue.task_done()
                    continue

                # 更新任务状态
                task.status = TaskStatus.RUNNING
                task.started_at = datetime.now()

                logger.info(f"工作线程 {worker_id} 开始执行任务: {task.task_id}")

                # 执行任务
                try:
                    # 检查模块是否存在
                    if task.module not in self.modules:
                        raise ValueError(f"模块不存在: {task.module}")

                    module = self.modules[task.module]

                    # 检查函数是否存在
                    if not hasattr(module, task.function):
                        raise ValueError(f"函数不存在: {task.function}")

                    func = getattr(module, task.function)

                    # 执行函数
                    result = func(**task.params)

                    # 更新任务状态
                    task.status = TaskStatus.COMPLETED
                    task.completed_at = datetime.now()
                    task.result = result

                    logger.info(f"任务执行成功: {task.task_id}")

                except Exception as e:
                    logger.error(f"任务执行失败: {task.task_id}, 错误: {str(e)}")

                    # 更新任务状态
                    task.error = str(e)

                    # 检查是否需要重试
                    if task.retry_count < task.max_retries:
                        task.retry_count += 1
                        task.status = TaskStatus.PENDING
                        logger.info(f"任务将重试: {task.task_id}, 重试次数: {task.retry_count}/{task.max_retries}")

                        # 重新加入队列
                        self.task_queue.put((task.priority.value, datetime.now().timestamp(), task))
                    else:
                        task.status = TaskStatus.FAILED
                        task.completed_at = datetime.now()

                # 标记任务完成
                self.task_queue.task_done()

            except Exception as e:
                logger.error(f"工作线程 {worker_id} 发生错误: {str(e)}")
                time.sleep(1)

        logger.info(f"工作线程 {worker_id} 停止")

    def _monitor_thread(self):
        """监控线程"""
        logger.info("监控线程启动")

        while self.is_running:
            try:
                # 更新市场状态
                new_market_state = self._get_market_state()
                if new_market_state != self.market_state:
                    logger.info(f"市场状态变更: {self.market_state.name} -> {new_market_state.name}")
                    self.market_state = new_market_state
                    self.resource_allocation = self._get_resource_allocation()

                # 检查任务队列大小
                queue_size = self.task_queue.qsize()
                if queue_size > 100:
                    logger.warning(f"任务队列过大: {queue_size}")

                # 检查工作线程是否存活
                alive_workers = sum(1 for w in self.workers if w.is_alive())
                if alive_workers < self.max_workers:
                    logger.warning(f"工作线程数量不足: {alive_workers}/{self.max_workers}")

                    # 重启工作线程
                    for i in range(self.max_workers - alive_workers):
                        worker_id = len(self.workers)
                        worker = threading.Thread(target=self._worker_thread, args=(worker_id,))
                        worker.daemon = True
                        worker.start()
                        self.workers.append(worker)

                        logger.info(f"重启工作线程: {worker_id}")

                # 清理已完成的任务
                self._cleanup_tasks()

                # 保存调度器状态
                self._save_state()

                # 休眠一段时间
                time.sleep(self.config.get('monitor_interval', 60))

            except Exception as e:
                logger.error(f"监控线程发生错误: {str(e)}")
                time.sleep(60)

        logger.info("监控线程停止")

    def _cleanup_tasks(self):
        """清理已完成的任务"""
        # 获取所有已完成或失败的任务
        completed_tasks = [
            task_id for task_id, task in self.tasks.items()
            if task.status in (TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED)
            and (datetime.now() - task.completed_at > timedelta(days=1) if task.completed_at else False)
        ]

        # 从字典中删除
        for task_id in completed_tasks:
            del self.tasks[task_id]

        if completed_tasks:
            logger.info(f"清理已完成任务: {len(completed_tasks)}个")

    def _save_state(self):
        """保存调度器状态"""
        try:
            # 保存任务状态
            tasks_data = {task_id: task.to_dict() for task_id, task in self.tasks.items()}

            with open('data/scheduler/tasks.json', 'w', encoding='utf-8') as f:
                json.dump(tasks_data, f, indent=4)

            # 保存调度器状态
            state_data = {
                'market_state': self.market_state.name,
                'resource_allocation': self.resource_allocation,
                'task_count': len(self.tasks),
                'queue_size': self.task_queue.qsize(),
                'worker_count': len(self.workers),
                'updated_at': datetime.now().isoformat()
            }

            with open('data/scheduler/state.json', 'w', encoding='utf-8') as f:
                json.dump(state_data, f, indent=4)

        except Exception as e:
            logger.error(f"保存状态失败: {str(e)}")

    def _load_state(self):
        """加载调度器状态"""
        try:
            # 加载任务状态
            if os.path.exists('data/scheduler/tasks.json'):
                with open('data/scheduler/tasks.json', 'r', encoding='utf-8') as f:
                    tasks_data = json.load(f)

                for task_id, task_data in tasks_data.items():
                    task = Task.from_dict(task_data)
                    self.tasks[task_id] = task

                    # 如果任务仍在等待中，重新加入队列
                    if task.status == TaskStatus.PENDING:
                        self.task_queue.put((task.priority.value, task.created_at.timestamp(), task))

                logger.info(f"加载任务状态成功: {len(self.tasks)}个任务")

        except Exception as e:
            logger.error(f"加载状态失败: {str(e)}")

    def schedule_task(self, task_type: str, module: str, function: str, params: Dict[str, Any] = None,
                     priority: TaskPriority = TaskPriority.MEDIUM, schedule_time: Optional[datetime] = None,
                     timeout: int = None) -> str:
        """
        调度任务

        Args:
            task_type: 任务类型
            module: 模块名称
            function: 函数名称
            params: 函数参数
            priority: 任务优先级
            schedule_time: 计划执行时间
            timeout: 超时时间(秒)

        Returns:
            task_id: 任务ID
        """
        # 生成任务ID
        task_id = f"{task_type}_{module}_{function}_{datetime.now().strftime('%Y%m%d%H%M%S')}_{id(self)}"

        # 创建任务
        task = Task(
            task_id=task_id,
            task_type=task_type,
            module=module,
            function=function,
            params=params,
            priority=priority,
            schedule_time=schedule_time,
            timeout=timeout or self.config.get('task_timeout', 300)
        )

        # 添加任务
        return self.add_task(task)
