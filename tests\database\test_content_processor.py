"""
内容处理器测试

测试新闻和政策内容处理器的功能
"""

import os
import sys
import unittest
import json
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('test_content_processor')

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

# 导入被测试的模块
from database.unified_data_access import UnifiedDataAccess
from database.file_storage import FileStorage
from database.content_processor import ContentProcessor

class TestContentProcessor(unittest.TestCase):
    """内容处理器测试类"""
    
    @classmethod
    def setUpClass(cls):
        """测试前的准备工作"""
        logger.info("初始化内容处理器测试...")
        
        # 创建测试目录
        cls.test_dir = os.path.join('data', 'test_content_processor')
        os.makedirs(cls.test_dir, exist_ok=True)
        
        # 创建测试配置
        cls.test_config = {
            "postgresql": {
                "enabled": False,
                "use_sqlite_fallback": True,
                "sqlite_db_path": os.path.join(cls.test_dir, "test_financial_system.db")
            },
            "influxdb": {
                "enabled": False,
                "use_csv_fallback": True,
                "csv_dir": os.path.join(cls.test_dir, "time_series")
            },
            "mongodb": {
                "enabled": False,
                "use_json_fallback": True,
                "json_dir": os.path.join(cls.test_dir, "documents")
            },
            "redis": {
                "enabled": False,
                "use_memory_fallback": True
            },
            "fallback": {
                "enabled": True,
                "data_dir": cls.test_dir
            }
        }
        
        # 创建统一数据访问接口
        cls.data_access = UnifiedDataAccess(cls.test_config)
        
        # 创建文件存储管理器
        cls.file_storage = FileStorage(cls.test_dir)
        
        # 创建内容处理器
        cls.content_processor = ContentProcessor(cls.data_access, cls.file_storage)
        
        logger.info("内容处理器测试初始化完成")
    
    @classmethod
    def tearDownClass(cls):
        """测试后的清理工作"""
        logger.info("清理内容处理器测试...")
        
        # 删除测试目录
        import shutil
        if os.path.exists(cls.test_dir):
            shutil.rmtree(cls.test_dir)
        
        logger.info("内容处理器测试清理完成")
    
    def test_process_news(self):
        """测试处理新闻"""
        logger.info("测试处理新闻...")
        
        # 创建测试新闻数据
        test_news = {
            "title": "央行宣布降准0.5个百分点",
            "source": "东方财富网",
            "publish_date": "2025-05-15",
            "content": """
            中国人民银行今日宣布，决定于2025年5月20日下调金融机构存款准备金率0.5个百分点（不含已执行5%存款准备金率的金融机构）。
            此次降准是全面降准，共计释放长期资金约1.2万亿元。
            央行表示，此次降准是稳健货币政策的常规操作，有利于保持银行体系流动性合理充裕，引导金融机构加大对实体经济的支持力度。
            分析人士认为，此次降准将有效提升市场流动性，降低实体经济融资成本，对股市和债市形成利好。
            """,
            "url": "https://finance.eastmoney.com/news/1345,202505151234567890.html"
        }
        
        # 处理新闻
        result = self.content_processor.process_news(test_news)
        
        # 验证结果
        self.assertIsNotNone(result, "处理结果不应为None")
        self.assertIn('metadata', result, "处理结果应包含metadata")
        self.assertIn('analysis', result, "处理结果应包含analysis")
        
        # 验证元数据
        metadata = result['metadata']
        self.assertIn('news_id', metadata, "元数据应包含news_id")
        self.assertEqual(metadata['title'], test_news['title'], "标题应匹配")
        self.assertEqual(metadata['source'], test_news['source'], "来源应匹配")
        self.assertEqual(metadata['publish_date'], test_news['publish_date'], "发布日期应匹配")
        self.assertIn('content_storage_path', metadata, "元数据应包含content_storage_path")
        
        # 验证分析结果
        analysis = result['analysis']
        self.assertEqual(analysis['news_id'], metadata['news_id'], "分析结果的news_id应与元数据匹配")
        self.assertIn('sentiment', analysis, "分析结果应包含sentiment")
        self.assertIn('keywords', analysis, "分析结果应包含keywords")
        self.assertIn('summary', analysis, "分析结果应包含summary")
        self.assertIn('key_points', analysis, "分析结果应包含key_points")
        
        # 验证情感分析
        sentiment = analysis['sentiment']
        self.assertIn('score', sentiment, "情感分析应包含score")
        self.assertIn('label', sentiment, "情感分析应包含label")
        self.assertEqual(sentiment['label'], 'positive', "情感标签应为positive")
        
        # 验证关键词
        keywords = analysis['keywords']
        self.assertTrue(len(keywords) > 0, "关键词列表不应为空")
        self.assertTrue(any(k['word'] == '央行' for k in keywords), "关键词应包含'央行'")
        
        # 验证文件存储
        content_path = metadata['content_storage_path']
        self.assertTrue(os.path.exists(os.path.join(self.test_dir, content_path)), "内容文件应存在")
        
        logger.info("处理新闻测试通过")
    
    def test_process_policy(self):
        """测试处理政策"""
        logger.info("测试处理政策...")
        
        # 创建测试政策数据
        test_policy = {
            "title": "关于进一步促进资本市场健康发展的若干意见",
            "source": "证监会",
            "publish_date": "2025-05-15",
            "policy_code": "证监发〔2025〕15号",
            "policy_type": "监管政策",
            "content": """
            为贯彻落实党中央、国务院关于资本市场改革发展的决策部署，进一步促进资本市场健康发展，现提出如下意见：
            
            一、总体要求
            （一）指导思想。以习近平新时代中国特色社会主义思想为指导，全面贯彻党的二十大精神，坚持稳中求进工作总基调，完整、准确、全面贯彻新发展理念，加快构建新发展格局，着力推动高质量发展，更好发挥资本市场功能作用，促进形成资本市场良性循环。
            
            （二）基本原则。坚持市场化、法治化、国际化方向，统筹发展与安全，强化系统观念，处理好改革发展稳定关系，稳步推进资本市场制度型开放，防范化解重大金融风险，促进资本市场平稳健康发展。
            
            二、主要措施
            （一）完善资本市场基础制度。健全多层次资本市场体系，完善交易、发行、信息披露、退市等基础制度，提高上市公司质量，强化中介机构责任，加强投资者保护。
            
            （二）提高直接融资比重。拓宽企业直接融资渠道，优化融资结构，降低融资成本，更好满足实体经济多样化融资需求。
            
            （三）促进资本市场高水平开放。稳步扩大资本市场制度型开放，完善境外投资者参与境内资本市场的渠道和方式，推动境内企业依法合规赴境外上市。
            """,
            "url": "http://www.csrc.gov.cn/csrc/c100028/202505/t20250515_123456.shtml"
        }
        
        # 处理政策
        result = self.content_processor.process_policy(test_policy)
        
        # 验证结果
        self.assertIsNotNone(result, "处理结果不应为None")
        self.assertIn('metadata', result, "处理结果应包含metadata")
        self.assertIn('analysis', result, "处理结果应包含analysis")
        
        # 验证元数据
        metadata = result['metadata']
        self.assertIn('policy_id', metadata, "元数据应包含policy_id")
        self.assertEqual(metadata['title'], test_policy['title'], "标题应匹配")
        self.assertEqual(metadata['source'], test_policy['source'], "来源应匹配")
        self.assertEqual(metadata['publish_date'], test_policy['publish_date'], "发布日期应匹配")
        self.assertEqual(metadata['policy_code'], test_policy['policy_code'], "政策文号应匹配")
        self.assertIn('content_storage_path', metadata, "元数据应包含content_storage_path")
        
        # 验证分析结果
        analysis = result['analysis']
        self.assertEqual(analysis['policy_id'], metadata['policy_id'], "分析结果的policy_id应与元数据匹配")
        self.assertIn('sentiment', analysis, "分析结果应包含sentiment")
        self.assertIn('keywords', analysis, "分析结果应包含keywords")
        self.assertIn('summary', analysis, "分析结果应包含summary")
        self.assertIn('key_points', analysis, "分析结果应包含key_points")
        self.assertIn('subject', analysis, "分析结果应包含subject")
        self.assertIn('action', analysis, "分析结果应包含action")
        self.assertIn('object', analysis, "分析结果应包含object")
        
        # 验证情感分析
        sentiment = analysis['sentiment']
        self.assertIn('score', sentiment, "情感分析应包含score")
        self.assertIn('label', sentiment, "情感分析应包含label")
        
        # 验证关键词
        keywords = analysis['keywords']
        self.assertTrue(len(keywords) > 0, "关键词列表不应为空")
        self.assertTrue(any(k['word'] == '证监会' for k in keywords) or any(k['word'] == '资本市场' for k in keywords), 
                       "关键词应包含'证监会'或'资本市场'")
        
        # 验证文件存储
        content_path = metadata['content_storage_path']
        self.assertTrue(os.path.exists(os.path.join(self.test_dir, content_path)), "内容文件应存在")
        
        logger.info("处理政策测试通过")
    
    def test_file_storage(self):
        """测试文件存储"""
        logger.info("测试文件存储...")
        
        # 保存内容
        content = "这是测试内容"
        file_path = self.file_storage.save_news_content(
            news_id="test123",
            content=content,
            source="test",
            publish_date="2025-05-15"
        )
        
        # 验证文件存在
        self.assertTrue(os.path.exists(os.path.join(self.test_dir, file_path)), "文件应存在")
        
        # 加载内容
        loaded_content = self.file_storage.load_content(file_path)
        self.assertEqual(loaded_content, content, "加载的内容应与原内容匹配")
        
        # 删除内容
        result = self.file_storage.delete_content(file_path)
        self.assertTrue(result, "删除操作应成功")
        self.assertFalse(os.path.exists(os.path.join(self.test_dir, file_path)), "文件应被删除")
        
        logger.info("文件存储测试通过")

if __name__ == '__main__':
    unittest.main()
