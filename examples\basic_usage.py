"""
Basic usage example for policy_liquidity_volatility_arbitrage.
"""

import os
import sys
import pandas as pd

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.logger import logger
from utils.config_loader import ConfigLoader
from utils.stock_utils import get_stock_list, get_industry_classification, get_stock_price

def main():
    """
    Main function.
    """
    # Load configuration
    config_loader = ConfigLoader()
    config = config_loader.config
    
    logger.info("Basic usage example for policy_liquidity_volatility_arbitrage")
    
    # Get stock list
    logger.info("Getting stock list...")
    stock_list = get_stock_list()
    logger.info(f"Found {len(stock_list)} stocks")
    
    # Display first 5 stocks
    logger.info("First 5 stocks:")
    print(stock_list.head())
    
    # Get industry classification
    logger.info("Getting industry classification...")
    industry_df = get_industry_classification()
    logger.info(f"Found industry classification for {len(industry_df)} stocks")
    
    # Display first 5 industries
    logger.info("First 5 industry classifications:")
    print(industry_df.head())
    
    # Get stock price for a sample stock
    sample_stock = stock_list.iloc[0]['stock_code']
    logger.info(f"Getting price data for {sample_stock}...")
    
    from datetime import datetime, timedelta
    end_date = datetime.now().strftime('%Y-%m-%d')
    start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
    
    price_df = get_stock_price(sample_stock, start_date, end_date)
    logger.info(f"Found {len(price_df)} price records")
    
    # Display first 5 price records
    logger.info("First 5 price records:")
    print(price_df.head())
    
    logger.info("Example completed")

if __name__ == '__main__':
    main()
