"""
FinBERT模型检查和修复工具
"""

import os
import sys
import logging
from pathlib import Path

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def check_finbert_availability():
    """检查FinBERT模型的可用性"""
    print("🔍 检查FinBERT模型可用性...")
    
    # 1. 检查本地模型文件
    model_paths = [
        "models/finbert",
        "models/finbert-sentiment",
        "models/ProsusAI/finbert"
    ]
    
    print("\n=== 检查本地模型文件 ===")
    local_model_found = False
    for path in model_paths:
        if os.path.exists(path):
            print(f"✅ 找到本地模型: {path}")
            local_model_found = True
            # 检查模型文件完整性
            check_model_files(path)
        else:
            print(f"❌ 本地模型不存在: {path}")
    
    # 2. 检查transformers库
    print("\n=== 检查transformers库 ===")
    try:
        from transformers import AutoTokenizer, AutoModelForSequenceClassification
        print("✅ transformers库可用")
        
        # 3. 尝试从HuggingFace下载模型
        print("\n=== 尝试从HuggingFace下载FinBERT ===")
        try:
            # 使用国内镜像
            model_names = [
                "ProsusAI/finbert",
                "mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis"
            ]
            
            for model_name in model_names:
                try:
                    print(f"尝试下载: {model_name}")
                    tokenizer = AutoTokenizer.from_pretrained(model_name)
                    model = AutoModelForSequenceClassification.from_pretrained(model_name)
                    print(f"✅ 成功加载: {model_name}")
                    
                    # 测试模型
                    test_model(tokenizer, model, model_name)
                    return True
                    
                except Exception as e:
                    print(f"❌ 加载失败 {model_name}: {str(e)}")
                    continue
                    
        except Exception as e:
            print(f"❌ HuggingFace下载失败: {str(e)}")
            
    except ImportError as e:
        print(f"❌ transformers库不可用: {str(e)}")
        print("请安装: pip install transformers torch")
        return False
    
    # 4. 检查中文BERT模型
    print("\n=== 检查中文BERT模型 ===")
    try:
        from transformers import BertTokenizer, BertForSequenceClassification
        
        chinese_models = [
            "bert-base-chinese",
            "hfl/chinese-bert-wwm-ext"
        ]
        
        for model_name in chinese_models:
            try:
                print(f"尝试加载: {model_name}")
                tokenizer = BertTokenizer.from_pretrained(model_name)
                model = BertForSequenceClassification.from_pretrained(model_name)
                print(f"✅ 成功加载: {model_name}")
                
                # 测试模型
                test_chinese_model(tokenizer, model, model_name)
                return True
                
            except Exception as e:
                print(f"❌ 加载失败 {model_name}: {str(e)}")
                continue
                
    except Exception as e:
        print(f"❌ 中文BERT模型检查失败: {str(e)}")
    
    return False

def check_model_files(model_path):
    """检查模型文件完整性"""
    required_files = [
        "config.json",
        "pytorch_model.bin",
        "tokenizer.json",
        "vocab.txt"
    ]
    
    print(f"  检查 {model_path} 的文件完整性:")
    for file in required_files:
        file_path = os.path.join(model_path, file)
        if os.path.exists(file_path):
            size = os.path.getsize(file_path)
            print(f"    ✅ {file} ({size/1024/1024:.1f}MB)")
        else:
            print(f"    ❌ {file} (缺失)")

def test_model(tokenizer, model, model_name):
    """测试模型功能"""
    print(f"  测试 {model_name}:")
    
    try:
        # 测试文本
        test_texts = [
            "The company reported strong quarterly earnings.",
            "Stock prices are falling due to market uncertainty.",
            "央行降准释放流动性，利好股市。"
        ]
        
        for text in test_texts:
            try:
                inputs = tokenizer(text, return_tensors="pt", truncation=True, padding=True)
                outputs = model(**inputs)
                predictions = outputs.logits.softmax(dim=-1)
                print(f"    ✅ 文本: '{text[:30]}...' -> 预测成功")
            except Exception as e:
                print(f"    ❌ 文本预测失败: {str(e)}")
                
    except Exception as e:
        print(f"    ❌ 模型测试失败: {str(e)}")

def test_chinese_model(tokenizer, model, model_name):
    """测试中文模型功能"""
    print(f"  测试中文模型 {model_name}:")
    
    try:
        # 中文测试文本
        test_texts = [
            "央行降准释放流动性，利好股市。",
            "公司业绩大幅下滑，股价承压。",
            "新政策出台，相关板块受益。"
        ]
        
        for text in test_texts:
            try:
                inputs = tokenizer(text, return_tensors="pt", truncation=True, padding=True)
                outputs = model(**inputs)
                print(f"    ✅ 中文文本: '{text}' -> 处理成功")
            except Exception as e:
                print(f"    ❌ 中文文本处理失败: {str(e)}")
                
    except Exception as e:
        print(f"    ❌ 中文模型测试失败: {str(e)}")

def download_finbert_models():
    """下载FinBERT模型到本地"""
    print("\n🔄 开始下载FinBERT模型...")
    
    try:
        from transformers import AutoTokenizer, AutoModelForSequenceClassification
        
        # 创建模型目录
        os.makedirs("models", exist_ok=True)
        
        models_to_download = [
            {
                "name": "ProsusAI/finbert",
                "local_path": "models/finbert"
            },
            {
                "name": "mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis", 
                "local_path": "models/financial-sentiment"
            }
        ]
        
        for model_info in models_to_download:
            try:
                print(f"\n下载 {model_info['name']} 到 {model_info['local_path']}...")
                
                # 下载tokenizer
                tokenizer = AutoTokenizer.from_pretrained(model_info['name'])
                tokenizer.save_pretrained(model_info['local_path'])
                
                # 下载模型
                model = AutoModelForSequenceClassification.from_pretrained(model_info['name'])
                model.save_pretrained(model_info['local_path'])
                
                print(f"✅ 成功下载: {model_info['name']}")
                
            except Exception as e:
                print(f"❌ 下载失败 {model_info['name']}: {str(e)}")
                
    except Exception as e:
        print(f"❌ 下载过程失败: {str(e)}")

def create_finbert_config():
    """创建FinBERT配置文件"""
    print("\n📝 创建FinBERT配置...")
    
    config = {
        "model_configs": {
            "finbert": {
                "model_name": "ProsusAI/finbert",
                "local_path": "models/finbert",
                "task": "sentiment-analysis",
                "labels": ["negative", "neutral", "positive"]
            },
            "financial_sentiment": {
                "model_name": "mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis",
                "local_path": "models/financial-sentiment", 
                "task": "sentiment-analysis",
                "labels": ["NEGATIVE", "POSITIVE"]
            },
            "chinese_bert": {
                "model_name": "bert-base-chinese",
                "local_path": "models/chinese-bert",
                "task": "classification",
                "labels": ["negative", "neutral", "positive"]
            }
        },
        "default_model": "finbert",
        "fallback_model": "chinese_bert",
        "cache_dir": "models/cache",
        "max_length": 512,
        "batch_size": 16
    }
    
    # 保存配置
    config_path = "config/finbert_config.json"
    os.makedirs(os.path.dirname(config_path), exist_ok=True)
    
    import json
    with open(config_path, 'w', encoding='utf-8') as f:
        json.dump(config, f, ensure_ascii=False, indent=2)
    
    print(f"✅ 配置文件已保存: {config_path}")

def main():
    """主函数"""
    print("🚀 FinBERT模型检查和修复工具")
    print("=" * 50)
    
    # 检查模型可用性
    model_available = check_finbert_availability()
    
    if not model_available:
        print("\n❌ 没有找到可用的FinBERT模型")
        
        # 询问是否下载
        response = input("\n是否尝试下载FinBERT模型? (y/n): ")
        if response.lower() == 'y':
            download_finbert_models()
            
            # 重新检查
            print("\n🔄 重新检查模型可用性...")
            model_available = check_finbert_availability()
    
    # 创建配置文件
    create_finbert_config()
    
    # 总结
    print("\n" + "=" * 50)
    if model_available:
        print("✅ FinBERT模型检查完成，模型可用！")
        print("💡 建议:")
        print("   1. 使用本地模型以提高加载速度")
        print("   2. 配置GPU加速（如果可用）")
        print("   3. 调整batch_size以优化性能")
    else:
        print("❌ FinBERT模型不可用")
        print("💡 解决方案:")
        print("   1. 检查网络连接")
        print("   2. 安装必要依赖: pip install transformers torch")
        print("   3. 使用国内镜像下载模型")
        print("   4. 考虑使用中文BERT模型作为替代")
    
    return model_available

if __name__ == "__main__":
    main()
