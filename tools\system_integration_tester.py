"""
系统集成测试工具
测试整个智能分析系统的集成功能
"""

import os
import sys
import time
import json
import asyncio
from datetime import datetime
from typing import Dict, List, Any, Optional

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.intelligent_finbert_engine import get_finbert_engine
from core.multi_factor_coupling_engine import MultiFactorCouplingEngine
from core.data_storage import DataStorage
from core.unified_data_collector import UnifiedDataCollector
from utils.logger import logger
import pandas as pd

class SystemIntegrationTester:
    """系统集成测试器"""
    
    def __init__(self):
        self.test_results = {}
        self.start_time = None
        self.end_time = None
        
        logger.info("系统集成测试器初始化完成")
    
    def run_full_integration_test(self) -> Dict[str, Any]:
        """运行完整的集成测试"""
        self.start_time = datetime.now()
        logger.info("🚀 开始系统集成测试...")
        
        # 测试计划
        test_plan = [
            ('数据收集测试', self.test_data_collection),
            ('数据存储测试', self.test_data_storage),
            ('FinBERT引擎测试', self.test_finbert_engine),
            ('多因子耦合测试', self.test_coupling_analysis),
            ('端到端流程测试', self.test_end_to_end_flow),
            ('性能压力测试', self.test_performance),
            ('错误处理测试', self.test_error_handling)
        ]
        
        # 执行测试
        for test_name, test_func in test_plan:
            logger.info(f"📋 执行测试: {test_name}")
            try:
                start_time = time.time()
                result = test_func()
                execution_time = time.time() - start_time
                
                self.test_results[test_name] = {
                    'status': 'PASS' if result['success'] else 'FAIL',
                    'execution_time': execution_time,
                    'details': result,
                    'timestamp': datetime.now().isoformat()
                }
                
                status_icon = "✅" if result['success'] else "❌"
                logger.info(f"{status_icon} {test_name}: {self.test_results[test_name]['status']} ({execution_time:.2f}s)")
                
            except Exception as e:
                self.test_results[test_name] = {
                    'status': 'ERROR',
                    'execution_time': 0,
                    'details': {'error': str(e)},
                    'timestamp': datetime.now().isoformat()
                }
                logger.error(f"❌ {test_name}: ERROR - {str(e)}")
        
        self.end_time = datetime.now()
        
        # 生成测试报告
        report = self.generate_test_report()
        self.save_test_report(report)
        
        return report
    
    def test_data_collection(self) -> Dict[str, Any]:
        """测试数据收集功能"""
        try:
            collector = UnifiedDataCollector()
            
            # 测试新闻数据收集
            news_data = collector.collect_news_data()
            news_success = news_data is not None and len(news_data) > 0
            
            # 测试资金流数据收集
            fund_flow_data = collector.collect_fund_flow_data()
            fund_flow_success = fund_flow_data is not None
            
            # 测试市场数据收集
            market_data = collector.collect_market_data()
            market_success = market_data is not None
            
            return {
                'success': news_success and fund_flow_success and market_success,
                'news_data_count': len(news_data) if news_data else 0,
                'fund_flow_success': fund_flow_success,
                'market_data_success': market_success,
                'details': {
                    'news_collection': 'PASS' if news_success else 'FAIL',
                    'fund_flow_collection': 'PASS' if fund_flow_success else 'FAIL',
                    'market_data_collection': 'PASS' if market_success else 'FAIL'
                }
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def test_data_storage(self) -> Dict[str, Any]:
        """测试数据存储功能"""
        try:
            storage = DataStorage()
            
            # 测试数据存储
            test_data = {'test': 'data', 'timestamp': datetime.now().isoformat()}
            store_success = storage.store_data('test_data', test_data)
            
            # 测试数据读取
            retrieved_data = storage.get_latest_data('test_data', limit=1)
            retrieve_success = retrieved_data is not None and len(retrieved_data) > 0
            
            # 测试存储状态
            storage_status = storage.get_storage_status()
            status_success = storage_status is not None
            
            return {
                'success': store_success and retrieve_success and status_success,
                'store_success': store_success,
                'retrieve_success': retrieve_success,
                'storage_status': storage_status,
                'details': {
                    'data_storage': 'PASS' if store_success else 'FAIL',
                    'data_retrieval': 'PASS' if retrieve_success else 'FAIL',
                    'status_check': 'PASS' if status_success else 'FAIL'
                }
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def test_finbert_engine(self) -> Dict[str, Any]:
        """测试FinBERT引擎功能"""
        try:
            finbert_engine = get_finbert_engine()
            
            # 测试情感分析
            test_texts = [
                "央行降准释放流动性，利好股市发展",
                "公司业绩大幅下滑，股价面临压力",
                "新政策出台，相关板块受益明显"
            ]
            
            sentiment_results = finbert_engine.analyze_sentiment(test_texts)
            sentiment_success = sentiment_results is not None and len(sentiment_results) == len(test_texts)
            
            # 测试金融影响分析
            impact_results = finbert_engine.analyze_financial_impact(test_texts)
            impact_success = impact_results is not None and len(impact_results) == len(test_texts)
            
            # 测试模型状态
            model_status = finbert_engine.get_model_status()
            status_success = model_status is not None
            
            return {
                'success': sentiment_success and impact_success and status_success,
                'sentiment_analysis': sentiment_results,
                'impact_analysis': impact_results,
                'model_status': model_status,
                'details': {
                    'sentiment_analysis': 'PASS' if sentiment_success else 'FAIL',
                    'impact_analysis': 'PASS' if impact_success else 'FAIL',
                    'model_status': 'PASS' if status_success else 'FAIL'
                }
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def test_coupling_analysis(self) -> Dict[str, Any]:
        """测试多因子耦合分析"""
        try:
            coupling_engine = MultiFactorCouplingEngine()
            
            # 创建测试数据
            test_data_streams = {
                'news': pd.DataFrame({
                    'title': ['测试新闻1', '测试新闻2'],
                    'sentiment': ['positive', 'negative']
                }),
                'fund_flow': pd.DataFrame({
                    'net_inflow': [1000000, -500000],
                    'main_net_inflow': [800000, -300000]
                }),
                'volatility': pd.DataFrame({
                    'volatility': [0.02, 0.03]
                })
            }
            
            # 执行耦合分析
            coupling_result = coupling_engine.analyze_coupling(test_data_streams)
            analysis_success = coupling_result is not None and 'coupling_score' in coupling_result
            
            return {
                'success': analysis_success,
                'coupling_result': coupling_result,
                'details': {
                    'coupling_analysis': 'PASS' if analysis_success else 'FAIL'
                }
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def test_end_to_end_flow(self) -> Dict[str, Any]:
        """测试端到端流程"""
        try:
            # 1. 数据收集
            collector = UnifiedDataCollector()
            news_data = collector.collect_news_data()
            
            # 2. 数据存储
            storage = DataStorage()
            if news_data:
                storage.store_data('e2e_test_news', news_data)
            
            # 3. AI分析
            if news_data:
                finbert_engine = get_finbert_engine()
                # 提取前5条新闻标题
                texts = [item.get('title', '') for item in news_data[:5] if isinstance(item, dict)]
                if texts:
                    sentiment_results = finbert_engine.analyze_sentiment(texts)
                else:
                    sentiment_results = []
            else:
                sentiment_results = []
            
            # 4. 耦合分析
            coupling_engine = MultiFactorCouplingEngine()
            if news_data:
                test_streams = {
                    'news': pd.DataFrame(news_data[:10]) if len(news_data) >= 10 else pd.DataFrame(news_data)
                }
                coupling_result = coupling_engine.analyze_coupling(test_streams)
            else:
                coupling_result = {}
            
            flow_success = (
                news_data is not None and 
                len(sentiment_results) > 0 and 
                coupling_result is not None
            )
            
            return {
                'success': flow_success,
                'data_collected': len(news_data) if news_data else 0,
                'sentiment_analyzed': len(sentiment_results),
                'coupling_analyzed': bool(coupling_result),
                'details': {
                    'data_collection': 'PASS' if news_data else 'FAIL',
                    'ai_analysis': 'PASS' if sentiment_results else 'FAIL',
                    'coupling_analysis': 'PASS' if coupling_result else 'FAIL'
                }
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def test_performance(self) -> Dict[str, Any]:
        """测试系统性能"""
        try:
            performance_metrics = {}
            
            # 测试FinBERT性能
            finbert_engine = get_finbert_engine()
            test_texts = ["测试文本"] * 10
            
            start_time = time.time()
            sentiment_results = finbert_engine.analyze_sentiment(test_texts)
            finbert_time = time.time() - start_time
            
            performance_metrics['finbert_10_texts'] = finbert_time
            performance_metrics['finbert_throughput'] = len(test_texts) / finbert_time if finbert_time > 0 else 0
            
            # 测试数据存储性能
            storage = DataStorage()
            test_data = {'test': 'performance_data'}
            
            start_time = time.time()
            for i in range(10):
                storage.store_data(f'perf_test_{i}', test_data)
            storage_time = time.time() - start_time
            
            performance_metrics['storage_10_operations'] = storage_time
            performance_metrics['storage_throughput'] = 10 / storage_time if storage_time > 0 else 0
            
            return {
                'success': True,
                'performance_metrics': performance_metrics,
                'details': {
                    'finbert_performance': f"{finbert_time:.3f}s for 10 texts",
                    'storage_performance': f"{storage_time:.3f}s for 10 operations"
                }
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def test_error_handling(self) -> Dict[str, Any]:
        """测试错误处理"""
        try:
            error_tests = {}
            
            # 测试FinBERT错误处理
            try:
                finbert_engine = get_finbert_engine()
                # 测试空输入
                result = finbert_engine.analyze_sentiment([])
                error_tests['finbert_empty_input'] = 'PASS' if result == [] else 'FAIL'
            except Exception:
                error_tests['finbert_empty_input'] = 'FAIL'
            
            # 测试数据存储错误处理
            try:
                storage = DataStorage()
                # 测试无效数据类型
                result = storage.store_data('', None)
                error_tests['storage_invalid_data'] = 'PASS' if not result else 'FAIL'
            except Exception:
                error_tests['storage_invalid_data'] = 'PASS'  # 异常被正确处理
            
            all_passed = all(status == 'PASS' for status in error_tests.values())
            
            return {
                'success': all_passed,
                'error_tests': error_tests,
                'details': error_tests
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def generate_test_report(self) -> Dict[str, Any]:
        """生成测试报告"""
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result['status'] == 'PASS')
        failed_tests = sum(1 for result in self.test_results.values() if result['status'] == 'FAIL')
        error_tests = sum(1 for result in self.test_results.values() if result['status'] == 'ERROR')
        
        total_time = (self.end_time - self.start_time).total_seconds() if self.end_time and self.start_time else 0
        
        report = {
            'test_summary': {
                'total_tests': total_tests,
                'passed_tests': passed_tests,
                'failed_tests': failed_tests,
                'error_tests': error_tests,
                'success_rate': (passed_tests / total_tests * 100) if total_tests > 0 else 0,
                'total_execution_time': total_time
            },
            'test_results': self.test_results,
            'test_timestamp': {
                'start_time': self.start_time.isoformat() if self.start_time else None,
                'end_time': self.end_time.isoformat() if self.end_time else None
            },
            'system_info': {
                'python_version': sys.version,
                'platform': sys.platform
            }
        }
        
        return report
    
    def save_test_report(self, report: Dict[str, Any]):
        """保存测试报告"""
        try:
            os.makedirs('reports', exist_ok=True)
            
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            report_file = f"reports/system_integration_test_{timestamp}.json"
            
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            
            logger.info(f"测试报告已保存: {report_file}")
            
            # 打印摘要
            summary = report['test_summary']
            logger.info("=" * 60)
            logger.info("📊 系统集成测试报告摘要")
            logger.info("=" * 60)
            logger.info(f"总测试数: {summary['total_tests']}")
            logger.info(f"通过: {summary['passed_tests']}")
            logger.info(f"失败: {summary['failed_tests']}")
            logger.info(f"错误: {summary['error_tests']}")
            logger.info(f"成功率: {summary['success_rate']:.1f}%")
            logger.info(f"总耗时: {summary['total_execution_time']:.2f}秒")
            logger.info("=" * 60)
            
        except Exception as e:
            logger.error(f"保存测试报告失败: {str(e)}")

def main():
    """主函数"""
    print("🧪 系统集成测试工具")
    print("=" * 50)
    
    tester = SystemIntegrationTester()
    report = tester.run_full_integration_test()
    
    # 返回测试是否成功
    success_rate = report['test_summary']['success_rate']
    if success_rate >= 80:
        print("🎉 系统集成测试通过！")
        return True
    else:
        print("⚠️ 系统集成测试未完全通过，请检查失败项目")
        return False

if __name__ == "__main__":
    main()
