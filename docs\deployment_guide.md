# 金融市场分析系统部署指南

## 1. 系统要求

### 1.1 硬件要求

- **CPU**：至少4核心
- **内存**：至少8GB RAM
- **存储**：至少20GB可用空间
- **网络**：稳定的互联网连接

### 1.2 软件要求

- **操作系统**：Windows 10/11 或 Linux (Ubuntu 18.04+/CentOS 7+)
- **Python**：Python 3.8+
- **依赖库**：详见requirements.txt

## 2. 安装步骤

### 2.1 安装Python环境

#### Windows

1. 从[Python官网](https://www.python.org/downloads/)下载Python 3.8+安装包
2. 运行安装包，勾选"Add Python to PATH"选项
3. 完成安装后，打开命令提示符，验证安装：
   ```bash
   python --version
   pip --version
   ```

#### Linux

1. 更新包管理器
   ```bash
   # Ubuntu
   sudo apt update
   
   # CentOS
   sudo yum update
   ```

2. 安装Python和pip
   ```bash
   # Ubuntu
   sudo apt install python3 python3-pip
   
   # CentOS
   sudo yum install python3 python3-pip
   ```

3. 验证安装
   ```bash
   python3 --version
   pip3 --version
   ```

### 2.2 获取系统代码

1. 使用Git克隆代码仓库
   ```bash
   git clone https://github.com/your-repo/financial-market-analysis-system.git
   cd financial-market-analysis-system
   ```

2. 或者下载并解压代码包
   ```bash
   # 下载代码包
   wget https://github.com/your-repo/financial-market-analysis-system/archive/main.zip
   
   # 解压代码包
   unzip main.zip
   
   # 进入项目目录
   cd financial-market-analysis-system-main
   ```

### 2.3 安装依赖库

1. 创建虚拟环境（可选但推荐）
   ```bash
   # Windows
   python -m venv venv
   venv\Scripts\activate
   
   # Linux
   python3 -m venv venv
   source venv/bin/activate
   ```

2. 安装依赖库
   ```bash
   pip install -r requirements.txt
   ```

### 2.4 创建必要的目录

```bash
mkdir -p data/hot data/warm data/cold logs
```

## 3. 配置系统

### 3.1 基本配置

1. 复制配置文件模板
   ```bash
   cp config/config.json.template config/config.json
   cp config/data_sources.json.template config/data_sources.json
   cp config/logging.json.template config/logging.json
   ```

2. 编辑配置文件
   - 根据需要修改`config/config.json`中的参数
   - 配置数据源参数，如API密钥等
   - 调整日志级别和输出方式

### 3.2 数据源配置

1. 配置AKShare数据源
   - 如果需要使用AKShare的高级功能，请在`config/data_sources.json`中配置相关参数

2. 配置Web爬虫数据源
   - 根据需要配置爬虫参数，如请求头、代理等

### 3.3 存储配置

1. 配置数据存储路径
   - 在`config/config.json`中配置热数据、温数据和冷数据的存储路径

2. 配置数据清理策略
   - 设置数据过期时间和清理策略

## 4. 运行系统

### 4.1 启动系统操作面板

```bash
# 激活虚拟环境（如果使用）
# Windows
venv\Scripts\activate
# Linux
source venv/bin/activate

# 启动系统操作面板
python system_operation_panel.py
```

### 4.2 命令行运行模块

如果需要在命令行中单独运行某个模块，可以使用以下命令：

```bash
# 运行政策分析模块
python -m data_sources.policy_analyzer

# 运行新闻监控模块
python -m data_sources.news_monitor

# 运行资金流分析模块
python -m data_sources.fund_flow_analyzer

# 运行波动率分析模块
python -m data_sources.volatility_analyzer
```

### 4.3 设置定时任务

除了使用系统操作面板设置定时任务外，还可以使用系统自带的任务调度工具：

#### Windows (使用任务计划程序)

1. 创建批处理文件`run_system.bat`：
   ```batch
   @echo off
   cd /d %~dp0
   call venv\Scripts\activate
   python system_operation_panel.py
   ```

2. 打开任务计划程序，创建基本任务
   - 设置触发器为每天指定时间
   - 设置操作为启动程序，选择`run_system.bat`

#### Linux (使用cron)

1. 创建Shell脚本`run_system.sh`：
   ```bash
   #!/bin/bash
   cd /path/to/financial-market-analysis-system
   source venv/bin/activate
   python system_operation_panel.py
   ```

2. 添加执行权限：
   ```bash
   chmod +x run_system.sh
   ```

3. 编辑crontab：
   ```bash
   crontab -e
   ```

4. 添加定时任务，例如每天9:30运行：
   ```
   30 9 * * * /path/to/financial-market-analysis-system/run_system.sh
   ```

## 5. 系统维护

### 5.1 日志管理

1. 日志文件位于`logs`目录下
2. 定期检查日志文件，及时发现和解决问题
3. 设置日志轮转，避免日志文件过大：
   ```bash
   # 创建logrotate配置文件
   sudo nano /etc/logrotate.d/financial-market-analysis
   ```
   
   配置内容：
   ```
   /path/to/financial-market-analysis-system/logs/*.log {
       daily
       rotate 7
       compress
       delaycompress
       missingok
       notifempty
       create 0640 user group
   }
   ```

### 5.2 数据备份

1. 定期备份重要数据：
   ```bash
   # 创建备份脚本
   nano backup.sh
   ```
   
   脚本内容：
   ```bash
   #!/bin/bash
   BACKUP_DIR="/path/to/backups"
   DATE=$(date +%Y%m%d)
   mkdir -p $BACKUP_DIR
   tar -czf $BACKUP_DIR/data_backup_$DATE.tar.gz data/
   ```

2. 添加执行权限：
   ```bash
   chmod +x backup.sh
   ```

3. 设置定时备份任务：
   ```bash
   # 编辑crontab
   crontab -e
   
   # 添加每周备份任务
   0 0 * * 0 /path/to/financial-market-analysis-system/backup.sh
   ```

### 5.3 系统更新

1. 获取最新代码：
   ```bash
   git pull origin main
   ```

2. 更新依赖库：
   ```bash
   pip install -r requirements.txt
   ```

3. 检查配置文件是否需要更新
4. 重启系统

## 6. 故障排除

### 6.1 常见问题

1. **系统无法启动**
   - 检查Python版本是否正确
   - 检查依赖库是否安装完整
   - 检查配置文件是否正确

2. **数据获取失败**
   - 检查网络连接
   - 检查数据源配置
   - 检查API密钥是否有效

3. **内存不足**
   - 增加系统内存
   - 调整配置，减少同时处理的数据量
   - 优化数据处理逻辑

4. **系统运行缓慢**
   - 检查CPU和内存使用情况
   - 优化数据处理逻辑
   - 考虑使用更强大的硬件

### 6.2 日志分析

1. 检查日志文件，查找错误信息
2. 根据错误信息定位问题
3. 参考开发文档和用户手册，解决问题

### 6.3 联系支持

如果遇到无法解决的问题，请联系系统开发团队获取支持：

- 邮箱：<EMAIL>
- 电话：400-123-4567
- 工作时间：周一至周五 9:00-18:00

## 7. 安全建议

1. **定期更新系统**
   - 保持Python和依赖库的最新版本
   - 定期更新系统代码

2. **数据安全**
   - 定期备份重要数据
   - 限制数据访问权限

3. **网络安全**
   - 使用防火墙限制系统访问
   - 使用HTTPS进行API通信
   - 避免在公共网络上运行系统
