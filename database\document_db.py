"""
文档数据库客户端

实现MongoDB数据库的访问，并提供JSON回退方案
"""

import logging
import json
import os
from typing import Dict, Any, List, Optional, Union
from datetime import datetime

from .db_client import DBClient
from .data_types import DOCUMENT_DATA_TYPES

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('document_db')

class MongoDBClient(DBClient):
    """MongoDB数据库客户端"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化MongoDB数据库客户端
        
        Args:
            config: 配置信息
        """
        super().__init__(config)
        
        # 如果配置了使用JSON回退，则初始化JSON存储
        self.use_json_fallback = config.get('use_json_fallback', True)
        self.json_dir = config.get('json_dir', os.path.join('data', 'documents'))
        
        # 尝试连接数据库
        self.connect()
    
    def connect(self) -> bool:
        """
        连接数据库
        
        Returns:
            连接结果
        """
        try:
            # 尝试导入pymongo
            import pymongo
            
            # 连接MongoDB
            connection_string = f"mongodb://"
            if self.config.get('user') and self.config.get('password'):
                connection_string += f"{self.config.get('user')}:{self.config.get('password')}@"
            
            connection_string += f"{self.config.get('host', 'localhost')}:{self.config.get('port', 27017)}"
            
            self.client = pymongo.MongoClient(connection_string)
            self.connection = self.client[self.config.get('database', 'financial_system')]
            
            # 测试连接
            self.client.server_info()
            
            self._log_info("MongoDB数据库连接成功")
            return True
        
        except ImportError:
            self._log_error("pymongo模块未安装，无法连接MongoDB", Exception("Module not found"))
            
            # 如果配置了使用JSON回退，则使用JSON存储
            if self.use_json_fallback:
                self._init_json_storage()
                self._log_info("使用JSON存储作为MongoDB的回退")
                return True
            
            return False
        
        except Exception as e:
            self._log_error("MongoDB数据库连接失败", e)
            
            # 如果配置了使用JSON回退，则使用JSON存储
            if self.use_json_fallback:
                self._init_json_storage()
                self._log_info("使用JSON存储作为MongoDB的回退")
                return True
            
            return False
    
    def _init_json_storage(self):
        """初始化JSON存储"""
        # 确保JSON目录存在
        os.makedirs(self.json_dir, exist_ok=True)
        
        # 为每个文档类型创建目录
        for data_type in DOCUMENT_DATA_TYPES:
            collection = DOCUMENT_DATA_TYPES[data_type].get('collection')
            if collection:
                os.makedirs(os.path.join(self.json_dir, collection), exist_ok=True)
    
    def disconnect(self) -> bool:
        """
        断开数据库连接
        
        Returns:
            断开结果
        """
        try:
            if hasattr(self, 'client') and self.client:
                self.client.close()
                self.client = None
                self.connection = None
            
            self._log_info("MongoDB连接已断开")
            return True
        
        except Exception as e:
            self._log_error("断开MongoDB连接失败", e)
            return False
    
    def is_connected(self) -> bool:
        """
        检查数据库连接状态
        
        Returns:
            连接状态
        """
        if hasattr(self, 'client') and self.client:
            try:
                # 尝试执行简单查询
                self.client.server_info()
                return True
            except:
                return False
        
        # 如果使用JSON回退，则始终返回True
        return self.use_json_fallback
    
    def query(self, data_type: str, query_params: Dict[str, Any], options: Optional[Dict[str, Any]] = None) -> Any:
        """
        查询数据
        
        Args:
            data_type: 数据类型
            query_params: 查询参数
            options: 选项参数
        
        Returns:
            查询结果
        """
        options = options or {}
        
        if data_type not in DOCUMENT_DATA_TYPES:
            raise ValueError(f"未知的文档数据类型: {data_type}")
        
        collection = DOCUMENT_DATA_TYPES[data_type].get('collection')
        if not collection:
            raise ValueError(f"数据类型 {data_type} 没有指定集合名称")
        
        # 使用MongoDB或JSON存储查询数据
        if hasattr(self, 'connection') and self.connection:
            try:
                return self._query_mongodb(collection, query_params, options)
            except Exception as e:
                self._log_error(f"MongoDB查询失败: {collection}", e)
                if self.use_json_fallback:
                    return self._query_json(collection, query_params, options)
                raise
        elif self.use_json_fallback:
            return self._query_json(collection, query_params, options)
        else:
            raise ConnectionError("MongoDB未连接且未启用JSON回退")
    
    def _query_mongodb(self, collection: str, query_params: Dict[str, Any], options: Dict[str, Any]) -> Any:
        """
        使用MongoDB查询数据
        
        Args:
            collection: 集合名称
            query_params: 查询参数
            options: 选项参数
        
        Returns:
            查询结果
        """
        # 获取集合
        coll = self.connection[collection]
        
        # 构建查询条件
        query = {}
        for key, value in query_params.items():
            query[key] = value
        
        # 执行查询
        cursor = coll.find(query)
        
        # 应用排序
        if 'sort' in options:
            sort_fields = []
            for field, direction in options['sort'].items():
                sort_fields.append((field, direction))
            cursor = cursor.sort(sort_fields)
        
        # 应用限制
        if 'limit' in options:
            cursor = cursor.limit(options['limit'])
        
        # 转换为列表
        result = list(cursor)
        
        # 如果只有一条记录且options中指定了single=True，则返回单个记录
        if options.get('single', False) and len(result) == 1:
            return result[0]
        
        return result
    
    def _query_json(self, collection: str, query_params: Dict[str, Any], options: Dict[str, Any]) -> Any:
        """
        使用JSON查询数据
        
        Args:
            collection: 集合名称
            query_params: 查询参数
            options: 选项参数
        
        Returns:
            查询结果
        """
        # 构建集合目录路径
        collection_dir = os.path.join(self.json_dir, collection)
        
        # 确保目录存在
        if not os.path.exists(collection_dir):
            os.makedirs(collection_dir, exist_ok=True)
            return []
        
        # 获取所有JSON文件
        result = []
        for file_name in os.listdir(collection_dir):
            if file_name.endswith('.json'):
                file_path = os.path.join(collection_dir, file_name)
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    
                    # 检查是否匹配查询条件
                    match = True
                    for key, value in query_params.items():
                        if key not in data or data[key] != value:
                            match = False
                            break
                    
                    if match:
                        result.append(data)
                
                except Exception as e:
                    self._log_error(f"读取JSON文件失败: {file_path}", e)
        
        # 应用排序
        if 'sort' in options:
            for field, direction in options['sort'].items():
                reverse = direction == -1
                result.sort(key=lambda x: x.get(field, ''), reverse=reverse)
        
        # 应用限制
        if 'limit' in options:
            result = result[:options['limit']]
        
        # 如果只有一条记录且options中指定了single=True，则返回单个记录
        if options.get('single', False) and len(result) == 1:
            return result[0]
        
        return result
    
    def save(self, data_type: str, data: Any, options: Optional[Dict[str, Any]] = None) -> Any:
        """
        保存数据
        
        Args:
            data_type: 数据类型
            data: 要保存的数据
            options: 选项参数
        
        Returns:
            保存结果
        """
        options = options or {}
        
        if data_type not in DOCUMENT_DATA_TYPES:
            raise ValueError(f"未知的文档数据类型: {data_type}")
        
        collection = DOCUMENT_DATA_TYPES[data_type].get('collection')
        if not collection:
            raise ValueError(f"数据类型 {data_type} 没有指定集合名称")
        
        # 使用MongoDB或JSON存储保存数据
        if hasattr(self, 'connection') and self.connection:
            try:
                return self._save_mongodb(collection, data, options)
            except Exception as e:
                self._log_error(f"MongoDB保存失败: {collection}", e)
                if self.use_json_fallback:
                    return self._save_json(collection, data, options)
                raise
        elif self.use_json_fallback:
            return self._save_json(collection, data, options)
        else:
            raise ConnectionError("MongoDB未连接且未启用JSON回退")
    
    def _save_mongodb(self, collection: str, data: Any, options: Dict[str, Any]) -> Any:
        """
        使用MongoDB保存数据
        
        Args:
            collection: 集合名称
            data: 要保存的数据
            options: 选项参数
        
        Returns:
            保存结果
        """
        # 获取集合
        coll = self.connection[collection]
        
        # 处理单条数据或多条数据
        if isinstance(data, dict):
            # 检查是否为更新操作
            if '_id' in data:
                # 更新记录
                result = coll.replace_one({'_id': data['_id']}, data, upsert=True)
            else:
                # 插入记录
                result = coll.insert_one(data)
                # 更新数据，添加_id
                data['_id'] = result.inserted_id
            
            return data
        
        elif isinstance(data, list) and all(isinstance(item, dict) for item in data):
            # 批量插入
            result = coll.insert_many(data)
            
            # 更新数据，添加_id
            for i, item_id in enumerate(result.inserted_ids):
                data[i]['_id'] = item_id
            
            return data
        
        else:
            raise ValueError("数据必须是字典或字典列表")
    
    def _save_json(self, collection: str, data: Any, options: Dict[str, Any]) -> Any:
        """
        使用JSON保存数据
        
        Args:
            collection: 集合名称
            data: 要保存的数据
            options: 选项参数
        
        Returns:
            保存结果
        """
        # 构建集合目录路径
        collection_dir = os.path.join(self.json_dir, collection)
        
        # 确保目录存在
        os.makedirs(collection_dir, exist_ok=True)
        
        # 处理单条数据或多条数据
        if isinstance(data, dict):
            # 确保数据有ID
            if '_id' not in data:
                data['_id'] = str(datetime.now().timestamp())
            
            # 构建文件路径
            file_path = os.path.join(collection_dir, f"{data['_id']}.json")
            
            # 保存数据
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            return data
        
        elif isinstance(data, list) and all(isinstance(item, dict) for item in data):
            # 批量保存
            for item in data:
                # 确保数据有ID
                if '_id' not in item:
                    item['_id'] = str(datetime.now().timestamp())
                
                # 构建文件路径
                file_path = os.path.join(collection_dir, f"{item['_id']}.json")
                
                # 保存数据
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(item, f, ensure_ascii=False, indent=2)
            
            return data
        
        else:
            raise ValueError("数据必须是字典或字典列表")
    
    def delete(self, data_type: str, query_params: Dict[str, Any], options: Optional[Dict[str, Any]] = None) -> bool:
        """
        删除数据
        
        Args:
            data_type: 数据类型
            query_params: 查询参数
            options: 选项参数
        
        Returns:
            删除结果
        """
        options = options or {}
        
        if data_type not in DOCUMENT_DATA_TYPES:
            raise ValueError(f"未知的文档数据类型: {data_type}")
        
        collection = DOCUMENT_DATA_TYPES[data_type].get('collection')
        if not collection:
            raise ValueError(f"数据类型 {data_type} 没有指定集合名称")
        
        if not query_params:
            raise ValueError("删除操作必须指定查询参数")
        
        # 使用MongoDB或JSON存储删除数据
        if hasattr(self, 'connection') and self.connection:
            try:
                return self._delete_mongodb(collection, query_params, options)
            except Exception as e:
                self._log_error(f"MongoDB删除失败: {collection}", e)
                if self.use_json_fallback:
                    return self._delete_json(collection, query_params, options)
                raise
        elif self.use_json_fallback:
            return self._delete_json(collection, query_params, options)
        else:
            raise ConnectionError("MongoDB未连接且未启用JSON回退")
    
    def _delete_mongodb(self, collection: str, query_params: Dict[str, Any], options: Dict[str, Any]) -> bool:
        """
        使用MongoDB删除数据
        
        Args:
            collection: 集合名称
            query_params: 查询参数
            options: 选项参数
        
        Returns:
            删除结果
        """
        # 获取集合
        coll = self.connection[collection]
        
        # 构建查询条件
        query = {}
        for key, value in query_params.items():
            query[key] = value
        
        # 执行删除
        if options.get('multi', True):
            result = coll.delete_many(query)
            return result.deleted_count > 0
        else:
            result = coll.delete_one(query)
            return result.deleted_count > 0
    
    def _delete_json(self, collection: str, query_params: Dict[str, Any], options: Dict[str, Any]) -> bool:
        """
        使用JSON删除数据
        
        Args:
            collection: 集合名称
            query_params: 查询参数
            options: 选项参数
        
        Returns:
            删除结果
        """
        # 构建集合目录路径
        collection_dir = os.path.join(self.json_dir, collection)
        
        # 确保目录存在
        if not os.path.exists(collection_dir):
            return False
        
        # 获取所有JSON文件
        deleted = False
        for file_name in os.listdir(collection_dir):
            if file_name.endswith('.json'):
                file_path = os.path.join(collection_dir, file_name)
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    
                    # 检查是否匹配查询条件
                    match = True
                    for key, value in query_params.items():
                        if key not in data or data[key] != value:
                            match = False
                            break
                    
                    if match:
                        # 删除文件
                        os.remove(file_path)
                        deleted = True
                        
                        # 如果不是批量删除，则只删除一个
                        if not options.get('multi', True):
                            break
                
                except Exception as e:
                    self._log_error(f"读取或删除JSON文件失败: {file_path}", e)
        
        return deleted
