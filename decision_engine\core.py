"""
Decision Engine Core module.
Responsible for making trading decisions based on analysis from various engines.
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from concurrent.futures import ThreadPoolExecutor

from utils.logger import logger
from utils.config_loader import config_loader
from utils.error_utils import safe_execute
from utils.stock_utils import get_stock_list, get_industry_classification, get_stock_info

class DecisionEngine:
    """
    Class for making trading decisions.
    """
    
    def __init__(self, config=None, news_policy_fetcher=None, policy_analyzer=None, 
                 sentiment_analyzer=None, tiered_fund_flow_analyzer=None, 
                 volatility_analyzer=None, stock_info_manager=None):
        """
        Initialize the DecisionEngine.
        
        Args:
            config: Configuration object or None to use default.
            news_policy_fetcher: NewsPolicyFetcher instance.
            policy_analyzer: PolicyAnalyzer instance.
            sentiment_analyzer: SentimentAnalyzer instance.
            tiered_fund_flow_analyzer: TieredFundFlowAnalyzer instance.
            volatility_analyzer: VolatilityAnalyzer instance.
            stock_info_manager: StockInfoManager instance.
        """
        self.config = config if config else config_loader
        
        # Store engine instances
        self.news_policy_fetcher = news_policy_fetcher
        self.policy_analyzer = policy_analyzer
        self.sentiment_analyzer = sentiment_analyzer
        self.tiered_fund_flow_analyzer = tiered_fund_flow_analyzer
        self.volatility_analyzer = volatility_analyzer
        self.stock_info_manager = stock_info_manager
        
        # Load factor weights from configuration
        self.default_weights = self.config.get('decision_engine.weights.default', {
            'news_policy_score': 0.3,
            'tiered_flow_score': 0.4,
            'volatility_factor': 0.3
        })
        
        self.high_volatility_weights = self.config.get('decision_engine.weights.high_volatility_market', {
            'news_policy_score': 0.4,
            'tiered_flow_score': 0.4,
            'volatility_factor': 0.2
        })
        
        self.low_volatility_weights = self.config.get('decision_engine.weights.low_volatility_market', {
            'news_policy_score': 0.2,
            'tiered_flow_score': 0.5,
            'volatility_factor': 0.3
        })
        
        # Number of top stocks to recommend
        self.top_n = self.config.get('decision_engine.top_n', 30)
        
        logger.info("DecisionEngine initialized")
    
    @safe_execute(default_return={})
    def generate_stock_features(self, stock_code, stock_info, market_info):
        """
        Generate features for a stock.
        
        Args:
            stock_code (str): Stock code.
            stock_info (dict): Stock information.
            market_info (dict): Market information.
            
        Returns:
            dict: Dictionary containing stock features.
        """
        # Initialize result
        features = {
            'stock_code': stock_code,
            'stock_name': stock_info.get('stock_name', ''),
            'industry': stock_info.get('industry_name', ''),
            'market_cap': stock_info.get('market_cap', 0),
            'news_policy_score': 0.5,  # Default neutral score
            'tiered_flow_score': 0.5,  # Default neutral score
            'volatility_factor': 1.0,  # Default neutral factor
            'final_score': 0.5  # Default neutral score
        }
        
        # Get start and end dates for analysis
        end_date = datetime.now().date()
        start_date = end_date - timedelta(days=30)  # 30 days lookback
        
        # 1. News and Policy Analysis
        if self.news_policy_fetcher and self.policy_analyzer and self.sentiment_analyzer:
            try:
                # Get relevant news and policy documents
                market_info_df = self.news_policy_fetcher.get_market_information(days_lookback=7)
                
                if not market_info_df.empty:
                    # Process documents
                    stock_info_df = pd.DataFrame([{
                        'stock_code': stock_code,
                        'industry_name': stock_info.get('industry_name', '')
                    }])
                    
                    parsed_docs = self.policy_analyzer.batch_process_documents(market_info_df, stock_info_df)
                    
                    # Calculate sentiment scores for news
                    news_df = parsed_docs[parsed_docs['doc_type'] == 'news']
                    sentiment_scores = self.sentiment_analyzer.analyze_news_df(news_df)
                    
                    # Calculate stock-specific sentiment
                    stock_sentiment = self.sentiment_analyzer.get_stock_sentiment(stock_code, news_df)
                    
                    # Extract policy relevance for the stock
                    policy_relevance = 0.5  # Default neutral score
                    if 'stock_relevance' in parsed_docs.columns:
                        stock_relevances = [sr.get(stock_code, 0) for sr in parsed_docs['stock_relevance'] if sr]
                        if stock_relevances:
                            policy_relevance = np.mean(stock_relevances)
                            # Normalize to [0, 1]
                            policy_relevance = 0.5 + 0.5 * policy_relevance
                            policy_relevance = max(0, min(1, policy_relevance))
                    
                    # Combine sentiment and policy scores
                    sentiment_score = stock_sentiment.get('sentiment_score', 0)
                    # Normalize to [0, 1]
                    sentiment_score = 0.5 + 0.5 * sentiment_score
                    sentiment_score = max(0, min(1, sentiment_score))
                    
                    # Final news policy score
                    news_policy_score = 0.5 * sentiment_score + 0.5 * policy_relevance
                    features['news_policy_score'] = news_policy_score
            except Exception as e:
                logger.error(f"Error generating news policy features for {stock_code}: {str(e)}")
        
        # 2. Tiered Fund Flow Analysis
        if self.tiered_fund_flow_analyzer:
            try:
                # Get tiered fund flow data
                tiered_flow_data = self.tiered_fund_flow_analyzer.fetch_all_tiered_flows_for_stock(
                    stock_code, start_date, end_date
                )
                
                # Calculate tiered flow score
                tiered_flow_score = self.tiered_fund_flow_analyzer.calculate_stock_tiered_flow_score(
                    stock_code, tiered_flow_data, stock_info
                )
                
                features['tiered_flow_score'] = tiered_flow_score
            except Exception as e:
                logger.error(f"Error generating tiered fund flow features for {stock_code}: {str(e)}")
        
        # 3. Volatility Analysis
        if self.volatility_analyzer:
            try:
                # Get volatility profile
                volatility_profile = self.volatility_analyzer.get_stock_volatility_profile(
                    stock_code, start_date, end_date
                )
                
                # Calculate volatility adjustment factor
                volatility_factor = self._calculate_volatility_adjustment_factor(
                    volatility_profile, market_info.get('market_vol_regime', 'normal_volatility')
                )
                
                features['volatility_factor'] = volatility_factor
                
                # Add volatility metrics to features
                features.update({
                    'historical_vol_20d': volatility_profile.get('historical_vol_20d', 0),
                    'historical_vol_60d': volatility_profile.get('historical_vol_60d', 0),
                    'garch_forecast': volatility_profile.get('garch_forecast', 0),
                    'implied_vol': volatility_profile.get('implied_vol', 0)
                })
            except Exception as e:
                logger.error(f"Error generating volatility features for {stock_code}: {str(e)}")
        
        return features
    
    @safe_execute(default_return=1.0)
    def _calculate_volatility_adjustment_factor(self, volatility_profile, market_vol_regime):
        """
        Calculate volatility adjustment factor.
        
        Args:
            volatility_profile (dict): Volatility profile.
            market_vol_regime (str): Market volatility regime.
            
        Returns:
            float: Volatility adjustment factor.
        """
        # Get historical volatility
        historical_vol = volatility_profile.get('historical_vol_20d', 0)
        
        # Get adjustment factors from configuration
        adjustment_factors = self.config.get(f'engines.volatility.adjustment_factors.{market_vol_regime}', {})
        
        # Determine stock volatility category
        if historical_vol > self.config.get('engines.volatility.regime_thresholds.high_volatility', 0.3):
            stock_vol_category = 'high_stock_vol'
        elif historical_vol < self.config.get('engines.volatility.regime_thresholds.low_volatility', 0.15):
            stock_vol_category = 'low_stock_vol'
        else:
            stock_vol_category = 'normal_stock_vol'
        
        # Get adjustment factor
        adjustment = adjustment_factors.get(stock_vol_category, 0)
        
        # Calculate factor
        factor = 1.0 + adjustment
        
        return factor
    
    @safe_execute(default_return=0.5)
    def calculate_final_score(self, stock_features, market_info):
        """
        Calculate final score for a stock.
        
        Args:
            stock_features (dict): Stock features.
            market_info (dict): Market information.
            
        Returns:
            float: Final score.
        """
        # Get weights based on market volatility regime
        market_vol_regime = market_info.get('market_vol_regime', 'normal_volatility')
        
        if market_vol_regime == 'high_volatility':
            weights = self.high_volatility_weights
        elif market_vol_regime == 'low_volatility':
            weights = self.low_volatility_weights
        else:
            weights = self.default_weights
        
        # Get scores
        news_policy_score = stock_features.get('news_policy_score', 0.5)
        tiered_flow_score = stock_features.get('tiered_flow_score', 0.5)
        volatility_factor = stock_features.get('volatility_factor', 1.0)
        
        # Calculate weighted score
        weighted_score = (
            weights.get('news_policy_score', 0.3) * news_policy_score +
            weights.get('tiered_flow_score', 0.4) * tiered_flow_score
        )
        
        # Apply volatility factor
        final_score = weighted_score * volatility_factor
        
        # Ensure score is in [0, 1] range
        final_score = max(0, min(1, final_score))
        
        return final_score
    
    def get_recommendations(self, stock_pool=None, top_n=None):
        """
        Get stock recommendations.
        
        Args:
            stock_pool (list or pandas.DataFrame, optional): Stock pool to analyze.
            top_n (int, optional): Number of top stocks to recommend.
            
        Returns:
            pandas.DataFrame: DataFrame containing recommended stocks.
        """
        if top_n is None:
            top_n = self.top_n
        
        # Get stock pool if not provided
        if stock_pool is None:
            stock_pool = get_stock_list()
            
            # Filter out ST stocks
            if isinstance(stock_pool, pd.DataFrame) and 'stock_name' in stock_pool.columns:
                stock_pool = stock_pool[~stock_pool['stock_name'].str.contains('ST')]
        
        # Convert to DataFrame if it's a list
        if isinstance(stock_pool, list):
            stock_pool = pd.DataFrame({'stock_code': stock_pool})
        
        # Get industry classification
        industry_df = get_industry_classification()
        
        # Merge with stock pool
        if 'industry_name' not in stock_pool.columns and 'stock_code' in stock_pool.columns:
            stock_pool = stock_pool.merge(industry_df[['stock_code', 'industry_name']], on='stock_code', how='left')
        
        # Analyze market
        market_info = self._analyze_market()
        
        # Process each stock
        results = []
        
        # Use ThreadPoolExecutor for parallel processing
        with ThreadPoolExecutor(max_workers=10) as executor:
            futures = []
            
            for _, row in stock_pool.iterrows():
                stock_code = row['stock_code']
                
                # Get stock info
                stock_info = {
                    'stock_code': stock_code,
                    'stock_name': row.get('stock_name', ''),
                    'industry_name': row.get('industry_name', ''),
                    'market_cap': row.get('market_cap', 0)
                }
                
                # Submit task to executor
                future = executor.submit(self._process_stock, stock_code, stock_info, market_info)
                futures.append(future)
            
            # Collect results
            for future in futures:
                try:
                    result = future.result()
                    if result:
                        results.append(result)
                except Exception as e:
                    logger.error(f"Error processing stock: {str(e)}")
        
        # Convert to DataFrame
        if not results:
            return pd.DataFrame()
        
        result_df = pd.DataFrame(results)
        
        # Sort by final score (descending)
        result_df = result_df.sort_values('final_score', ascending=False)
        
        # Get top N stocks
        if len(result_df) > top_n:
            result_df = result_df.head(top_n)
        
        return result_df
    
    @safe_execute(default_return=None)
    def _process_stock(self, stock_code, stock_info, market_info):
        """
        Process a stock.
        
        Args:
            stock_code (str): Stock code.
            stock_info (dict): Stock information.
            market_info (dict): Market information.
            
        Returns:
            dict: Stock features with final score.
        """
        # Generate features
        features = self.generate_stock_features(stock_code, stock_info, market_info)
        
        # Calculate final score
        final_score = self.calculate_final_score(features, market_info)
        
        # Update final score
        features['final_score'] = final_score
        
        return features
    
    @safe_execute(default_return={})
    def _analyze_market(self):
        """
        Analyze market conditions.
        
        Returns:
            dict: Market information.
        """
        # Initialize result
        market_info = {
            'date': datetime.now().date(),
            'market_vol_regime': 'normal_volatility',
            'market_volatility': 0.0,
            'market_trend': 'neutral',
            'market_sentiment': 'neutral'
        }
        
        # Get market volatility regime
        if self.volatility_analyzer:
            try:
                # Get market volatility regime
                market_vol_info = self.volatility_analyzer.analyze_market_volatility_regime()
                
                # Update market info
                market_info.update(market_vol_info)
            except Exception as e:
                logger.error(f"Error analyzing market volatility: {str(e)}")
        
        # Get market sentiment
        if self.news_policy_fetcher and self.sentiment_analyzer:
            try:
                # Get market news
                market_news_df = self.news_policy_fetcher.get_market_information(days_lookback=7, include_policy_docs=False, include_policy_rss=False)
                
                # Analyze sentiment
                market_sentiment = self.sentiment_analyzer.get_market_sentiment(market_news_df)
                
                # Update market info
                market_info['market_sentiment'] = market_sentiment.get('sentiment_trend', 'neutral')
                market_info['market_sentiment_score'] = market_sentiment.get('market_sentiment_score', 0)
            except Exception as e:
                logger.error(f"Error analyzing market sentiment: {str(e)}")
        
        return market_info
