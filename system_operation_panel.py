#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
系统操作面板
用于执行各个模块的功能，支持单模块执行和多模块集成执行
"""

import os
import sys
import json
import time
import logging
import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox, filedialog
from datetime import datetime, timedelta
import threading
import queue
import traceback

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入核心模块
from core.module_interface import ModuleInterface
from core.data_storage import DataStorage, StorageLevel
from core.scheduler import CentralScheduler as Scheduler

# 导入功能模块
try:
    from data_sources.policy_analyzer import PolicyAnalyzer
    POLICY_ANALYZER_AVAILABLE = True
except ImportError:
    POLICY_ANALYZER_AVAILABLE = False
    print("警告: PolicyAnalyzer 模块不可用")

try:
    from data_sources.news_monitor import NewsMonitor
    NEWS_MONITOR_AVAILABLE = True
except ImportError:
    NEWS_MONITOR_AVAILABLE = False
    print("警告: NewsMonitor 模块不可用")

try:
    from data_sources.fund_flow_analyzer import FundFlowAnalyzer
    FUND_FLOW_ANALYZER_AVAILABLE = True
except ImportError:
    FUND_FLOW_ANALYZER_AVAILABLE = False
    print("警告: FundFlowAnalyzer 模块不可用")

try:
    from data_sources.volatility_analyzer import VolatilityAnalyzer
    VOLATILITY_ANALYZER_AVAILABLE = True
except ImportError:
    VOLATILITY_ANALYZER_AVAILABLE = False
    print("警告: VolatilityAnalyzer 模块不可用")

try:
    from data_sources.sentiment_resonance import SentimentResonanceModel
    SENTIMENT_RESONANCE_AVAILABLE = True
except ImportError:
    SENTIMENT_RESONANCE_AVAILABLE = False
    print("警告: SentimentResonanceModel 模块不可用")

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('logs/system_operation_panel.log')
    ]
)
logger = logging.getLogger("SystemOperationPanel")

class SystemOperationPanel:
    """系统操作面板类"""

    def __init__(self):
        """初始化系统操作面板"""
        self.root = tk.Tk()
        self.root.title("金融市场分析系统 - 操作面板")
        self.root.geometry("1200x800")
        self.root.minsize(1000, 700)

        # 创建数据存储
        self.data_storage = DataStorage()

        # 创建任务调度器
        self.scheduler = Scheduler()

        # 创建模块实例
        self.modules = {}
        self.init_modules()

        # 创建任务队列
        self.task_queue = queue.Queue()

        # 创建线程事件
        self.stop_event = threading.Event()

        # 创建工作线程
        self.worker_thread = None

        # 创建UI组件
        self.create_widgets()

        # 启动工作线程
        self.start_worker_thread()

    def init_modules(self):
        """初始化各个模块"""
        # 政策分析模块
        if POLICY_ANALYZER_AVAILABLE:
            try:
                self.modules['policy_analyzer'] = PolicyAnalyzer()
                # 设置数据存储对象
                self.modules['policy_analyzer'].data_storage = self.data_storage
                logger.info("政策分析模块初始化成功")
            except Exception as e:
                logger.error(f"政策分析模块初始化失败: {str(e)}")

        # 新闻监控模块
        if NEWS_MONITOR_AVAILABLE:
            try:
                self.modules['news_monitor'] = NewsMonitor()
                # 设置数据存储对象
                self.modules['news_monitor'].data_storage = self.data_storage
                logger.info("新闻监控模块初始化成功")
            except Exception as e:
                logger.error(f"新闻监控模块初始化失败: {str(e)}")

        # 资金流分析模块
        if FUND_FLOW_ANALYZER_AVAILABLE:
            try:
                self.modules['fund_flow_analyzer'] = FundFlowAnalyzer()
                # 设置数据存储对象
                self.modules['fund_flow_analyzer'].data_storage = self.data_storage
                logger.info("资金流分析模块初始化成功")
            except Exception as e:
                logger.error(f"资金流分析模块初始化失败: {str(e)}")

        # 波动率分析模块
        if VOLATILITY_ANALYZER_AVAILABLE:
            try:
                self.modules['volatility_analyzer'] = VolatilityAnalyzer()
                # 设置数据存储对象
                self.modules['volatility_analyzer'].data_storage = self.data_storage
                logger.info("波动率分析模块初始化成功")
            except Exception as e:
                logger.error(f"波动率分析模块初始化失败: {str(e)}")

        # 情绪共振模型
        if SENTIMENT_RESONANCE_AVAILABLE:
            try:
                self.modules['sentiment_resonance'] = SentimentResonanceModel()
                # 设置数据存储对象
                self.modules['sentiment_resonance'].data_storage = self.data_storage
                logger.info("情绪共振模型初始化成功")
            except Exception as e:
                logger.error(f"情绪共振模型初始化失败: {str(e)}")

    def create_widgets(self):
        """创建UI组件"""
        # 创建主框架
        self.main_frame = ttk.Frame(self.root, padding="10")
        self.main_frame.pack(fill=tk.BOTH, expand=True)

        # 创建顶部框架
        self.top_frame = ttk.Frame(self.main_frame)
        self.top_frame.pack(fill=tk.X, pady=(0, 10))

        # 创建标题标签
        title_label = ttk.Label(self.top_frame, text="金融市场分析系统", font=("Arial", 16, "bold"))
        title_label.pack(side=tk.LEFT, padx=5)

        # 创建状态标签
        self.status_label = ttk.Label(self.top_frame, text="就绪", font=("Arial", 10))
        self.status_label.pack(side=tk.RIGHT, padx=5)

        # 创建内容框架
        self.content_frame = ttk.Frame(self.main_frame)
        self.content_frame.pack(fill=tk.BOTH, expand=True)

        # 创建左侧模块选择框架
        self.module_frame = ttk.LabelFrame(self.content_frame, text="模块选择", padding="10")
        self.module_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))

        # 创建模块选择列表
        self.module_listbox = tk.Listbox(self.module_frame, selectmode=tk.MULTIPLE, width=25)
        self.module_listbox.pack(fill=tk.BOTH, expand=True)

        # 添加模块到列表
        self.module_names = {
            'policy_analyzer': '政策分析模块',
            'news_monitor': '新闻监控模块',
            'fund_flow_analyzer': '资金流分析模块',
            'volatility_analyzer': '波动率分析模块',
            'sentiment_resonance': '情绪共振模型'
        }

        for module_id, module_name in self.module_names.items():
            if module_id in self.modules:
                self.module_listbox.insert(tk.END, module_name)
            else:
                self.module_listbox.insert(tk.END, f"{module_name} (不可用)")

        # 创建右侧操作框架
        self.operation_frame = ttk.Frame(self.content_frame)
        self.operation_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # 创建参数配置框架
        self.param_frame = ttk.LabelFrame(self.operation_frame, text="参数配置", padding="10")
        self.param_frame.pack(fill=tk.X, pady=(0, 10))

        # 创建日期范围选择
        date_frame = ttk.Frame(self.param_frame)
        date_frame.pack(fill=tk.X, pady=5)

        ttk.Label(date_frame, text="开始日期:").pack(side=tk.LEFT, padx=(0, 5))
        self.start_date_var = tk.StringVar(value=(datetime.now() - timedelta(days=30)).strftime("%Y-%m-%d"))
        ttk.Entry(date_frame, textvariable=self.start_date_var, width=12).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Label(date_frame, text="结束日期:").pack(side=tk.LEFT, padx=(0, 5))
        self.end_date_var = tk.StringVar(value=datetime.now().strftime("%Y-%m-%d"))
        ttk.Entry(date_frame, textvariable=self.end_date_var, width=12).pack(side=tk.LEFT)

        # 创建股票代码输入
        stock_frame = ttk.Frame(self.param_frame)
        stock_frame.pack(fill=tk.X, pady=5)

        ttk.Label(stock_frame, text="股票代码:").pack(side=tk.LEFT, padx=(0, 5))
        self.stock_code_var = tk.StringVar()
        ttk.Entry(stock_frame, textvariable=self.stock_code_var, width=12).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Label(stock_frame, text="(多个代码用逗号分隔)").pack(side=tk.LEFT)

        # 创建行业选择
        sector_frame = ttk.Frame(self.param_frame)
        sector_frame.pack(fill=tk.X, pady=5)

        ttk.Label(sector_frame, text="行业:").pack(side=tk.LEFT, padx=(0, 5))
        self.sector_var = tk.StringVar()
        ttk.Combobox(sector_frame, textvariable=self.sector_var, values=["全部", "银行", "保险", "证券", "房地产", "医药", "计算机", "电子", "通信", "汽车", "食品饮料"]).pack(side=tk.LEFT, padx=(0, 10))

        # 创建执行按钮框架
        button_frame = ttk.Frame(self.operation_frame)
        button_frame.pack(fill=tk.X, pady=(0, 10))

        # 创建执行按钮
        ttk.Button(button_frame, text="执行选中模块", command=self.execute_selected_modules).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="停止执行", command=self.stop_execution).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="清空日志", command=self.clear_log).pack(side=tk.LEFT)

        # 创建定时任务框架
        schedule_frame = ttk.Frame(self.operation_frame)
        schedule_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(schedule_frame, text="定时执行:").pack(side=tk.LEFT, padx=(0, 5))
        self.schedule_var = tk.StringVar()
        ttk.Combobox(schedule_frame, textvariable=self.schedule_var, values=["不定时", "每天", "每小时", "每30分钟"]).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Label(schedule_frame, text="时间:").pack(side=tk.LEFT, padx=(0, 5))
        self.schedule_time_var = tk.StringVar(value="09:30")
        ttk.Entry(schedule_frame, textvariable=self.schedule_time_var, width=8).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(schedule_frame, text="设置定时任务", command=self.set_scheduled_task).pack(side=tk.LEFT)

        # 创建日志框架
        log_frame = ttk.LabelFrame(self.operation_frame, text="执行日志", padding="10")
        log_frame.pack(fill=tk.BOTH, expand=True)

        # 创建日志文本框
        self.log_text = scrolledtext.ScrolledText(log_frame, wrap=tk.WORD)
        self.log_text.pack(fill=tk.BOTH, expand=True)
        self.log_text.config(state=tk.DISABLED)

        # 创建底部框架
        self.bottom_frame = ttk.Frame(self.main_frame)
        self.bottom_frame.pack(fill=tk.X, pady=(10, 0))

        # 创建版本标签
        version_label = ttk.Label(self.bottom_frame, text="版本: 1.0.0")
        version_label.pack(side=tk.LEFT, padx=5)

        # 创建退出按钮
        ttk.Button(self.bottom_frame, text="退出", command=self.exit_application).pack(side=tk.RIGHT, padx=5)

    def start_worker_thread(self):
        """启动工作线程"""
        if self.worker_thread is None or not self.worker_thread.is_alive():
            self.stop_event.clear()
            self.worker_thread = threading.Thread(target=self.worker_loop, daemon=True)
            self.worker_thread.start()
            logger.info("工作线程已启动")

    def worker_loop(self):
        """工作线程循环"""
        while not self.stop_event.is_set():
            try:
                # 尝试从队列获取任务，超时1秒
                task = self.task_queue.get(timeout=1)

                # 执行任务
                self.execute_task(task)

                # 标记任务完成
                self.task_queue.task_done()
            except queue.Empty:
                # 队列为空，继续等待
                pass
            except Exception as e:
                logger.error(f"工作线程异常: {str(e)}")
                self.log_message(f"错误: {str(e)}")

    def execute_task(self, task):
        """执行任务"""
        task_type = task.get('type')
        module_id = task.get('module_id')
        params = task.get('params', {})

        if task_type == 'execute_module':
            if module_id in self.modules:
                module = self.modules[module_id]
                module_name = self.module_names[module_id]

                self.log_message(f"开始执行 {module_name}...")
                self.update_status(f"正在执行 {module_name}")

                try:
                    # 根据模块类型执行不同的方法
                    result = self.execute_module_method(module_id, module, params)

                    if result:
                        status = result.get('status', 'unknown')
                        message = result.get('message', '无消息')

                        if status == 'success':
                            self.log_message(f"{module_name} 执行成功: {message}")
                        else:
                            self.log_message(f"{module_name} 执行警告: {message}")
                    else:
                        self.log_message(f"{module_name} 执行完成，但未返回结果")

                except Exception as e:
                    error_msg = f"{module_name} 执行失败: {str(e)}"
                    logger.error(error_msg)
                    self.log_message(f"错误: {error_msg}")
                    self.log_message(f"详细错误: {traceback.format_exc()}")
            else:
                self.log_message(f"错误: 模块 {module_id} 不可用")

        elif task_type == 'scheduled_task':
            self.log_message(f"执行定时任务: {task.get('name', '未命名任务')}")
            modules_to_execute = task.get('modules', [])

            for module_id in modules_to_execute:
                if module_id in self.modules:
                    new_task = {
                        'type': 'execute_module',
                        'module_id': module_id,
                        'params': params
                    }
                    self.task_queue.put(new_task)

        self.update_status("就绪")

    def execute_module_method(self, module_id, module, params):
        """根据模块ID执行相应的方法"""
        # 提取参数
        start_date = params.get('start_date', '')
        end_date = params.get('end_date', '')
        stock_codes = params.get('stock_codes', [])
        sector = params.get('sector', '')

        # 政策分析模块
        if module_id == 'policy_analyzer':
            return module.fetch_and_parse_policies()

        # 新闻监控模块
        elif module_id == 'news_monitor':
            return module.fetch_and_process_news()

        # 资金流分析模块
        elif module_id == 'fund_flow_analyzer':
            # 北向资金分析
            north_result = module.fetch_northbound_flow()
            self.log_message(f"北向资金分析结果: {north_result.get('message', '')}")

            # 如果提供了股票代码，执行个股资金流分析
            if stock_codes:
                stock_result = module.fetch_stock_fund_flow(stock_codes=stock_codes)
                self.log_message(f"个股资金流分析结果: {stock_result.get('message', '')}")

            # 如果提供了行业，执行行业资金流分析
            if sector and sector != "全部":
                sector_result = module.fetch_sector_fund_flow(sectors=[sector])
                self.log_message(f"行业资金流分析结果: {sector_result.get('message', '')}")

            # 执行游资行为分析
            hot_money_result = module.analyze_hot_money_behavior()
            self.log_message(f"游资行为分析结果: {hot_money_result.get('message', '')}")

            return north_result  # 返回北向资金分析结果作为主要结果

        # 波动率分析模块
        elif module_id == 'volatility_analyzer':
            # 市场波动率分析
            market_result = module.calculate_market_volatility()
            self.log_message(f"市场波动率分析结果: {market_result.get('message', '')}")

            # 行业波动率分析
            if sector and sector != "全部":
                sector_result = module.calculate_sector_volatility(sectors=[sector])
                self.log_message(f"行业波动率分析结果: {sector_result.get('message', '')}")

            # 个股波动率分析
            if stock_codes:
                stock_result = module.calculate_stock_volatility(stock_codes=stock_codes)
                self.log_message(f"个股波动率分析结果: {stock_result.get('message', '')}")

            # 政策波动率溢价分析
            premium_result = module.calculate_policy_volatility_premium()
            self.log_message(f"政策波动率溢价分析结果: {premium_result.get('message', '')}")

            # 资金流-波动率耦合分析
            coupling_result = module.calculate_fund_flow_volatility_coupling()
            self.log_message(f"资金流-波动率耦合分析结果: {coupling_result.get('message', '')}")

            # 生成波动率分析报告
            report_result = module.generate_volatility_report()
            self.log_message(f"波动率分析报告生成结果: {report_result.get('message', '')}")

            return report_result  # 返回报告生成结果作为主要结果

        # 情绪共振模型
        elif module_id == 'sentiment_resonance':
            return module.analyze_sentiment_resonance()

        # 未知模块
        else:
            self.log_message(f"警告: 未知模块类型 {module_id}")
            return None

    def execute_selected_modules(self):
        """执行选中的模块"""
        # 获取选中的模块索引
        selected_indices = self.module_listbox.curselection()

        if not selected_indices:
            messagebox.showwarning("警告", "请至少选择一个模块")
            return

        # 获取选中的模块ID
        selected_modules = []
        for index in selected_indices:
            module_name = self.module_listbox.get(index)

            # 检查模块是否可用
            if "(不可用)" in module_name:
                messagebox.showwarning("警告", f"模块 {module_name} 不可用")
                continue

            # 查找模块ID
            for module_id, name in self.module_names.items():
                if name == module_name and module_id in self.modules:
                    selected_modules.append(module_id)
                    break

        if not selected_modules:
            messagebox.showwarning("警告", "没有可用的模块被选中")
            return

        # 获取参数
        params = self.get_execution_params()

        # 将任务添加到队列
        for module_id in selected_modules:
            task = {
                'type': 'execute_module',
                'module_id': module_id,
                'params': params
            }
            self.task_queue.put(task)

        self.log_message(f"已添加 {len(selected_modules)} 个模块到执行队列")

    def get_execution_params(self):
        """获取执行参数"""
        # 获取日期范围
        start_date = self.start_date_var.get()
        end_date = self.end_date_var.get()

        # 获取股票代码
        stock_code_text = self.stock_code_var.get()
        stock_codes = [code.strip() for code in stock_code_text.split(',') if code.strip()]

        # 获取行业
        sector = self.sector_var.get()

        return {
            'start_date': start_date,
            'end_date': end_date,
            'stock_codes': stock_codes,
            'sector': sector
        }

    def stop_execution(self):
        """停止执行"""
        # 清空任务队列
        while not self.task_queue.empty():
            try:
                self.task_queue.get_nowait()
                self.task_queue.task_done()
            except queue.Empty:
                break

        self.log_message("已停止所有任务")
        self.update_status("就绪")

    def set_scheduled_task(self):
        """设置定时任务"""
        # 获取定时类型
        schedule_type = self.schedule_var.get()

        if schedule_type == "不定时":
            messagebox.showinfo("提示", "已取消定时任务")
            return

        # 获取选中的模块
        selected_indices = self.module_listbox.curselection()

        if not selected_indices:
            messagebox.showwarning("警告", "请至少选择一个模块")
            return

        # 获取选中的模块ID
        selected_modules = []
        for index in selected_indices:
            module_name = self.module_listbox.get(index)

            # 检查模块是否可用
            if "(不可用)" in module_name:
                continue

            # 查找模块ID
            for module_id, name in self.module_names.items():
                if name == module_name and module_id in self.modules:
                    selected_modules.append(module_id)
                    break

        if not selected_modules:
            messagebox.showwarning("警告", "没有可用的模块被选中")
            return

        # 获取定时时间
        schedule_time = self.schedule_time_var.get()

        try:
            # 解析时间
            hour, minute = map(int, schedule_time.split(':'))

            if hour < 0 or hour > 23 or minute < 0 or minute > 59:
                raise ValueError("时间格式不正确")
        except:
            messagebox.showerror("错误", "时间格式不正确，请使用HH:MM格式")
            return

        # 创建定时任务
        task_name = f"定时任务 ({schedule_type} {schedule_time})"

        # 获取参数
        params = self.get_execution_params()

        # 创建任务
        task = {
            'type': 'scheduled_task',
            'name': task_name,
            'modules': selected_modules,
            'params': params,
            'schedule_type': schedule_type,
            'schedule_time': schedule_time
        }

        # 添加到调度器
        if schedule_type == "每天":
            # 使用 CentralScheduler 的 schedule_task 方法
            self.scheduler.schedule_task(
                task_type="scheduled_task",
                module="system_operation_panel",
                function="execute_scheduled_task",
                params={"task": task, "hour": hour, "minute": minute},
                schedule_time=datetime.now().replace(hour=hour, minute=minute)
            )
            self.log_message(f"已设置每天 {schedule_time} 执行所选模块")
        elif schedule_type == "每小时":
            # 使用 CentralScheduler 的 schedule_task 方法
            self.scheduler.schedule_task(
                task_type="scheduled_task",
                module="system_operation_panel",
                function="execute_scheduled_task",
                params={"task": task, "minute": minute},
                schedule_time=datetime.now().replace(minute=minute)
            )
            self.log_message(f"已设置每小时的 {minute} 分执行所选模块")
        elif schedule_type == "每30分钟":
            # 使用 CentralScheduler 的 schedule_task 方法
            self.scheduler.schedule_task(
                task_type="scheduled_task",
                module="system_operation_panel",
                function="execute_scheduled_task",
                params={"task": task, "interval": 30 * 60},
                schedule_time=datetime.now()
            )
            self.log_message(f"已设置每30分钟执行所选模块")

        messagebox.showinfo("提示", f"已设置{task_name}")

    def log_message(self, message):
        """记录日志消息"""
        # 获取当前时间
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # 格式化消息
        formatted_message = f"[{current_time}] {message}\n"

        # 将消息添加到日志文本框
        self.log_text.config(state=tk.NORMAL)
        self.log_text.insert(tk.END, formatted_message)
        self.log_text.see(tk.END)
        self.log_text.config(state=tk.DISABLED)

        # 同时记录到日志文件
        logger.info(message)

    def update_status(self, status):
        """更新状态标签"""
        self.status_label.config(text=status)
        self.root.update_idletasks()

    def clear_log(self):
        """清空日志"""
        self.log_text.config(state=tk.NORMAL)
        self.log_text.delete(1.0, tk.END)
        self.log_text.config(state=tk.DISABLED)

    def exit_application(self):
        """退出应用程序"""
        # 停止工作线程
        self.stop_event.set()

        if self.worker_thread and self.worker_thread.is_alive():
            self.worker_thread.join(timeout=1)

        # 退出应用
        self.root.quit()

    def run(self):
        """运行应用程序"""
        # 启动主循环
        self.root.mainloop()


if __name__ == "__main__":
    # 创建日志目录
    os.makedirs("logs", exist_ok=True)

    # 创建并运行操作面板
    panel = SystemOperationPanel()
    panel.run()
