"""
波动率分析模块

提供政策波动率溢价模型、资金流-波动率耦合分析和A股特色波动率模型
"""

import os
import logging
import json
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Any, Tuple
import math
import akshare as ak

# 导入模块接口
from core.module_interface import ModuleInterface

# 导入数据存储
from core.data_storage import DataStorage, StorageLevel

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/volatility_analyzer.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('volatility_analyzer')

class VolatilityAnalyzer(ModuleInterface):
    """波动率分析模块类"""

    def __init__(self):
        """初始化波动率分析模块"""
        super().__init__(module_name='volatility_analyzer')

        # 创建数据存储
        self.data_storage = DataStorage()

        # 上次更新时间
        self.last_update_time = datetime.now()

        # 波动率数据
        self.volatility_data = self.data_storage.load('volatility_analyzer', 'volatility_data', {
            'market': {},
            'sector': {},
            'stock': {},
            'policy_premium': {},
            'fund_flow_coupling': {},
            'signals': []
        })

        # 股票代码映射
        self.stock_code_map = self._load_stock_code_map()

        # 行业板块映射
        self.sector_map = self._load_sector_map()

        # 波动率模型参数
        self.volatility_params = {
            'historical_window': 20,  # 历史波动率窗口(天)
            'realized_window': 5,  # 已实现波动率窗口(天)
            'policy_impact_window': 10,  # 政策影响窗口(天)
            'fund_flow_window': 5,  # 资金流窗口(天)
            'volatility_threshold': 0.3,  # 波动率阈值
            'premium_threshold': 0.2,  # 溢价阈值
            'coupling_threshold': 0.6  # 耦合阈值
        }

        logger.info("波动率分析模块初始化完成")

    def _load_stock_code_map(self) -> Dict[str, str]:
        """
        加载股票代码映射

        Returns:
            stock_code_map: 股票代码映射
        """
        # 尝试从数据存储加载
        stock_map = self.data_storage.load('volatility_analyzer', 'stock_code_map')
        if stock_map:
            logger.info(f"从数据存储加载了{len(stock_map)}个股票代码")
            return stock_map

        # 如果数据存储中没有，尝试从文件加载
        try:
            stock_file = os.path.join('data', 'volatility_analyzer', 'stock_code_map.json')
            if os.path.exists(stock_file):
                with open(stock_file, 'r', encoding='utf-8') as f:
                    stock_map = json.load(f)
                logger.info(f"从文件加载了{len(stock_map)}个股票代码")

                # 保存到数据存储
                self.data_storage.save('volatility_analyzer', 'stock_code_map', stock_map)

                return stock_map
        except Exception as e:
            logger.error(f"加载股票代码映射文件失败: {str(e)}")

        # 如果都没有，尝试从akshare获取
        try:
            # 获取A股股票代码和名称
            stock_info_df = ak.stock_info_a_code_name()

            # 创建映射
            stock_map = {}
            for _, row in stock_info_df.iterrows():
                code = row['code']
                name = row['name']
                stock_map[name] = code

            logger.info(f"从akshare获取了{len(stock_map)}个股票代码")

            # 保存到数据存储
            self.data_storage.save('volatility_analyzer', 'stock_code_map', stock_map)

            return stock_map

        except Exception as e:
            logger.error(f"从akshare获取股票代码失败: {str(e)}")

        # 如果都失败了，使用默认映射
        stock_map = {
            '上证指数': '000001',
            '深证成指': '399001',
            '创业板指': '399006',
            '沪深300': '000300',
            '中证500': '000905',
            '科创50': '000688',
            '贵州茅台': '600519',
            '宁德时代': '300750',
            '招商银行': '600036',
            '中国平安': '601318'
        }

        # 保存到数据存储
        self.data_storage.save('volatility_analyzer', 'stock_code_map', stock_map)

        logger.info(f"使用默认股票代码映射，包含{len(stock_map)}个股票")
        return stock_map

    def _load_sector_map(self) -> Dict[str, List[str]]:
        """
        加载行业板块映射

        Returns:
            sector_map: 行业板块映射
        """
        # 尝试从数据存储加载
        sector_map = self.data_storage.load('volatility_analyzer', 'sector_map')
        if sector_map:
            logger.info(f"从数据存储加载了{len(sector_map)}个行业板块")
            return sector_map

        # 如果数据存储中没有，尝试从文件加载
        try:
            sector_file = os.path.join('data', 'volatility_analyzer', 'sector_map.json')
            if os.path.exists(sector_file):
                with open(sector_file, 'r', encoding='utf-8') as f:
                    sector_map = json.load(f)
                logger.info(f"从文件加载了{len(sector_map)}个行业板块")

                # 保存到数据存储
                self.data_storage.save('volatility_analyzer', 'sector_map', sector_map)

                return sector_map
        except Exception as e:
            logger.error(f"加载行业板块映射文件失败: {str(e)}")

        # 如果都没有，使用默认映射
        sector_map = {
            '白酒': ['贵州茅台', '五粮液', '泸州老窖', '洋河股份', '山西汾酒'],
            '新能源': ['宁德时代', '比亚迪', '隆基绿能', '阳光电源', '亿纬锂能'],
            '银行': ['招商银行', '工商银行', '建设银行', '农业银行', '中国银行'],
            '保险': ['中国平安', '中国人寿', '中国太保', '新华保险', '中国人保'],
            '家电': ['美的集团', '格力电器', '海尔智家', '海信家电', '老板电器'],
            '医药': ['恒瑞医药', '迈瑞医疗', '药明康德', '爱尔眼科', '通策医疗'],
            '科技': ['腾讯控股', '阿里巴巴', '百度', '京东', '网易'],
            '半导体': ['中芯国际', '韦尔股份', '北方华创', '兆易创新', '紫光国微']
        }

        # 保存到数据存储
        self.data_storage.save('volatility_analyzer', 'sector_map', sector_map)

        logger.info(f"使用默认行业板块映射，包含{len(sector_map)}个行业")
        return sector_map

    def initialize(self):
        """初始化模块"""
        logger.info("初始化波动率分析模块...")

        # 注册定时任务
        if self.scheduler:
            # 每天计算市场波动率
            self.schedule_task(
                function='calculate_market_volatility',
                params={},
                priority='medium',
                schedule_time=datetime.now() + timedelta(minutes=5)
            )

            # 每天计算行业波动率
            self.schedule_task(
                function='calculate_sector_volatility',
                params={},
                priority='medium',
                schedule_time=datetime.now() + timedelta(minutes=10)
            )

            # 每天计算个股波动率
            self.schedule_task(
                function='calculate_stock_volatility',
                params={},
                priority='medium',
                schedule_time=datetime.now() + timedelta(minutes=15)
            )

            # 每天计算政策波动率溢价
            self.schedule_task(
                function='calculate_policy_volatility_premium',
                params={},
                priority='medium',
                schedule_time=datetime.now() + timedelta(minutes=20)
            )

            # 每天计算资金流-波动率耦合
            self.schedule_task(
                function='calculate_fund_flow_volatility_coupling',
                params={},
                priority='medium',
                schedule_time=datetime.now() + timedelta(minutes=25)
            )

            # 每周生成波动率分析报告
            self.schedule_task(
                function='generate_volatility_report',
                params={},
                priority='low',
                schedule_time=datetime.now() + timedelta(days=1)
            )

        logger.info("波动率分析模块初始化完成")

    def get_status(self) -> Dict[str, Any]:
        """获取模块状态"""
        return {
            'module_name': self.module_name,
            'enabled': self.config.get('enabled', True),
            'last_update_time': self.last_update_time.isoformat() if self.last_update_time else None,
            'market_volatility_count': len(self.volatility_data.get('market', {})),
            'sector_volatility_count': len(self.volatility_data.get('sector', {})),
            'stock_volatility_count': len(self.volatility_data.get('stock', {})),
            'policy_premium_count': len(self.volatility_data.get('policy_premium', {})),
            'fund_flow_coupling_count': len(self.volatility_data.get('fund_flow_coupling', {})),
            'signals_count': len(self.volatility_data.get('signals', []))
        }

    def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        try:
            # 检查上次更新时间
            time_since_last_update = datetime.now() - self.last_update_time

            if time_since_last_update > timedelta(days=2):
                status = 'warning'
                message = f'上次更新时间超过2天: {self.last_update_time.isoformat()}'
            else:
                status = 'healthy'
                message = f'上次更新时间: {self.last_update_time.isoformat()}'

            return {
                'status': status,
                'message': message,
                'last_update_time': self.last_update_time.isoformat(),
                'time_since_last_update': str(time_since_last_update),
                'market_volatility_count': len(self.volatility_data.get('market', {})),
                'sector_volatility_count': len(self.volatility_data.get('sector', {})),
                'stock_volatility_count': len(self.volatility_data.get('stock', {})),
                'signals_count': len(self.volatility_data.get('signals', []))
            }

        except Exception as e:
            logger.error(f"健康检查失败: {str(e)}")
            return {
                'status': 'error',
                'message': f'健康检查失败: {str(e)}',
                'error': str(e)
            }

    def calculate_market_volatility(self, **kwargs) -> Dict[str, Any]:
        """
        计算市场波动率

        Returns:
            result: 计算结果
        """
        try:
            logger.info("开始计算市场波动率...")

            # 记录当前时间
            current_time = datetime.now()

            # 获取主要指数列表
            indices = [
                {'code': '000001', 'name': '上证指数'},
                {'code': '399001', 'name': '深证成指'},
                {'code': '399006', 'name': '创业板指'},
                {'code': '000300', 'name': '沪深300'},
                {'code': '000905', 'name': '中证500'},
                {'code': '000688', 'name': '科创50'}
            ]

            # 计算各指数波动率
            market_volatility = {}

            for index in indices:
                code = index['code']
                name = index['name']

                # 获取指数历史数据
                index_data = self._fetch_index_data(code)

                if index_data.empty:
                    logger.warning(f"未获取到指数{name}({code})的历史数据")
                    continue

                # 计算波动率
                volatility_result = self._calculate_volatility(index_data)

                if not volatility_result:
                    logger.warning(f"计算指数{name}({code})的波动率失败")
                    continue

                # 添加指数信息
                volatility_result['code'] = code
                volatility_result['name'] = name
                volatility_result['date'] = current_time.strftime('%Y-%m-%d')
                volatility_result['calculated_at'] = current_time.isoformat()

                # 保存波动率结果
                market_volatility[code] = volatility_result

            # 更新市场波动率数据
            self._update_market_volatility(market_volatility)

            # 生成市场波动率信号
            signals = self._generate_market_volatility_signals(market_volatility)

            # 更新上次更新时间
            self.last_update_time = current_time

            logger.info(f"市场波动率计算成功，共{len(market_volatility)}个指数，生成{len(signals)}个信号")

            return {
                'status': 'success',
                'message': f'市场波动率计算成功，共{len(market_volatility)}个指数，生成{len(signals)}个信号',
                'indices_count': len(market_volatility),
                'signals_count': len(signals)
            }

        except Exception as e:
            logger.error(f"计算市场波动率失败: {str(e)}")
            return {
                'status': 'error',
                'message': f'计算市场波动率失败: {str(e)}',
                'error': str(e)
            }

    def _fetch_index_data(self, code: str) -> pd.DataFrame:
        """
        获取指数历史数据

        Args:
            code: 指数代码

        Returns:
            index_data: 指数历史数据
        """
        try:
            # 使用akshare获取指数日线数据
            if code.startswith('0'):  # 上证系列指数
                index_data = ak.stock_zh_index_daily(symbol=f"sh{code}")
            else:  # 深证系列指数
                index_data = ak.stock_zh_index_daily(symbol=f"sz{code}")

            # 重命名列
            index_data = index_data.rename(columns={
                '日期': 'date',
                '开盘': 'open',
                '高': 'high',
                '低': 'low',
                '收盘': 'close',
                '成交量': 'volume',
                '成交额': 'amount'
            })

            # 转换日期列
            index_data['date'] = pd.to_datetime(index_data['date'])

            # 按日期排序
            index_data = index_data.sort_values('date', ascending=False)

            # 只保留最近60天的数据
            index_data = index_data.head(60)

            return index_data

        except Exception as e:
            logger.error(f"获取指数{code}历史数据失败: {str(e)}")
            return pd.DataFrame()

    def _calculate_volatility(self, price_data: pd.DataFrame) -> Dict[str, Any]:
        """
        计算波动率

        Args:
            price_data: 价格数据

        Returns:
            volatility_result: 波动率计算结果
        """
        try:
            # 获取参数
            historical_window = self.volatility_params['historical_window']
            realized_window = self.volatility_params['realized_window']

            # 检查数据是否足够
            if len(price_data) < historical_window:
                logger.warning(f"数据不足，无法计算波动率，需要至少{historical_window}天数据")
                return {}

            # 计算日收益率
            price_data['return'] = price_data['close'].pct_change() * 100

            # 计算历史波动率 (20天)
            historical_volatility = price_data['return'].iloc[1:historical_window+1].std() * np.sqrt(252)

            # 计算已实现波动率 (5天)
            realized_volatility = price_data['return'].iloc[1:realized_window+1].std() * np.sqrt(252)

            # 计算隐含波动率 (使用GARCH模型估计)
            implied_volatility = self._estimate_implied_volatility(price_data)

            # 计算波动率锥
            volatility_cone = self._calculate_volatility_cone(price_data)

            # 计算波动率偏度
            volatility_skew = self._calculate_volatility_skew(price_data)

            # 计算波动率期限结构
            term_structure = self._calculate_volatility_term_structure(price_data)

            # 创建波动率结果
            volatility_result = {
                'historical_volatility': historical_volatility,
                'realized_volatility': realized_volatility,
                'implied_volatility': implied_volatility,
                'volatility_cone': volatility_cone,
                'volatility_skew': volatility_skew,
                'term_structure': term_structure,
                'latest_price': price_data['close'].iloc[0],
                'latest_return': price_data['return'].iloc[0],
                'price_change_5d': (price_data['close'].iloc[0] / price_data['close'].iloc[5] - 1) * 100 if len(price_data) > 5 else 0,
                'price_change_20d': (price_data['close'].iloc[0] / price_data['close'].iloc[20] - 1) * 100 if len(price_data) > 20 else 0,
                'avg_volume_5d': price_data['volume'].iloc[:5].mean(),
                'avg_volume_20d': price_data['volume'].iloc[:20].mean() if len(price_data) >= 20 else 0
            }

            return volatility_result

        except Exception as e:
            logger.error(f"计算波动率失败: {str(e)}")
            return {}

    def _estimate_implied_volatility(self, price_data: pd.DataFrame) -> float:
        """
        估计隐含波动率

        Args:
            price_data: 价格数据

        Returns:
            implied_volatility: 隐含波动率
        """
        try:
            # 简化处理：使用GARCH(1,1)模型估计隐含波动率
            # 在实际应用中，可以使用更复杂的模型或从期权市场获取隐含波动率

            # 计算日收益率
            returns = price_data['return'].iloc[1:21].values

            # GARCH(1,1)参数
            omega = 0.00001
            alpha = 0.1
            beta = 0.8

            # 初始化波动率
            sigma2 = np.var(returns)

            # 迭代计算
            for r in returns:
                sigma2 = omega + alpha * r**2 + beta * sigma2

            # 计算隐含波动率
            implied_volatility = np.sqrt(sigma2) * np.sqrt(252)

            return implied_volatility

        except Exception as e:
            logger.error(f"估计隐含波动率失败: {str(e)}")
            return 0.0

    def _calculate_volatility_cone(self, price_data: pd.DataFrame) -> Dict[str, float]:
        """
        计算波动率锥

        Args:
            price_data: 价格数据

        Returns:
            volatility_cone: 波动率锥
        """
        try:
            # 计算不同时间窗口的波动率
            windows = [5, 10, 20, 30]
            cone = {}

            for window in windows:
                if len(price_data) >= window:
                    # 计算窗口波动率
                    window_volatility = price_data['return'].iloc[1:window+1].std() * np.sqrt(252)
                    cone[f'{window}d'] = window_volatility

            return cone

        except Exception as e:
            logger.error(f"计算波动率锥失败: {str(e)}")
            return {}

    def _calculate_volatility_skew(self, price_data: pd.DataFrame) -> float:
        """
        计算波动率偏度

        Args:
            price_data: 价格数据

        Returns:
            volatility_skew: 波动率偏度
        """
        try:
            # 计算日收益率
            returns = price_data['return'].iloc[1:21].values

            # 计算偏度
            skew = 0.0
            if len(returns) > 0:
                mean = np.mean(returns)
                std = np.std(returns)
                if std > 0:
                    skew = np.mean(((returns - mean) / std) ** 3)

            return skew

        except Exception as e:
            logger.error(f"计算波动率偏度失败: {str(e)}")
            return 0.0

    def _calculate_volatility_term_structure(self, price_data: pd.DataFrame) -> Dict[str, float]:
        """
        计算波动率期限结构

        Args:
            price_data: 价格数据

        Returns:
            term_structure: 波动率期限结构
        """
        try:
            # 计算不同时间窗口的波动率
            windows = [5, 10, 20, 30]
            term_structure = {}

            for window in windows:
                if len(price_data) >= window:
                    # 计算窗口波动率
                    window_volatility = price_data['return'].iloc[1:window+1].std() * np.sqrt(252)
                    term_structure[f'{window}d'] = window_volatility

            return term_structure

        except Exception as e:
            logger.error(f"计算波动率期限结构失败: {str(e)}")
            return {}

    def _update_market_volatility(self, market_volatility: Dict[str, Dict[str, Any]]):
        """
        更新市场波动率数据

        Args:
            market_volatility: 市场波动率数据
        """
        # 获取现有数据
        existing_market_volatility = self.volatility_data.get('market', {})

        # 更新数据
        current_date = datetime.now().strftime('%Y-%m-%d')

        if current_date not in existing_market_volatility:
            existing_market_volatility[current_date] = {}

        for code, volatility in market_volatility.items():
            existing_market_volatility[current_date][code] = volatility

        # 只保留最近30天的数据
        if len(existing_market_volatility) > 30:
            # 按日期排序
            sorted_dates = sorted(existing_market_volatility.keys(), reverse=True)

            # 只保留最近30天
            new_market_volatility = {}
            for date in sorted_dates[:30]:
                new_market_volatility[date] = existing_market_volatility[date]

            existing_market_volatility = new_market_volatility

        # 更新数据
        self.volatility_data['market'] = existing_market_volatility

        # 保存数据
        self.data_storage.save('volatility_analyzer', 'volatility_data', self.volatility_data, StorageLevel.WARM)

        logger.info(f"市场波动率数据更新成功，共{len(market_volatility)}个指数")

    def _generate_market_volatility_signals(self, market_volatility: Dict[str, Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        生成市场波动率信号

        Args:
            market_volatility: 市场波动率数据

        Returns:
            signals: 市场波动率信号列表
        """
        signals = []

        # 获取参数
        volatility_threshold = self.volatility_params['volatility_threshold']

        # 获取现有信号
        existing_signals = self.volatility_data.get('signals', [])

        # 处理每个指数
        for code, volatility in market_volatility.items():
            name = volatility.get('name', '')
            historical_volatility = volatility.get('historical_volatility', 0)
            realized_volatility = volatility.get('realized_volatility', 0)

            # 1. 高波动率信号
            if realized_volatility > volatility_threshold * 100:
                signal = {
                    'type': 'market_high_volatility',
                    'code': code,
                    'name': name,
                    'date': volatility.get('date', ''),
                    'volatility': realized_volatility,
                    'threshold': volatility_threshold * 100,
                    'strength': realized_volatility / (volatility_threshold * 100),
                    'description': f'市场高波动率信号：{name}已实现波动率{realized_volatility:.2f}%，超过阈值{volatility_threshold * 100:.2f}%',
                    'created_at': datetime.now().isoformat()
                }

                signals.append(signal)

            # 2. 波动率突变信号
            if realized_volatility > historical_volatility * 1.5:
                signal = {
                    'type': 'market_volatility_surge',
                    'code': code,
                    'name': name,
                    'date': volatility.get('date', ''),
                    'realized_volatility': realized_volatility,
                    'historical_volatility': historical_volatility,
                    'ratio': realized_volatility / historical_volatility if historical_volatility > 0 else 0,
                    'strength': realized_volatility / historical_volatility if historical_volatility > 0 else 0,
                    'description': f'市场波动率突变信号：{name}已实现波动率{realized_volatility:.2f}%，历史波动率{historical_volatility:.2f}%，比值{realized_volatility / historical_volatility if historical_volatility > 0 else 0:.2f}',
                    'created_at': datetime.now().isoformat()
                }

                signals.append(signal)

            # 3. 波动率偏度信号
            volatility_skew = volatility.get('volatility_skew', 0)
            if abs(volatility_skew) > 1.0:
                signal_type = 'market_volatility_positive_skew' if volatility_skew > 0 else 'market_volatility_negative_skew'
                description = f'市场波动率{"正" if volatility_skew > 0 else "负"}偏度信号：{name}波动率偏度{volatility_skew:.2f}'

                signal = {
                    'type': signal_type,
                    'code': code,
                    'name': name,
                    'date': volatility.get('date', ''),
                    'volatility_skew': volatility_skew,
                    'strength': abs(volatility_skew),
                    'description': description,
                    'created_at': datetime.now().isoformat()
                }

                signals.append(signal)

        # 合并信号
        combined_signals = existing_signals + signals

        # 去重
        unique_signals = []
        signal_keys = set()

        for signal in combined_signals:
            key = f"{signal['type']}_{signal['code']}_{signal['date']}"
            if key not in signal_keys:
                unique_signals.append(signal)
                signal_keys.add(key)

        # 只保留最近100个信号
        if len(unique_signals) > 100:
            unique_signals = sorted(unique_signals, key=lambda x: x['created_at'], reverse=True)[:100]

        # 更新信号
        self.volatility_data['signals'] = unique_signals

        # 保存数据
        self.data_storage.save('volatility_analyzer', 'volatility_data', self.volatility_data, StorageLevel.WARM)

        logger.info(f"生成市场波动率信号{len(signals)}个")

        return signals

    def calculate_sector_volatility(self, **kwargs) -> Dict[str, Any]:
        """
        计算行业波动率

        Returns:
            result: 计算结果
        """
        try:
            logger.info("开始计算行业波动率...")

            # 记录当前时间
            current_time = datetime.now()

            # 获取行业板块列表
            sectors = list(self.sector_map.keys())

            # 计算各行业波动率
            sector_volatility = {}

            for sector_name in sectors:
                # 获取行业成分股
                stocks = self.sector_map.get(sector_name, [])

                if not stocks:
                    logger.warning(f"未获取到行业{sector_name}的成分股")
                    continue

                # 获取成分股代码
                stock_codes = []
                for stock_name in stocks:
                    if stock_name in self.stock_code_map:
                        stock_codes.append(self.stock_code_map[stock_name])

                if not stock_codes:
                    logger.warning(f"未获取到行业{sector_name}的成分股代码")
                    continue

                # 计算行业波动率
                sector_result = self._calculate_sector_volatility(sector_name, stock_codes)

                if not sector_result:
                    logger.warning(f"计算行业{sector_name}的波动率失败")
                    continue

                # 添加行业信息
                sector_result['name'] = sector_name
                sector_result['date'] = current_time.strftime('%Y-%m-%d')
                sector_result['calculated_at'] = current_time.isoformat()

                # 保存波动率结果
                sector_volatility[sector_name] = sector_result

            # 更新行业波动率数据
            self._update_sector_volatility(sector_volatility)

            # 生成行业波动率信号
            signals = self._generate_sector_volatility_signals(sector_volatility)

            # 更新上次更新时间
            self.last_update_time = current_time

            logger.info(f"行业波动率计算成功，共{len(sector_volatility)}个行业，生成{len(signals)}个信号")

            return {
                'status': 'success',
                'message': f'行业波动率计算成功，共{len(sector_volatility)}个行业，生成{len(signals)}个信号',
                'sectors_count': len(sector_volatility),
                'signals_count': len(signals)
            }

        except Exception as e:
            logger.error(f"计算行业波动率失败: {str(e)}")
            return {
                'status': 'error',
                'message': f'计算行业波动率失败: {str(e)}',
                'error': str(e)
            }

    def _calculate_sector_volatility(self, sector_name: str, stock_codes: List[str]) -> Dict[str, Any]:
        """
        计算行业波动率

        Args:
            sector_name: 行业名称
            stock_codes: 成分股代码列表

        Returns:
            sector_result: 行业波动率计算结果
        """
        try:
            # 获取参数
            historical_window = self.volatility_params['historical_window']
            realized_window = self.volatility_params['realized_window']

            # 获取成分股数据
            stock_data_list = []

            for code in stock_codes:
                # 获取股票历史数据
                stock_data = self._fetch_stock_data(code)

                if not stock_data.empty:
                    stock_data_list.append(stock_data)

            if not stock_data_list:
                logger.warning(f"未获取到行业{sector_name}的成分股数据")
                return {}

            # 计算行业指数
            sector_index = self._calculate_sector_index(stock_data_list)

            if sector_index.empty:
                logger.warning(f"计算行业{sector_name}指数失败")
                return {}

            # 计算波动率
            volatility_result = self._calculate_volatility(sector_index)

            if not volatility_result:
                logger.warning(f"计算行业{sector_name}波动率失败")
                return {}

            # 添加成分股信息
            volatility_result['stock_codes'] = stock_codes
            volatility_result['stocks_count'] = len(stock_codes)

            return volatility_result

        except Exception as e:
            logger.error(f"计算行业{sector_name}波动率失败: {str(e)}")
            return {}

    def _fetch_stock_data(self, code: str) -> pd.DataFrame:
        """
        获取股票历史数据

        Args:
            code: 股票代码

        Returns:
            stock_data: 股票历史数据
        """
        try:
            # 使用akshare获取股票日线数据
            stock_data = ak.stock_zh_a_hist(symbol=code, period="daily", adjust="qfq")

            # 重命名列
            stock_data = stock_data.rename(columns={
                '日期': 'date',
                '开盘': 'open',
                '收盘': 'close',
                '最高': 'high',
                '最低': 'low',
                '成交量': 'volume',
                '成交额': 'amount',
                '振幅': 'amplitude',
                '涨跌幅': 'pct_change',
                '涨跌额': 'change',
                '换手率': 'turnover'
            })

            # 转换日期列
            stock_data['date'] = pd.to_datetime(stock_data['date'])

            # 按日期排序
            stock_data = stock_data.sort_values('date', ascending=False)

            # 只保留最近60天的数据
            stock_data = stock_data.head(60)

            return stock_data

        except Exception as e:
            logger.error(f"获取股票{code}历史数据失败: {str(e)}")
            return pd.DataFrame()

    def _calculate_sector_index(self, stock_data_list: List[pd.DataFrame]) -> pd.DataFrame:
        """
        计算行业指数

        Args:
            stock_data_list: 成分股数据列表

        Returns:
            sector_index: 行业指数
        """
        try:
            if not stock_data_list:
                return pd.DataFrame()

            # 获取所有日期
            all_dates = set()
            for stock_data in stock_data_list:
                all_dates.update(stock_data['date'].tolist())

            all_dates = sorted(all_dates, reverse=True)

            # 创建行业指数
            sector_index = pd.DataFrame({
                'date': all_dates,
                'close': [0] * len(all_dates),
                'volume': [0] * len(all_dates)
            })

            # 设置日期为索引
            sector_index.set_index('date', inplace=True)

            # 计算等权重指数
            weight = 1.0 / len(stock_data_list)

            for stock_data in stock_data_list:
                # 设置日期为索引
                stock_data_indexed = stock_data.set_index('date')

                # 添加到行业指数
                for date in all_dates:
                    if date in stock_data_indexed.index:
                        sector_index.loc[date, 'close'] += stock_data_indexed.loc[date, 'close'] * weight
                        sector_index.loc[date, 'volume'] += stock_data_indexed.loc[date, 'volume']

            # 重置索引
            sector_index.reset_index(inplace=True)

            # 计算收益率
            sector_index['return'] = sector_index['close'].pct_change() * 100

            return sector_index

        except Exception as e:
            logger.error(f"计算行业指数失败: {str(e)}")
            return pd.DataFrame()

    def _update_sector_volatility(self, sector_volatility: Dict[str, Dict[str, Any]]):
        """
        更新行业波动率数据

        Args:
            sector_volatility: 行业波动率数据
        """
        # 获取现有数据
        existing_sector_volatility = self.volatility_data.get('sector', {})

        # 更新数据
        current_date = datetime.now().strftime('%Y-%m-%d')

        if current_date not in existing_sector_volatility:
            existing_sector_volatility[current_date] = {}

        for sector_name, volatility in sector_volatility.items():
            existing_sector_volatility[current_date][sector_name] = volatility

        # 只保留最近30天的数据
        if len(existing_sector_volatility) > 30:
            # 按日期排序
            sorted_dates = sorted(existing_sector_volatility.keys(), reverse=True)

            # 只保留最近30天
            new_sector_volatility = {}
            for date in sorted_dates[:30]:
                new_sector_volatility[date] = existing_sector_volatility[date]

            existing_sector_volatility = new_sector_volatility

        # 更新数据
        self.volatility_data['sector'] = existing_sector_volatility

        # 保存数据
        self.data_storage.save('volatility_analyzer', 'volatility_data', self.volatility_data, StorageLevel.WARM)

        logger.info(f"行业波动率数据更新成功，共{len(sector_volatility)}个行业")

    def _generate_sector_volatility_signals(self, sector_volatility: Dict[str, Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        生成行业波动率信号

        Args:
            sector_volatility: 行业波动率数据

        Returns:
            signals: 行业波动率信号列表
        """
        signals = []

        # 获取参数
        volatility_threshold = self.volatility_params['volatility_threshold']

        # 获取现有信号
        existing_signals = self.volatility_data.get('signals', [])

        # 处理每个行业
        for sector_name, volatility in sector_volatility.items():
            historical_volatility = volatility.get('historical_volatility', 0)
            realized_volatility = volatility.get('realized_volatility', 0)

            # 1. 高波动率信号
            if realized_volatility > volatility_threshold * 100:
                signal = {
                    'type': 'sector_high_volatility',
                    'sector': sector_name,
                    'date': volatility.get('date', ''),
                    'volatility': realized_volatility,
                    'threshold': volatility_threshold * 100,
                    'strength': realized_volatility / (volatility_threshold * 100),
                    'description': f'行业高波动率信号：{sector_name}已实现波动率{realized_volatility:.2f}%，超过阈值{volatility_threshold * 100:.2f}%',
                    'created_at': datetime.now().isoformat()
                }

                signals.append(signal)

            # 2. 波动率突变信号
            if realized_volatility > historical_volatility * 1.5:
                signal = {
                    'type': 'sector_volatility_surge',
                    'sector': sector_name,
                    'date': volatility.get('date', ''),
                    'realized_volatility': realized_volatility,
                    'historical_volatility': historical_volatility,
                    'ratio': realized_volatility / historical_volatility if historical_volatility > 0 else 0,
                    'strength': realized_volatility / historical_volatility if historical_volatility > 0 else 0,
                    'description': f'行业波动率突变信号：{sector_name}已实现波动率{realized_volatility:.2f}%，历史波动率{historical_volatility:.2f}%，比值{realized_volatility / historical_volatility if historical_volatility > 0 else 0:.2f}',
                    'created_at': datetime.now().isoformat()
                }

                signals.append(signal)

            # 3. 波动率偏度信号
            volatility_skew = volatility.get('volatility_skew', 0)
            if abs(volatility_skew) > 1.0:
                signal_type = 'sector_volatility_positive_skew' if volatility_skew > 0 else 'sector_volatility_negative_skew'
                description = f'行业波动率{"正" if volatility_skew > 0 else "负"}偏度信号：{sector_name}波动率偏度{volatility_skew:.2f}'

                signal = {
                    'type': signal_type,
                    'sector': sector_name,
                    'date': volatility.get('date', ''),
                    'volatility_skew': volatility_skew,
                    'strength': abs(volatility_skew),
                    'description': description,
                    'created_at': datetime.now().isoformat()
                }

                signals.append(signal)

        # 合并信号
        combined_signals = existing_signals + signals

        # 去重
        unique_signals = []
        signal_keys = set()

        for signal in combined_signals:
            # 创建信号键
            if 'code' in signal:
                key = f"{signal['type']}_{signal['code']}_{signal['date']}"
            elif 'sector' in signal:
                key = f"{signal['type']}_{signal['sector']}_{signal['date']}"
            else:
                key = f"{signal['type']}_{signal['date']}"

            if key not in signal_keys:
                unique_signals.append(signal)
                signal_keys.add(key)

        # 只保留最近100个信号
        if len(unique_signals) > 100:
            unique_signals = sorted(unique_signals, key=lambda x: x['created_at'], reverse=True)[:100]

        # 更新信号
        self.volatility_data['signals'] = unique_signals

        # 保存数据
        self.data_storage.save('volatility_analyzer', 'volatility_data', self.volatility_data, StorageLevel.WARM)

        logger.info(f"生成行业波动率信号{len(signals)}个")

        return signals

    def calculate_stock_volatility(self, **kwargs) -> Dict[str, Any]:
        """
        计算个股波动率

        Returns:
            result: 计算结果
        """
        try:
            logger.info("开始计算个股波动率...")

            # 记录当前时间
            current_time = datetime.now()

            # 获取股票列表
            stocks = []

            # 从行业板块映射中获取股票
            for sector, sector_stocks in self.sector_map.items():
                for stock_name in sector_stocks:
                    if stock_name in self.stock_code_map:
                        code = self.stock_code_map[stock_name]
                        stocks.append({
                            'code': code,
                            'name': stock_name
                        })

            # 去重
            unique_stocks = []
            stock_codes = set()

            for stock in stocks:
                if stock['code'] not in stock_codes:
                    unique_stocks.append(stock)
                    stock_codes.add(stock['code'])

            # 计算各股票波动率
            stock_volatility = {}

            for stock in unique_stocks:
                code = stock['code']
                name = stock['name']

                # 获取股票历史数据
                stock_data = self._fetch_stock_data(code)

                if stock_data.empty:
                    logger.warning(f"未获取到股票{name}({code})的历史数据")
                    continue

                # 计算波动率
                volatility_result = self._calculate_volatility(stock_data)

                if not volatility_result:
                    logger.warning(f"计算股票{name}({code})的波动率失败")
                    continue

                # 添加股票信息
                volatility_result['code'] = code
                volatility_result['name'] = name
                volatility_result['date'] = current_time.strftime('%Y-%m-%d')
                volatility_result['calculated_at'] = current_time.isoformat()

                # 保存波动率结果
                stock_volatility[code] = volatility_result

            # 更新个股波动率数据
            self._update_stock_volatility(stock_volatility)

            # 生成个股波动率信号
            signals = self._generate_stock_volatility_signals(stock_volatility)

            # 更新上次更新时间
            self.last_update_time = current_time

            logger.info(f"个股波动率计算成功，共{len(stock_volatility)}只股票，生成{len(signals)}个信号")

            return {
                'status': 'success',
                'message': f'个股波动率计算成功，共{len(stock_volatility)}只股票，生成{len(signals)}个信号',
                'stocks_count': len(stock_volatility),
                'signals_count': len(signals)
            }

        except Exception as e:
            logger.error(f"计算个股波动率失败: {str(e)}")
            return {
                'status': 'error',
                'message': f'计算个股波动率失败: {str(e)}',
                'error': str(e)
            }

    def _update_stock_volatility(self, stock_volatility: Dict[str, Dict[str, Any]]):
        """
        更新个股波动率数据

        Args:
            stock_volatility: 个股波动率数据
        """
        # 获取现有数据
        existing_stock_volatility = self.volatility_data.get('stock', {})

        # 更新数据
        current_date = datetime.now().strftime('%Y-%m-%d')

        if current_date not in existing_stock_volatility:
            existing_stock_volatility[current_date] = {}

        for code, volatility in stock_volatility.items():
            existing_stock_volatility[current_date][code] = volatility

        # 只保留最近30天的数据
        if len(existing_stock_volatility) > 30:
            # 按日期排序
            sorted_dates = sorted(existing_stock_volatility.keys(), reverse=True)

            # 只保留最近30天
            new_stock_volatility = {}
            for date in sorted_dates[:30]:
                new_stock_volatility[date] = existing_stock_volatility[date]

            existing_stock_volatility = new_stock_volatility

        # 更新数据
        self.volatility_data['stock'] = existing_stock_volatility

        # 保存数据
        self.data_storage.save('volatility_analyzer', 'volatility_data', self.volatility_data, StorageLevel.WARM)

        logger.info(f"个股波动率数据更新成功，共{len(stock_volatility)}只股票")

    def _generate_stock_volatility_signals(self, stock_volatility: Dict[str, Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        生成个股波动率信号

        Args:
            stock_volatility: 个股波动率数据

        Returns:
            signals: 个股波动率信号列表
        """
        signals = []

        # 获取参数
        volatility_threshold = self.volatility_params['volatility_threshold']

        # 获取现有信号
        existing_signals = self.volatility_data.get('signals', [])

        # 处理每只股票
        for code, volatility in stock_volatility.items():
            name = volatility.get('name', '')
            historical_volatility = volatility.get('historical_volatility', 0)
            realized_volatility = volatility.get('realized_volatility', 0)

            # 1. 高波动率信号
            if realized_volatility > volatility_threshold * 100:
                signal = {
                    'type': 'stock_high_volatility',
                    'code': code,
                    'name': name,
                    'date': volatility.get('date', ''),
                    'volatility': realized_volatility,
                    'threshold': volatility_threshold * 100,
                    'strength': realized_volatility / (volatility_threshold * 100),
                    'description': f'个股高波动率信号：{name}({code})已实现波动率{realized_volatility:.2f}%，超过阈值{volatility_threshold * 100:.2f}%',
                    'created_at': datetime.now().isoformat()
                }

                signals.append(signal)

            # 2. 波动率突变信号
            if realized_volatility > historical_volatility * 1.5:
                signal = {
                    'type': 'stock_volatility_surge',
                    'code': code,
                    'name': name,
                    'date': volatility.get('date', ''),
                    'realized_volatility': realized_volatility,
                    'historical_volatility': historical_volatility,
                    'ratio': realized_volatility / historical_volatility if historical_volatility > 0 else 0,
                    'strength': realized_volatility / historical_volatility if historical_volatility > 0 else 0,
                    'description': f'个股波动率突变信号：{name}({code})已实现波动率{realized_volatility:.2f}%，历史波动率{historical_volatility:.2f}%，比值{realized_volatility / historical_volatility if historical_volatility > 0 else 0:.2f}',
                    'created_at': datetime.now().isoformat()
                }

                signals.append(signal)

            # 3. 波动率偏度信号
            volatility_skew = volatility.get('volatility_skew', 0)
            if abs(volatility_skew) > 1.0:
                signal_type = 'stock_volatility_positive_skew' if volatility_skew > 0 else 'stock_volatility_negative_skew'
                description = f'个股波动率{"正" if volatility_skew > 0 else "负"}偏度信号：{name}({code})波动率偏度{volatility_skew:.2f}'

                signal = {
                    'type': signal_type,
                    'code': code,
                    'name': name,
                    'date': volatility.get('date', ''),
                    'volatility_skew': volatility_skew,
                    'strength': abs(volatility_skew),
                    'description': description,
                    'created_at': datetime.now().isoformat()
                }

                signals.append(signal)

            # 4. 波动率期限结构异常信号
            term_structure = volatility.get('term_structure', {})
            if term_structure and '5d' in term_structure and '30d' in term_structure:
                short_term = term_structure['5d']
                long_term = term_structure['30d']

                # 期限结构倒挂
                if short_term > long_term * 1.5:
                    signal = {
                        'type': 'stock_volatility_term_inversion',
                        'code': code,
                        'name': name,
                        'date': volatility.get('date', ''),
                        'short_term': short_term,
                        'long_term': long_term,
                        'ratio': short_term / long_term if long_term > 0 else 0,
                        'strength': short_term / long_term if long_term > 0 else 0,
                        'description': f'个股波动率期限结构倒挂信号：{name}({code})短期波动率{short_term:.2f}%，长期波动率{long_term:.2f}%，比值{short_term / long_term if long_term > 0 else 0:.2f}',
                        'created_at': datetime.now().isoformat()
                    }

                    signals.append(signal)

        # 合并信号
        combined_signals = existing_signals + signals

        # 去重
        unique_signals = []
        signal_keys = set()

        for signal in combined_signals:
            # 创建信号键
            if 'code' in signal:
                key = f"{signal['type']}_{signal['code']}_{signal['date']}"
            elif 'sector' in signal:
                key = f"{signal['type']}_{signal['sector']}_{signal['date']}"
            else:
                key = f"{signal['type']}_{signal['date']}"

            if key not in signal_keys:
                unique_signals.append(signal)
                signal_keys.add(key)

        # 只保留最近100个信号
        if len(unique_signals) > 100:
            unique_signals = sorted(unique_signals, key=lambda x: x['created_at'], reverse=True)[:100]

        # 更新信号
        self.volatility_data['signals'] = unique_signals

        # 保存数据
        self.data_storage.save('volatility_analyzer', 'volatility_data', self.volatility_data, StorageLevel.WARM)

        logger.info(f"生成个股波动率信号{len(signals)}个")

        return signals

    def calculate_policy_volatility_premium(self, **kwargs) -> Dict[str, Any]:
        """
        计算政策波动率溢价

        Returns:
            result: 计算结果
        """
        try:
            logger.info("开始计算政策波动率溢价...")

            # 记录当前时间
            current_time = datetime.now()

            # 获取政策数据
            policy_data = self._fetch_policy_data()

            if not policy_data:
                logger.warning("未获取到政策数据")
                return {
                    'status': 'warning',
                    'message': '未获取到政策数据',
                    'count': 0
                }

            # 获取市场波动率数据
            market_volatility = self._get_latest_market_volatility()

            if not market_volatility:
                logger.warning("未获取到市场波动率数据")
                return {
                    'status': 'warning',
                    'message': '未获取到市场波动率数据',
                    'count': 0
                }

            # 计算政策波动率溢价
            premium_result = self._calculate_policy_premium(policy_data, market_volatility)

            if not premium_result:
                logger.warning("计算政策波动率溢价失败")
                return {
                    'status': 'warning',
                    'message': '计算政策波动率溢价失败',
                    'count': 0
                }

            # 更新政策波动率溢价数据
            self._update_policy_premium(premium_result)

            # 生成政策波动率溢价信号
            signals = self._generate_policy_premium_signals(premium_result)

            # 更新上次更新时间
            self.last_update_time = current_time

            logger.info(f"政策波动率溢价计算成功，生成{len(signals)}个信号")

            return {
                'status': 'success',
                'message': f'政策波动率溢价计算成功，生成{len(signals)}个信号',
                'premium_count': len(premium_result),
                'signals_count': len(signals)
            }

        except Exception as e:
            logger.error(f"计算政策波动率溢价失败: {str(e)}")
            return {
                'status': 'error',
                'message': f'计算政策波动率溢价失败: {str(e)}',
                'error': str(e)
            }

    def _fetch_policy_data(self) -> List[Dict[str, Any]]:
        """
        获取政策数据

        Returns:
            policy_data: 政策数据列表
        """
        try:
            # 尝试从数据存储中获取政策数据
            policy_data = self.data_storage.load('policy_analyzer', 'policy_data')

            if policy_data:
                logger.info(f"从数据存储中获取了{len(policy_data)}条政策数据")
                return policy_data

            # 如果数据存储中没有，使用模拟数据
            # 在实际应用中，应该从政策分析模块获取数据
            policy_data = [
                {
                    'id': '1',
                    'title': '关于进一步加强金融支持实体经济发展的指导意见',
                    'date': (datetime.now() - timedelta(days=5)).strftime('%Y-%m-%d'),
                    'source': '国务院',
                    'importance': 0.8,
                    'sentiment': 0.6,
                    'keywords': ['金融', '实体经济', '支持'],
                    'summary': '加强金融对实体经济的支持，降低融资成本，提高金融服务效率。'
                },
                {
                    'id': '2',
                    'title': '关于促进消费扩容提质加快形成强大国内市场的实施意见',
                    'date': (datetime.now() - timedelta(days=10)).strftime('%Y-%m-%d'),
                    'source': '国家发改委',
                    'importance': 0.7,
                    'sentiment': 0.5,
                    'keywords': ['消费', '国内市场', '扩容'],
                    'summary': '促进消费扩容提质，加快形成强大国内市场，提振消费信心。'
                },
                {
                    'id': '3',
                    'title': '关于加快建设全国统一大市场的意见',
                    'date': (datetime.now() - timedelta(days=15)).strftime('%Y-%m-%d'),
                    'source': '中共中央、国务院',
                    'importance': 0.9,
                    'sentiment': 0.7,
                    'keywords': ['统一市场', '要素流通', '公平竞争'],
                    'summary': '加快建设高效规范、公平竞争、充分开放的全国统一大市场。'
                }
            ]

            logger.info(f"使用模拟数据，共{len(policy_data)}条政策数据")
            return policy_data

        except Exception as e:
            logger.error(f"获取政策数据失败: {str(e)}")
            return []

    def _get_latest_market_volatility(self) -> Dict[str, Dict[str, Any]]:
        """
        获取最新市场波动率数据

        Returns:
            market_volatility: 市场波动率数据
        """
        try:
            # 获取市场波动率数据
            market_volatility_data = self.volatility_data.get('market', {})

            if not market_volatility_data:
                logger.warning("未获取到市场波动率数据")
                return {}

            # 获取最新日期
            sorted_dates = sorted(market_volatility_data.keys(), reverse=True)

            if not sorted_dates:
                logger.warning("未获取到市场波动率数据日期")
                return {}

            latest_date = sorted_dates[0]

            # 获取最新市场波动率数据
            latest_market_volatility = market_volatility_data[latest_date]

            return latest_market_volatility

        except Exception as e:
            logger.error(f"获取最新市场波动率数据失败: {str(e)}")
            return {}

    def _calculate_policy_premium(self, policy_data: List[Dict[str, Any]], market_volatility: Dict[str, Dict[str, Any]]) -> Dict[str, Dict[str, Any]]:
        """
        计算政策波动率溢价

        Args:
            policy_data: 政策数据列表
            market_volatility: 市场波动率数据

        Returns:
            premium_result: 政策波动率溢价计算结果
        """
        try:
            # 获取参数
            policy_impact_window = self.volatility_params['policy_impact_window']
            premium_threshold = self.volatility_params['premium_threshold']

            # 获取当前日期
            current_date = datetime.now().strftime('%Y-%m-%d')

            # 创建政策波动率溢价结果
            premium_result = {}

            # 处理每个政策
            for policy in policy_data:
                policy_id = policy.get('id', '')
                policy_date = policy.get('date', '')
                policy_title = policy.get('title', '')
                policy_importance = policy.get('importance', 0)
                policy_sentiment = policy.get('sentiment', 0)

                # 计算政策发布后的时间
                try:
                    policy_date_obj = datetime.strptime(policy_date, '%Y-%m-%d')
                    days_since_policy = (datetime.now() - policy_date_obj).days
                except:
                    days_since_policy = 0

                # 只考虑最近一段时间内的政策
                if days_since_policy > policy_impact_window:
                    continue

                # 计算政策影响因子
                # 政策重要性、情感和时间衰减
                time_decay = max(0, 1 - days_since_policy / policy_impact_window)
                impact_factor = policy_importance * abs(policy_sentiment) * time_decay

                # 获取市场指数
                market_indices = ['000001', '000300', '000905']  # 上证指数、沪深300、中证500

                # 计算每个指数的政策波动率溢价
                for index_code in market_indices:
                    if index_code in market_volatility:
                        index_volatility = market_volatility[index_code]
                        index_name = index_volatility.get('name', '')

                        # 获取波动率数据
                        historical_volatility = index_volatility.get('historical_volatility', 0)
                        realized_volatility = index_volatility.get('realized_volatility', 0)
                        implied_volatility = index_volatility.get('implied_volatility', 0)

                        # 计算政策波动率溢价
                        # 政策溢价 = 已实现波动率 / 历史波动率 * 政策影响因子
                        if historical_volatility > 0:
                            volatility_ratio = realized_volatility / historical_volatility
                            policy_premium = volatility_ratio * impact_factor

                            # 创建溢价结果
                            premium_key = f"{policy_id}_{index_code}"
                            premium_result[premium_key] = {
                                'policy_id': policy_id,
                                'policy_title': policy_title,
                                'policy_date': policy_date,
                                'policy_importance': policy_importance,
                                'policy_sentiment': policy_sentiment,
                                'days_since_policy': days_since_policy,
                                'impact_factor': impact_factor,
                                'index_code': index_code,
                                'index_name': index_name,
                                'historical_volatility': historical_volatility,
                                'realized_volatility': realized_volatility,
                                'implied_volatility': implied_volatility,
                                'volatility_ratio': volatility_ratio,
                                'policy_premium': policy_premium,
                                'date': current_date,
                                'calculated_at': datetime.now().isoformat()
                            }

            logger.info(f"计算政策波动率溢价成功，共{len(premium_result)}个结果")
            return premium_result

        except Exception as e:
            logger.error(f"计算政策波动率溢价失败: {str(e)}")
            return {}

    def _update_policy_premium(self, premium_result: Dict[str, Dict[str, Any]]):
        """
        更新政策波动率溢价数据

        Args:
            premium_result: 政策波动率溢价计算结果
        """
        # 获取现有数据
        existing_policy_premium = self.volatility_data.get('policy_premium', {})

        # 更新数据
        current_date = datetime.now().strftime('%Y-%m-%d')

        if current_date not in existing_policy_premium:
            existing_policy_premium[current_date] = {}

        for premium_key, premium in premium_result.items():
            existing_policy_premium[current_date][premium_key] = premium

        # 只保留最近30天的数据
        if len(existing_policy_premium) > 30:
            # 按日期排序
            sorted_dates = sorted(existing_policy_premium.keys(), reverse=True)

            # 只保留最近30天
            new_policy_premium = {}
            for date in sorted_dates[:30]:
                new_policy_premium[date] = existing_policy_premium[date]

            existing_policy_premium = new_policy_premium

        # 更新数据
        self.volatility_data['policy_premium'] = existing_policy_premium

        # 保存数据
        self.data_storage.save('volatility_analyzer', 'volatility_data', self.volatility_data, StorageLevel.WARM)

        logger.info(f"政策波动率溢价数据更新成功，共{len(premium_result)}个结果")

    def _generate_policy_premium_signals(self, premium_result: Dict[str, Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        生成政策波动率溢价信号

        Args:
            premium_result: 政策波动率溢价计算结果

        Returns:
            signals: 政策波动率溢价信号列表
        """
        signals = []

        # 获取参数
        premium_threshold = self.volatility_params['premium_threshold']

        # 获取现有信号
        existing_signals = self.volatility_data.get('signals', [])

        # 处理每个溢价结果
        for premium_key, premium in premium_result.items():
            policy_id = premium.get('policy_id', '')
            policy_title = premium.get('policy_title', '')
            index_code = premium.get('index_code', '')
            index_name = premium.get('index_name', '')
            policy_premium = premium.get('policy_premium', 0)

            # 高溢价信号
            if policy_premium > premium_threshold:
                signal = {
                    'type': 'policy_high_premium',
                    'policy_id': policy_id,
                    'policy_title': policy_title,
                    'index_code': index_code,
                    'index_name': index_name,
                    'date': premium.get('date', ''),
                    'premium': policy_premium,
                    'threshold': premium_threshold,
                    'strength': policy_premium / premium_threshold,
                    'description': f'政策波动率高溢价信号：{policy_title}对{index_name}的波动率溢价{policy_premium:.2f}，超过阈值{premium_threshold:.2f}',
                    'created_at': datetime.now().isoformat()
                }

                signals.append(signal)

        # 合并信号
        combined_signals = existing_signals + signals

        # 去重
        unique_signals = []
        signal_keys = set()

        for signal in combined_signals:
            # 创建信号键
            if 'policy_id' in signal and 'index_code' in signal:
                key = f"{signal['type']}_{signal['policy_id']}_{signal['index_code']}_{signal['date']}"
            elif 'code' in signal:
                key = f"{signal['type']}_{signal['code']}_{signal['date']}"
            elif 'sector' in signal:
                key = f"{signal['type']}_{signal['sector']}_{signal['date']}"
            else:
                key = f"{signal['type']}_{signal['date']}"

            if key not in signal_keys:
                unique_signals.append(signal)
                signal_keys.add(key)

        # 只保留最近100个信号
        if len(unique_signals) > 100:
            unique_signals = sorted(unique_signals, key=lambda x: x['created_at'], reverse=True)[:100]

        # 更新信号
        self.volatility_data['signals'] = unique_signals

        # 保存数据
        self.data_storage.save('volatility_analyzer', 'volatility_data', self.volatility_data, StorageLevel.WARM)

        logger.info(f"生成政策波动率溢价信号{len(signals)}个")

        return signals

    def calculate_fund_flow_volatility_coupling(self, **kwargs) -> Dict[str, Any]:
        """
        计算资金流-波动率耦合

        Returns:
            result: 计算结果
        """
        try:
            logger.info("开始计算资金流-波动率耦合...")

            # 记录当前时间
            current_time = datetime.now()

            # 获取资金流数据
            fund_flow_data = self._fetch_fund_flow_data()

            if not fund_flow_data:
                logger.warning("未获取到资金流数据")
                return {
                    'status': 'warning',
                    'message': '未获取到资金流数据',
                    'count': 0
                }

            # 获取波动率数据
            volatility_data = self._get_latest_volatility_data()

            if not volatility_data:
                logger.warning("未获取到波动率数据")
                return {
                    'status': 'warning',
                    'message': '未获取到波动率数据',
                    'count': 0
                }

            # 计算资金流-波动率耦合
            coupling_result = self._calculate_fund_flow_coupling(fund_flow_data, volatility_data)

            if not coupling_result:
                logger.warning("计算资金流-波动率耦合失败")
                return {
                    'status': 'warning',
                    'message': '计算资金流-波动率耦合失败',
                    'count': 0
                }

            # 更新资金流-波动率耦合数据
            self._update_fund_flow_coupling(coupling_result)

            # 生成资金流-波动率耦合信号
            signals = self._generate_fund_flow_coupling_signals(coupling_result)

            # 更新上次更新时间
            self.last_update_time = current_time

            logger.info(f"资金流-波动率耦合计算成功，生成{len(signals)}个信号")

            return {
                'status': 'success',
                'message': f'资金流-波动率耦合计算成功，生成{len(signals)}个信号',
                'coupling_count': len(coupling_result),
                'signals_count': len(signals)
            }

        except Exception as e:
            logger.error(f"计算资金流-波动率耦合失败: {str(e)}")
            return {
                'status': 'error',
                'message': f'计算资金流-波动率耦合失败: {str(e)}',
                'error': str(e)
            }

    def _fetch_fund_flow_data(self) -> Dict[str, Any]:
        """
        获取资金流数据

        Returns:
            fund_flow_data: 资金流数据
        """
        try:
            # 尝试从数据存储中获取资金流数据
            fund_flow_data = self.data_storage.load('fund_flow_analyzer', 'fund_flow_data')

            if fund_flow_data:
                logger.info(f"从数据存储中获取了资金流数据")
                return fund_flow_data

            # 如果数据存储中没有，使用模拟数据
            # 在实际应用中，应该从资金流分析模块获取数据
            fund_flow_data = {
                'market': {
                    datetime.now().strftime('%Y-%m-%d'): {
                        'north': {
                            'net_flow': 50.0,  # 亿元
                            'inflow': 150.0,
                            'outflow': 100.0
                        },
                        'hsgt': {
                            'sh_hk_flow': 20.0,
                            'sz_hk_flow': 30.0,
                            'total_flow': 50.0
                        }
                    }
                },
                'sector': {
                    datetime.now().strftime('%Y-%m-%d'): {
                        '白酒': {
                            'net_inflow': 5.0,  # 亿元
                            'net_inflow_ratio': 0.8,
                            'super_large_inflow': 3.0,
                            'large_inflow': 2.0
                        },
                        '新能源': {
                            'net_inflow': 8.0,
                            'net_inflow_ratio': 1.2,
                            'super_large_inflow': 5.0,
                            'large_inflow': 3.0
                        },
                        '银行': {
                            'net_inflow': -3.0,
                            'net_inflow_ratio': -0.5,
                            'super_large_inflow': -2.0,
                            'large_inflow': -1.0
                        }
                    }
                },
                'stock': {
                    datetime.now().strftime('%Y-%m-%d'): {
                        '600519': {  # 贵州茅台
                            'net_inflow': 10000,  # 万元
                            'super_large_inflow': 6000,
                            'large_inflow': 4000
                        },
                        '300750': {  # 宁德时代
                            'net_inflow': 15000,
                            'super_large_inflow': 10000,
                            'large_inflow': 5000
                        },
                        '601318': {  # 中国平安
                            'net_inflow': -5000,
                            'super_large_inflow': -3000,
                            'large_inflow': -2000
                        }
                    }
                }
            }

            logger.info("使用模拟资金流数据")
            return fund_flow_data

        except Exception as e:
            logger.error(f"获取资金流数据失败: {str(e)}")
            return {}

    def _get_latest_volatility_data(self) -> Dict[str, Dict[str, Any]]:
        """
        获取最新波动率数据

        Returns:
            volatility_data: 波动率数据
        """
        try:
            # 获取市场波动率数据
            market_volatility = self._get_latest_market_volatility()

            # 获取行业波动率数据
            sector_volatility = self._get_latest_sector_volatility()

            # 获取个股波动率数据
            stock_volatility = self._get_latest_stock_volatility()

            # 合并波动率数据
            volatility_data = {
                'market': market_volatility,
                'sector': sector_volatility,
                'stock': stock_volatility
            }

            return volatility_data

        except Exception as e:
            logger.error(f"获取最新波动率数据失败: {str(e)}")
            return {}

    def _get_latest_sector_volatility(self) -> Dict[str, Dict[str, Any]]:
        """
        获取最新行业波动率数据

        Returns:
            sector_volatility: 行业波动率数据
        """
        try:
            # 获取行业波动率数据
            sector_volatility_data = self.volatility_data.get('sector', {})

            if not sector_volatility_data:
                logger.warning("未获取到行业波动率数据")
                return {}

            # 获取最新日期
            sorted_dates = sorted(sector_volatility_data.keys(), reverse=True)

            if not sorted_dates:
                logger.warning("未获取到行业波动率数据日期")
                return {}

            latest_date = sorted_dates[0]

            # 获取最新行业波动率数据
            latest_sector_volatility = sector_volatility_data[latest_date]

            return latest_sector_volatility

        except Exception as e:
            logger.error(f"获取最新行业波动率数据失败: {str(e)}")
            return {}

    def _get_latest_stock_volatility(self) -> Dict[str, Dict[str, Any]]:
        """
        获取最新个股波动率数据

        Returns:
            stock_volatility: 个股波动率数据
        """
        try:
            # 获取个股波动率数据
            stock_volatility_data = self.volatility_data.get('stock', {})

            if not stock_volatility_data:
                logger.warning("未获取到个股波动率数据")
                return {}

            # 获取最新日期
            sorted_dates = sorted(stock_volatility_data.keys(), reverse=True)

            if not sorted_dates:
                logger.warning("未获取到个股波动率数据日期")
                return {}

            latest_date = sorted_dates[0]

            # 获取最新个股波动率数据
            latest_stock_volatility = stock_volatility_data[latest_date]

            return latest_stock_volatility

        except Exception as e:
            logger.error(f"获取最新个股波动率数据失败: {str(e)}")
            return {}

    def _calculate_fund_flow_coupling(self, fund_flow_data: Dict[str, Any], volatility_data: Dict[str, Dict[str, Any]]) -> Dict[str, Dict[str, Any]]:
        """
        计算资金流-波动率耦合

        Args:
            fund_flow_data: 资金流数据
            volatility_data: 波动率数据

        Returns:
            coupling_result: 资金流-波动率耦合计算结果
        """
        try:
            # 获取当前日期
            current_date = datetime.now().strftime('%Y-%m-%d')

            # 创建资金流-波动率耦合结果
            coupling_result = {}

            # 1. 北向资金-市场波动率耦合
            if 'market' in fund_flow_data and current_date in fund_flow_data['market']:
                market_flow = fund_flow_data['market'][current_date]

                if 'north' in market_flow and 'net_flow' in market_flow['north']:
                    north_net_flow = market_flow['north']['net_flow']

                    # 计算北向资金-市场波动率耦合
                    for index_code, index_volatility in volatility_data['market'].items():
                        index_name = index_volatility.get('name', '')
                        realized_volatility = index_volatility.get('realized_volatility', 0)

                        # 计算耦合系数
                        # 耦合系数 = 北向资金净流入 * 已实现波动率 / 100
                        coupling_coefficient = north_net_flow * realized_volatility / 100

                        # 创建耦合结果
                        coupling_key = f"north_{index_code}"
                        coupling_result[coupling_key] = {
                            'type': 'north_market',
                            'index_code': index_code,
                            'index_name': index_name,
                            'north_net_flow': north_net_flow,
                            'realized_volatility': realized_volatility,
                            'coupling_coefficient': coupling_coefficient,
                            'date': current_date,
                            'calculated_at': datetime.now().isoformat()
                        }

            # 2. 行业资金流-行业波动率耦合
            if 'sector' in fund_flow_data and current_date in fund_flow_data['sector']:
                sector_flow = fund_flow_data['sector'][current_date]

                for sector_name, sector_fund in sector_flow.items():
                    if sector_name in volatility_data['sector']:
                        sector_volatility = volatility_data['sector'][sector_name]

                        # 获取资金流数据
                        net_inflow = sector_fund.get('net_inflow', 0)
                        super_large_inflow = sector_fund.get('super_large_inflow', 0)

                        # 获取波动率数据
                        realized_volatility = sector_volatility.get('realized_volatility', 0)

                        # 计算耦合系数
                        # 耦合系数 = 行业资金净流入 * 已实现波动率 / 100
                        coupling_coefficient = net_inflow * realized_volatility / 100

                        # 创建耦合结果
                        coupling_key = f"sector_{sector_name}"
                        coupling_result[coupling_key] = {
                            'type': 'sector',
                            'sector_name': sector_name,
                            'net_inflow': net_inflow,
                            'super_large_inflow': super_large_inflow,
                            'realized_volatility': realized_volatility,
                            'coupling_coefficient': coupling_coefficient,
                            'date': current_date,
                            'calculated_at': datetime.now().isoformat()
                        }

            # 3. 个股资金流-个股波动率耦合
            if 'stock' in fund_flow_data and current_date in fund_flow_data['stock']:
                stock_flow = fund_flow_data['stock'][current_date]

                for stock_code, stock_fund in stock_flow.items():
                    if stock_code in volatility_data['stock']:
                        stock_volatility = volatility_data['stock'][stock_code]

                        # 获取资金流数据
                        net_inflow = stock_fund.get('net_inflow', 0)
                        super_large_inflow = stock_fund.get('super_large_inflow', 0)

                        # 获取波动率数据
                        stock_name = stock_volatility.get('name', '')
                        realized_volatility = stock_volatility.get('realized_volatility', 0)

                        # 计算耦合系数
                        # 耦合系数 = 个股资金净流入(万元) * 已实现波动率 / 10000
                        coupling_coefficient = net_inflow * realized_volatility / 10000

                        # 创建耦合结果
                        coupling_key = f"stock_{stock_code}"
                        coupling_result[coupling_key] = {
                            'type': 'stock',
                            'stock_code': stock_code,
                            'stock_name': stock_name,
                            'net_inflow': net_inflow,
                            'super_large_inflow': super_large_inflow,
                            'realized_volatility': realized_volatility,
                            'coupling_coefficient': coupling_coefficient,
                            'date': current_date,
                            'calculated_at': datetime.now().isoformat()
                        }

            logger.info(f"计算资金流-波动率耦合成功，共{len(coupling_result)}个结果")
            return coupling_result

        except Exception as e:
            logger.error(f"计算资金流-波动率耦合失败: {str(e)}")
            return {}

    def _update_fund_flow_coupling(self, coupling_result: Dict[str, Dict[str, Any]]):
        """
        更新资金流-波动率耦合数据

        Args:
            coupling_result: 资金流-波动率耦合计算结果
        """
        # 获取现有数据
        existing_fund_flow_coupling = self.volatility_data.get('fund_flow_coupling', {})

        # 更新数据
        current_date = datetime.now().strftime('%Y-%m-%d')

        if current_date not in existing_fund_flow_coupling:
            existing_fund_flow_coupling[current_date] = {}

        for coupling_key, coupling in coupling_result.items():
            existing_fund_flow_coupling[current_date][coupling_key] = coupling

        # 只保留最近30天的数据
        if len(existing_fund_flow_coupling) > 30:
            # 按日期排序
            sorted_dates = sorted(existing_fund_flow_coupling.keys(), reverse=True)

            # 只保留最近30天
            new_fund_flow_coupling = {}
            for date in sorted_dates[:30]:
                new_fund_flow_coupling[date] = existing_fund_flow_coupling[date]

            existing_fund_flow_coupling = new_fund_flow_coupling

        # 更新数据
        self.volatility_data['fund_flow_coupling'] = existing_fund_flow_coupling

        # 保存数据
        self.data_storage.save('volatility_analyzer', 'volatility_data', self.volatility_data, StorageLevel.WARM)

        logger.info(f"资金流-波动率耦合数据更新成功，共{len(coupling_result)}个结果")

    def _generate_fund_flow_coupling_signals(self, coupling_result: Dict[str, Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        生成资金流-波动率耦合信号

        Args:
            coupling_result: 资金流-波动率耦合计算结果

        Returns:
            signals: 资金流-波动率耦合信号列表
        """
        signals = []

        # 获取现有信号
        existing_signals = self.volatility_data.get('signals', [])

        # 处理每个耦合结果
        for coupling_key, coupling in coupling_result.items():
            coupling_type = coupling.get('type', '')
            coupling_coefficient = coupling.get('coupling_coefficient', 0)

            # 根据耦合类型生成信号
            if coupling_type == 'north_market':
                index_code = coupling.get('index_code', '')
                index_name = coupling.get('index_name', '')
                north_net_flow = coupling.get('north_net_flow', 0)
                realized_volatility = coupling.get('realized_volatility', 0)

                # 北向资金-市场波动率强耦合信号
                if abs(coupling_coefficient) > 30:  # 耦合系数阈值
                    signal_type = 'north_market_strong_coupling_positive' if coupling_coefficient > 0 else 'north_market_strong_coupling_negative'
                    description = f'北向资金-市场波动率强{"正" if coupling_coefficient > 0 else "负"}耦合信号：北向资金净{"流入" if north_net_flow > 0 else "流出"}{abs(north_net_flow):.2f}亿元，{index_name}已实现波动率{realized_volatility:.2f}%，耦合系数{coupling_coefficient:.2f}'

                    signal = {
                        'type': signal_type,
                        'index_code': index_code,
                        'index_name': index_name,
                        'date': coupling.get('date', ''),
                        'north_net_flow': north_net_flow,
                        'realized_volatility': realized_volatility,
                        'coupling_coefficient': coupling_coefficient,
                        'strength': abs(coupling_coefficient) / 30,
                        'description': description,
                        'created_at': datetime.now().isoformat()
                    }

                    signals.append(signal)

            elif coupling_type == 'sector':
                sector_name = coupling.get('sector_name', '')
                net_inflow = coupling.get('net_inflow', 0)
                realized_volatility = coupling.get('realized_volatility', 0)

                # 行业资金流-波动率强耦合信号
                if abs(coupling_coefficient) > 20:  # 耦合系数阈值
                    signal_type = 'sector_strong_coupling_positive' if coupling_coefficient > 0 else 'sector_strong_coupling_negative'
                    description = f'行业资金流-波动率强{"正" if coupling_coefficient > 0 else "负"}耦合信号：{sector_name}资金净{"流入" if net_inflow > 0 else "流出"}{abs(net_inflow):.2f}亿元，已实现波动率{realized_volatility:.2f}%，耦合系数{coupling_coefficient:.2f}'

                    signal = {
                        'type': signal_type,
                        'sector_name': sector_name,
                        'date': coupling.get('date', ''),
                        'net_inflow': net_inflow,
                        'realized_volatility': realized_volatility,
                        'coupling_coefficient': coupling_coefficient,
                        'strength': abs(coupling_coefficient) / 20,
                        'description': description,
                        'created_at': datetime.now().isoformat()
                    }

                    signals.append(signal)

            elif coupling_type == 'stock':
                stock_code = coupling.get('stock_code', '')
                stock_name = coupling.get('stock_name', '')
                net_inflow = coupling.get('net_inflow', 0)
                realized_volatility = coupling.get('realized_volatility', 0)

                # 个股资金流-波动率强耦合信号
                if abs(coupling_coefficient) > 50:  # 耦合系数阈值
                    signal_type = 'stock_strong_coupling_positive' if coupling_coefficient > 0 else 'stock_strong_coupling_negative'
                    description = f'个股资金流-波动率强{"正" if coupling_coefficient > 0 else "负"}耦合信号：{stock_name}({stock_code})资金净{"流入" if net_inflow > 0 else "流出"}{abs(net_inflow):.2f}万元，已实现波动率{realized_volatility:.2f}%，耦合系数{coupling_coefficient:.2f}'

                    signal = {
                        'type': signal_type,
                        'stock_code': stock_code,
                        'stock_name': stock_name,
                        'date': coupling.get('date', ''),
                        'net_inflow': net_inflow,
                        'realized_volatility': realized_volatility,
                        'coupling_coefficient': coupling_coefficient,
                        'strength': abs(coupling_coefficient) / 50,
                        'description': description,
                        'created_at': datetime.now().isoformat()
                    }

                    signals.append(signal)

        # 合并信号
        combined_signals = existing_signals + signals

        # 去重
        unique_signals = []
        signal_keys = set()

        for signal in combined_signals:
            # 创建信号键
            if 'stock_code' in signal:
                key = f"{signal['type']}_{signal['stock_code']}_{signal['date']}"
            elif 'sector_name' in signal:
                key = f"{signal['type']}_{signal['sector_name']}_{signal['date']}"
            elif 'index_code' in signal:
                key = f"{signal['type']}_{signal['index_code']}_{signal['date']}"
            elif 'policy_id' in signal and 'index_code' in signal:
                key = f"{signal['type']}_{signal['policy_id']}_{signal['index_code']}_{signal['date']}"
            else:
                key = f"{signal['type']}_{signal['date']}"

            if key not in signal_keys:
                unique_signals.append(signal)
                signal_keys.add(key)

        # 只保留最近100个信号
        if len(unique_signals) > 100:
            unique_signals = sorted(unique_signals, key=lambda x: x['created_at'], reverse=True)[:100]

        # 更新信号
        self.volatility_data['signals'] = unique_signals

        # 保存数据
        self.data_storage.save('volatility_analyzer', 'volatility_data', self.volatility_data, StorageLevel.WARM)

        logger.info(f"生成资金流-波动率耦合信号{len(signals)}个")

        return signals

    def get_market_volatility_analysis(self) -> Dict[str, Any]:
        """
        获取市场波动率分析

        Returns:
            analysis: 市场波动率分析
        """
        try:
            # 获取市场波动率数据
            market_volatility = self._get_latest_market_volatility()

            if not market_volatility:
                return {
                    'status': 'warning',
                    'message': '没有市场波动率数据',
                    'data': None
                }

            # 创建分析结果
            analysis = {
                'date': datetime.now().strftime('%Y-%m-%d'),
                'indices': [],
                'volatility_ranking': [],
                'volatility_trend': self._analyze_market_volatility_trend(),
                'signals': self._get_market_volatility_signals()
            }

            # 处理每个指数
            for code, volatility in market_volatility.items():
                index_name = volatility.get('name', '')
                historical_volatility = volatility.get('historical_volatility', 0)
                realized_volatility = volatility.get('realized_volatility', 0)

                # 添加指数分析
                index_analysis = {
                    'code': code,
                    'name': index_name,
                    'historical_volatility': historical_volatility,
                    'realized_volatility': realized_volatility,
                    'volatility_ratio': realized_volatility / historical_volatility if historical_volatility > 0 else 0,
                    'volatility_cone': volatility.get('volatility_cone', {}),
                    'volatility_skew': volatility.get('volatility_skew', 0),
                    'term_structure': volatility.get('term_structure', {})
                }

                analysis['indices'].append(index_analysis)

                # 添加到波动率排名
                analysis['volatility_ranking'].append({
                    'code': code,
                    'name': index_name,
                    'realized_volatility': realized_volatility
                })

            # 按已实现波动率排序
            analysis['volatility_ranking'] = sorted(analysis['volatility_ranking'], key=lambda x: x['realized_volatility'], reverse=True)

            return {
                'status': 'success',
                'message': '市场波动率分析完成',
                'data': analysis
            }

        except Exception as e:
            logger.error(f"获取市场波动率分析失败: {str(e)}")
            return {
                'status': 'error',
                'message': f'获取市场波动率分析失败: {str(e)}',
                'error': str(e),
                'data': None
            }

    def _analyze_market_volatility_trend(self) -> Dict[str, Any]:
        """
        分析市场波动率趋势

        Returns:
            trend: 市场波动率趋势
        """
        try:
            # 获取市场波动率数据
            market_volatility_data = self.volatility_data.get('market', {})

            if not market_volatility_data:
                return {}

            # 获取最近30天的数据
            sorted_dates = sorted(market_volatility_data.keys(), reverse=True)

            if len(sorted_dates) < 2:
                return {}

            # 只保留最近30天
            recent_dates = sorted_dates[:min(30, len(sorted_dates))]

            # 获取沪深300指数的波动率趋势
            hs300_trend = []

            for date in recent_dates:
                if '000300' in market_volatility_data[date]:
                    hs300_volatility = market_volatility_data[date]['000300']

                    hs300_trend.append({
                        'date': date,
                        'realized_volatility': hs300_volatility.get('realized_volatility', 0),
                        'historical_volatility': hs300_volatility.get('historical_volatility', 0)
                    })

            # 计算波动率趋势
            if len(hs300_trend) >= 2:
                latest = hs300_trend[0]
                previous = hs300_trend[1]

                realized_change = latest['realized_volatility'] - previous['realized_volatility']
                historical_change = latest['historical_volatility'] - previous['historical_volatility']

                trend_direction = 'up' if realized_change > 0 else 'down' if realized_change < 0 else 'flat'
                trend_strength = abs(realized_change) / previous['realized_volatility'] if previous['realized_volatility'] > 0 else 0

                return {
                    'index': '沪深300',
                    'trend_direction': trend_direction,
                    'trend_strength': trend_strength,
                    'realized_change': realized_change,
                    'historical_change': historical_change,
                    'data': hs300_trend
                }

            return {}

        except Exception as e:
            logger.error(f"分析市场波动率趋势失败: {str(e)}")
            return {}

    def _get_market_volatility_signals(self) -> List[Dict[str, Any]]:
        """
        获取市场波动率信号

        Returns:
            signals: 市场波动率信号列表
        """
        try:
            # 获取所有信号
            all_signals = self.volatility_data.get('signals', [])

            # 筛选市场波动率信号
            market_signals = []

            for signal in all_signals:
                signal_type = signal.get('type', '')

                if signal_type.startswith('market_'):
                    market_signals.append(signal)

            # 按创建时间排序
            market_signals = sorted(market_signals, key=lambda x: x['created_at'], reverse=True)

            return market_signals

        except Exception as e:
            logger.error(f"获取市场波动率信号失败: {str(e)}")
            return []

    def get_sector_volatility_analysis(self) -> Dict[str, Any]:
        """
        获取行业波动率分析

        Returns:
            analysis: 行业波动率分析
        """
        try:
            # 获取行业波动率数据
            sector_volatility = self._get_latest_sector_volatility()

            if not sector_volatility:
                return {
                    'status': 'warning',
                    'message': '没有行业波动率数据',
                    'data': None
                }

            # 创建分析结果
            analysis = {
                'date': datetime.now().strftime('%Y-%m-%d'),
                'sectors': [],
                'volatility_ranking': [],
                'signals': self._get_sector_volatility_signals()
            }

            # 处理每个行业
            for sector_name, volatility in sector_volatility.items():
                historical_volatility = volatility.get('historical_volatility', 0)
                realized_volatility = volatility.get('realized_volatility', 0)

                # 添加行业分析
                sector_analysis = {
                    'name': sector_name,
                    'historical_volatility': historical_volatility,
                    'realized_volatility': realized_volatility,
                    'volatility_ratio': realized_volatility / historical_volatility if historical_volatility > 0 else 0,
                    'volatility_skew': volatility.get('volatility_skew', 0),
                    'stocks_count': volatility.get('stocks_count', 0)
                }

                analysis['sectors'].append(sector_analysis)

                # 添加到波动率排名
                analysis['volatility_ranking'].append({
                    'name': sector_name,
                    'realized_volatility': realized_volatility
                })

            # 按已实现波动率排序
            analysis['volatility_ranking'] = sorted(analysis['volatility_ranking'], key=lambda x: x['realized_volatility'], reverse=True)

            return {
                'status': 'success',
                'message': '行业波动率分析完成',
                'data': analysis
            }

        except Exception as e:
            logger.error(f"获取行业波动率分析失败: {str(e)}")
            return {
                'status': 'error',
                'message': f'获取行业波动率分析失败: {str(e)}',
                'error': str(e),
                'data': None
            }

    def _get_sector_volatility_signals(self) -> List[Dict[str, Any]]:
        """
        获取行业波动率信号

        Returns:
            signals: 行业波动率信号列表
        """
        try:
            # 获取所有信号
            all_signals = self.volatility_data.get('signals', [])

            # 筛选行业波动率信号
            sector_signals = []

            for signal in all_signals:
                signal_type = signal.get('type', '')

                if signal_type.startswith('sector_'):
                    sector_signals.append(signal)

            # 按创建时间排序
            sector_signals = sorted(sector_signals, key=lambda x: x['created_at'], reverse=True)

            return sector_signals

        except Exception as e:
            logger.error(f"获取行业波动率信号失败: {str(e)}")
            return []

    def get_stock_volatility_analysis(self) -> Dict[str, Any]:
        """
        获取个股波动率分析

        Returns:
            analysis: 个股波动率分析
        """
        try:
            # 获取个股波动率数据
            stock_volatility = self._get_latest_stock_volatility()

            if not stock_volatility:
                return {
                    'status': 'warning',
                    'message': '没有个股波动率数据',
                    'data': None
                }

            # 创建分析结果
            analysis = {
                'date': datetime.now().strftime('%Y-%m-%d'),
                'stocks': [],
                'volatility_ranking': [],
                'signals': self._get_stock_volatility_signals()
            }

            # 处理每只股票
            for code, volatility in stock_volatility.items():
                stock_name = volatility.get('name', '')
                historical_volatility = volatility.get('historical_volatility', 0)
                realized_volatility = volatility.get('realized_volatility', 0)

                # 添加股票分析
                stock_analysis = {
                    'code': code,
                    'name': stock_name,
                    'historical_volatility': historical_volatility,
                    'realized_volatility': realized_volatility,
                    'volatility_ratio': realized_volatility / historical_volatility if historical_volatility > 0 else 0,
                    'volatility_skew': volatility.get('volatility_skew', 0),
                    'latest_price': volatility.get('latest_price', 0),
                    'price_change_5d': volatility.get('price_change_5d', 0)
                }

                analysis['stocks'].append(stock_analysis)

                # 添加到波动率排名
                analysis['volatility_ranking'].append({
                    'code': code,
                    'name': stock_name,
                    'realized_volatility': realized_volatility
                })

            # 按已实现波动率排序
            analysis['volatility_ranking'] = sorted(analysis['volatility_ranking'], key=lambda x: x['realized_volatility'], reverse=True)

            # 只保留前50只股票
            analysis['volatility_ranking'] = analysis['volatility_ranking'][:50]

            return {
                'status': 'success',
                'message': '个股波动率分析完成',
                'data': analysis
            }

        except Exception as e:
            logger.error(f"获取个股波动率分析失败: {str(e)}")
            return {
                'status': 'error',
                'message': f'获取个股波动率分析失败: {str(e)}',
                'error': str(e),
                'data': None
            }

    def _get_stock_volatility_signals(self) -> List[Dict[str, Any]]:
        """
        获取个股波动率信号

        Returns:
            signals: 个股波动率信号列表
        """
        try:
            # 获取所有信号
            all_signals = self.volatility_data.get('signals', [])

            # 筛选个股波动率信号
            stock_signals = []

            for signal in all_signals:
                signal_type = signal.get('type', '')

                if signal_type.startswith('stock_'):
                    stock_signals.append(signal)

            # 按创建时间排序
            stock_signals = sorted(stock_signals, key=lambda x: x['created_at'], reverse=True)

            return stock_signals

        except Exception as e:
            logger.error(f"获取个股波动率信号失败: {str(e)}")
            return []

    def get_policy_premium_analysis(self) -> Dict[str, Any]:
        """
        获取政策波动率溢价分析

        Returns:
            analysis: 政策波动率溢价分析
        """
        try:
            # 获取政策波动率溢价数据
            policy_premium = self._get_latest_policy_premium()

            if not policy_premium:
                return {
                    'status': 'warning',
                    'message': '没有政策波动率溢价数据',
                    'data': None
                }

            # 创建分析结果
            analysis = {
                'date': datetime.now().strftime('%Y-%m-%d'),
                'premiums': [],
                'premium_ranking': [],
                'signals': self._get_policy_premium_signals()
            }

            # 处理每个溢价结果
            for premium_key, premium in policy_premium.items():
                policy_id = premium.get('policy_id', '')
                policy_title = premium.get('policy_title', '')
                index_code = premium.get('index_code', '')
                index_name = premium.get('index_name', '')
                policy_premium_value = premium.get('policy_premium', 0)

                # 添加溢价分析
                premium_analysis = {
                    'policy_id': policy_id,
                    'policy_title': policy_title,
                    'index_code': index_code,
                    'index_name': index_name,
                    'policy_premium': policy_premium_value,
                    'volatility_ratio': premium.get('volatility_ratio', 0),
                    'impact_factor': premium.get('impact_factor', 0)
                }

                analysis['premiums'].append(premium_analysis)

                # 添加到溢价排名
                analysis['premium_ranking'].append({
                    'policy_id': policy_id,
                    'policy_title': policy_title,
                    'index_name': index_name,
                    'policy_premium': policy_premium_value
                })

            # 按溢价排序
            analysis['premium_ranking'] = sorted(analysis['premium_ranking'], key=lambda x: x['policy_premium'], reverse=True)

            return {
                'status': 'success',
                'message': '政策波动率溢价分析完成',
                'data': analysis
            }

        except Exception as e:
            logger.error(f"获取政策波动率溢价分析失败: {str(e)}")
            return {
                'status': 'error',
                'message': f'获取政策波动率溢价分析失败: {str(e)}',
                'error': str(e),
                'data': None
            }

    def _get_latest_policy_premium(self) -> Dict[str, Dict[str, Any]]:
        """
        获取最新政策波动率溢价数据

        Returns:
            policy_premium: 政策波动率溢价数据
        """
        try:
            # 获取政策波动率溢价数据
            policy_premium_data = self.volatility_data.get('policy_premium', {})

            if not policy_premium_data:
                logger.warning("未获取到政策波动率溢价数据")
                return {}

            # 获取最新日期
            sorted_dates = sorted(policy_premium_data.keys(), reverse=True)

            if not sorted_dates:
                logger.warning("未获取到政策波动率溢价数据日期")
                return {}

            latest_date = sorted_dates[0]

            # 获取最新政策波动率溢价数据
            latest_policy_premium = policy_premium_data[latest_date]

            return latest_policy_premium

        except Exception as e:
            logger.error(f"获取最新政策波动率溢价数据失败: {str(e)}")
            return {}

    def _get_policy_premium_signals(self) -> List[Dict[str, Any]]:
        """
        获取政策波动率溢价信号

        Returns:
            signals: 政策波动率溢价信号列表
        """
        try:
            # 获取所有信号
            all_signals = self.volatility_data.get('signals', [])

            # 筛选政策波动率溢价信号
            policy_signals = []

            for signal in all_signals:
                signal_type = signal.get('type', '')

                if signal_type.startswith('policy_'):
                    policy_signals.append(signal)

            # 按创建时间排序
            policy_signals = sorted(policy_signals, key=lambda x: x['created_at'], reverse=True)

            return policy_signals

        except Exception as e:
            logger.error(f"获取政策波动率溢价信号失败: {str(e)}")
            return []

    def get_fund_flow_coupling_analysis(self) -> Dict[str, Any]:
        """
        获取资金流-波动率耦合分析

        Returns:
            analysis: 资金流-波动率耦合分析
        """
        try:
            # 获取资金流-波动率耦合数据
            fund_flow_coupling = self._get_latest_fund_flow_coupling()

            if not fund_flow_coupling:
                return {
                    'status': 'warning',
                    'message': '没有资金流-波动率耦合数据',
                    'data': None
                }

            # 创建分析结果
            analysis = {
                'date': datetime.now().strftime('%Y-%m-%d'),
                'couplings': [],
                'coupling_ranking': [],
                'signals': self._get_fund_flow_coupling_signals()
            }

            # 处理每个耦合结果
            for coupling_key, coupling in fund_flow_coupling.items():
                coupling_type = coupling.get('type', '')
                coupling_coefficient = coupling.get('coupling_coefficient', 0)

                # 添加耦合分析
                if coupling_type == 'north_market':
                    index_code = coupling.get('index_code', '')
                    index_name = coupling.get('index_name', '')
                    north_net_flow = coupling.get('north_net_flow', 0)
                    realized_volatility = coupling.get('realized_volatility', 0)

                    coupling_analysis = {
                        'type': coupling_type,
                        'index_code': index_code,
                        'index_name': index_name,
                        'north_net_flow': north_net_flow,
                        'realized_volatility': realized_volatility,
                        'coupling_coefficient': coupling_coefficient
                    }

                    analysis['couplings'].append(coupling_analysis)

                    # 添加到耦合排名
                    analysis['coupling_ranking'].append({
                        'type': 'north_market',
                        'name': f'北向资金-{index_name}',
                        'coupling_coefficient': coupling_coefficient
                    })

                elif coupling_type == 'sector':
                    sector_name = coupling.get('sector_name', '')
                    net_inflow = coupling.get('net_inflow', 0)
                    realized_volatility = coupling.get('realized_volatility', 0)

                    coupling_analysis = {
                        'type': coupling_type,
                        'sector_name': sector_name,
                        'net_inflow': net_inflow,
                        'realized_volatility': realized_volatility,
                        'coupling_coefficient': coupling_coefficient
                    }

                    analysis['couplings'].append(coupling_analysis)

                    # 添加到耦合排名
                    analysis['coupling_ranking'].append({
                        'type': 'sector',
                        'name': sector_name,
                        'coupling_coefficient': coupling_coefficient
                    })

                elif coupling_type == 'stock':
                    stock_code = coupling.get('stock_code', '')
                    stock_name = coupling.get('stock_name', '')
                    net_inflow = coupling.get('net_inflow', 0)
                    realized_volatility = coupling.get('realized_volatility', 0)

                    coupling_analysis = {
                        'type': coupling_type,
                        'stock_code': stock_code,
                        'stock_name': stock_name,
                        'net_inflow': net_inflow,
                        'realized_volatility': realized_volatility,
                        'coupling_coefficient': coupling_coefficient
                    }

                    analysis['couplings'].append(coupling_analysis)

                    # 添加到耦合排名
                    analysis['coupling_ranking'].append({
                        'type': 'stock',
                        'name': f'{stock_name}({stock_code})',
                        'coupling_coefficient': coupling_coefficient
                    })

            # 按耦合系数排序
            analysis['coupling_ranking'] = sorted(analysis['coupling_ranking'], key=lambda x: abs(x['coupling_coefficient']), reverse=True)

            # 只保留前30个耦合结果
            analysis['coupling_ranking'] = analysis['coupling_ranking'][:30]

            return {
                'status': 'success',
                'message': '资金流-波动率耦合分析完成',
                'data': analysis
            }

        except Exception as e:
            logger.error(f"获取资金流-波动率耦合分析失败: {str(e)}")
            return {
                'status': 'error',
                'message': f'获取资金流-波动率耦合分析失败: {str(e)}',
                'error': str(e),
                'data': None
            }

    def _get_latest_fund_flow_coupling(self) -> Dict[str, Dict[str, Any]]:
        """
        获取最新资金流-波动率耦合数据

        Returns:
            fund_flow_coupling: 资金流-波动率耦合数据
        """
        try:
            # 获取资金流-波动率耦合数据
            fund_flow_coupling_data = self.volatility_data.get('fund_flow_coupling', {})

            if not fund_flow_coupling_data:
                logger.warning("未获取到资金流-波动率耦合数据")
                return {}

            # 获取最新日期
            sorted_dates = sorted(fund_flow_coupling_data.keys(), reverse=True)

            if not sorted_dates:
                logger.warning("未获取到资金流-波动率耦合数据日期")
                return {}

            latest_date = sorted_dates[0]

            # 获取最新资金流-波动率耦合数据
            latest_fund_flow_coupling = fund_flow_coupling_data[latest_date]

            return latest_fund_flow_coupling

        except Exception as e:
            logger.error(f"获取最新资金流-波动率耦合数据失败: {str(e)}")
            return {}

    def _get_fund_flow_coupling_signals(self) -> List[Dict[str, Any]]:
        """
        获取资金流-波动率耦合信号

        Returns:
            signals: 资金流-波动率耦合信号列表
        """
        try:
            # 获取所有信号
            all_signals = self.volatility_data.get('signals', [])

            # 筛选资金流-波动率耦合信号
            coupling_signals = []

            for signal in all_signals:
                signal_type = signal.get('type', '')

                if ('coupling' in signal_type) or ('north_market_strong' in signal_type) or ('sector_strong' in signal_type) or ('stock_strong' in signal_type):
                    coupling_signals.append(signal)

            # 按创建时间排序
            coupling_signals = sorted(coupling_signals, key=lambda x: x['created_at'], reverse=True)

            return coupling_signals

        except Exception as e:
            logger.error(f"获取资金流-波动率耦合信号失败: {str(e)}")
            return []

    def generate_volatility_report(self, **kwargs) -> Dict[str, Any]:
        """
        生成波动率分析报告

        Returns:
            result: 生成结果
        """
        try:
            logger.info("开始生成波动率分析报告...")

            # 获取市场波动率分析
            market_analysis = self.get_market_volatility_analysis()

            # 获取行业波动率分析
            sector_analysis = self.get_sector_volatility_analysis()

            # 获取个股波动率分析
            stock_analysis = self.get_stock_volatility_analysis()

            # 获取政策波动率溢价分析
            policy_premium_analysis = self.get_policy_premium_analysis()

            # 获取资金流-波动率耦合分析
            fund_flow_coupling_analysis = self.get_fund_flow_coupling_analysis()

            # 创建报告
            report = {
                'date': datetime.now().strftime('%Y-%m-%d'),
                'market_analysis': market_analysis.get('data', {}),
                'sector_analysis': sector_analysis.get('data', {}),
                'stock_analysis': stock_analysis.get('data', {}),
                'policy_premium_analysis': policy_premium_analysis.get('data', {}),
                'fund_flow_coupling_analysis': fund_flow_coupling_analysis.get('data', {}),
                'created_at': datetime.now().isoformat()
            }

            # 保存报告
            self.data_storage.save('volatility_analyzer', 'volatility_report', report, StorageLevel.WARM)

            logger.info("波动率分析报告生成成功")

            return {
                'status': 'success',
                'message': '波动率分析报告生成成功',
                'report': report
            }

        except Exception as e:
            logger.error(f"生成波动率分析报告失败: {str(e)}")
            return {
                'status': 'error',
                'message': f'生成波动率分析报告失败: {str(e)}',
                'error': str(e)
            }

