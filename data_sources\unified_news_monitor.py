"""
统一新闻监控模块

整合news_monitor.py和enhanced_news_monitor.py的功能，
提供更高效、更稳定的新闻监控功能
"""

import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import time
import logging
import json
import re
import threading
import schedule
from collections import defaultdict, Counter
import jieba.analyse
import random
import queue
import signal
import sys

# 导入模块接口
from core.module_interface import ModuleInterface

# 导入数据存储
from core.data_storage import DataStorage, StorageLevel

# 导入数据源
from data_sources.policy_data import PolicyDataSource
from data_sources.news_processor import NewsProcessor

# 导入新闻去重与聚类模块
try:
    from data_sources.news_clustering import NewsClusteringModule
    CLUSTERING_AVAILABLE = True
except ImportError:
    CLUSTERING_AVAILABLE = False
    logger.warning("新闻去重与聚类模块未安装，将使用简化版去重")

# 导入其他依赖
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/unified_news_monitor.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('unified_news_monitor')

class UnifiedNewsMonitor(ModuleInterface):
    """统一新闻监控类"""

    def __init__(self):
        """初始化统一新闻监控器"""
        super().__init__(module_name='unified_news_monitor')

        # 创建数据源和处理器
        self.policy_data = PolicyDataSource()
        self.news_processor = NewsProcessor()

        # 创建数据存储
        self.data_storage = DataStorage()

        # 创建新闻去重与聚类模块
        self.news_clustering = NewsClusteringModule() if CLUSTERING_AVAILABLE else None

        # 任务队列
        self.task_queue = queue.Queue()

        # 错误计数和重试机制
        self.error_counts = defaultdict(int)
        self.max_retries = self.config.get('max_retries', 3)
        self.backoff_factor = self.config.get('backoff_factor', 2)  # 指数退避因子

        # 上次更新时间
        self.last_update_time = datetime.now()
        self.source_last_update = {}  # 每个数据源的最后更新时间

        # 增量更新标志
        self.incremental_update = self.config.get('incremental_update', True)

        # 股票代码映射表
        self.stock_code_map = self._load_stock_code_map()

        # 板块映射表
        self.sector_map = self._load_sector_map()

        # 关键词映射表
        self.keyword_map = self._load_keyword_map()

        # 热点板块和个股
        self.hot_sectors = defaultdict(float)
        self.hot_stocks = defaultdict(float)

        # 重要新闻
        self.important_news = []

        # 运行状态
        self.is_running = False
        self.monitor_thread = None

        # 加载停用词
        self.stopwords = self._load_stopwords()

        # 初始化TF-IDF向量化器
        self.vectorizer = TfidfVectorizer(
            max_features=1000,
            stop_words=self.stopwords
        )

        # 加载处理后的新闻
        self.processed_news = self.data_storage.load('unified_news_monitor', 'processed_news', [])

        # 加载新闻源权重
        self.source_weights = self.config.get('source_weights', {
            'default': 1.0,
            '东方财富': 1.2,
            '新浪财经': 1.1,
            '财联社': 1.3,
            '证券时报': 1.2,
            '中国证券报': 1.2,
            '上海证券报': 1.2,
            '证券日报': 1.2,
            '国务院': 1.5,
            '发改委': 1.4,
            '证监会': 1.4,
            '央行': 1.4
        })

        logger.info("统一新闻监控器初始化完成")

    def _load_stock_code_map(self):
        """加载股票代码映射表"""
        # 尝试从数据存储加载
        stock_map = self.data_storage.load('unified_news_monitor', 'stock_code_map')
        if stock_map:
            logger.info(f"从数据存储加载了{len(stock_map)}个股票代码")
            return stock_map

        # 如果数据存储中没有，尝试从文件加载
        try:
            stock_file = os.path.join('data', 'unified_news_monitor', 'stock_code_map.json')
            if os.path.exists(stock_file):
                with open(stock_file, 'r', encoding='utf-8') as f:
                    stock_map = json.load(f)
                logger.info(f"从文件加载了{len(stock_map)}个股票代码")

                # 保存到数据存储
                self.data_storage.save('unified_news_monitor', 'stock_code_map', stock_map)

                return stock_map
        except Exception as e:
            logger.error(f"加载股票代码映射文件失败: {str(e)}")

        # 如果都没有，使用默认映射
        stock_map = {
            '东方财富': '300059',
            '平安银行': '000001',
            '贵州茅台': '600519',
            '宁德时代': '300750',
            '比亚迪': '002594',
            '中国平安': '601318',
            '招商银行': '600036',
            '五粮液': '000858',
            '隆基绿能': '601012',
            '美的集团': '000333'
        }

        # 保存到数据存储
        self.data_storage.save('unified_news_monitor', 'stock_code_map', stock_map)

        logger.info(f"创建默认股票代码映射，包含{len(stock_map)}个股票")
        return stock_map

    def _load_sector_map(self):
        """加载板块映射表"""
        # 尝试从数据存储加载
        sector_map = self.data_storage.load('unified_news_monitor', 'sector_map')
        if sector_map:
            logger.info(f"从数据存储加载了{len(sector_map)}个板块")
            return sector_map

        # 如果数据存储中没有，尝试从文件加载
        try:
            sector_file = os.path.join('data', 'unified_news_monitor', 'sector_map.json')
            if os.path.exists(sector_file):
                with open(sector_file, 'r', encoding='utf-8') as f:
                    sector_map = json.load(f)
                logger.info(f"从文件加载了{len(sector_map)}个板块")

                # 保存到数据存储
                self.data_storage.save('unified_news_monitor', 'sector_map', sector_map)

                return sector_map
        except Exception as e:
            logger.error(f"加载板块映射文件失败: {str(e)}")

        # 如果都没有，使用默认映射
        sector_map = {
            '芯片': ['中芯国际', '韦尔股份', '北方华创', '兆易创新', '紫光国微'],
            '新能源': ['宁德时代', '隆基绿能', '阳光电源', '比亚迪', '亿纬锂能'],
            '人工智能': ['科大讯飞', '寒武纪', '海康威视', '商汤科技', '旷视科技'],
            '医药': ['恒瑞医药', '迈瑞医疗', '药明康德', '爱尔眼科', '通策医疗'],
            '金融': ['平安银行', '招商银行', '中国平安', '中信证券', '东方财富'],
            '消费': ['贵州茅台', '五粮液', '伊利股份', '海天味业', '美的集团'],
            '科技': ['腾讯控股', '阿里巴巴', '百度', '京东', '网易'],
            '制造': ['三一重工', '中国中车', '潍柴动力', '上汽集团', '格力电器']
        }

        # 保存到数据存储
        self.data_storage.save('unified_news_monitor', 'sector_map', sector_map)

        logger.info(f"创建默认板块映射，包含{len(sector_map)}个板块")
        return sector_map

    def _load_keyword_map(self):
        """加载关键词映射表"""
        # 尝试从数据存储加载
        keyword_map = self.data_storage.load('unified_news_monitor', 'keyword_map')
        if keyword_map:
            logger.info(f"从数据存储加载了{len(keyword_map)}个关键词映射")
            return keyword_map

        # 如果数据存储中没有，使用默认映射
        keyword_map = {
            '芯片': ['芯片', '半导体', 'AI芯片', 'GPU', 'CPU', '集成电路', 'ASIC'],
            '新能源': ['新能源', '光伏', '风电', '储能', '氢能', '电动车', '锂电池'],
            '人工智能': ['人工智能', 'AI', '大模型', '机器学习', '深度学习', 'GPT', '智能化'],
            '医药': ['医药', '生物医药', '创新药', '疫苗', '医疗器械', '医疗服务', '健康'],
            '金融': ['金融', '银行', '保险', '证券', '基金', '信托', '理财'],
            '消费': ['消费', '零售', '电商', '餐饮', '旅游', '娱乐', '奢侈品'],
            '科技': ['科技', '互联网', '云计算', '大数据', '区块链', '元宇宙', '虚拟现实'],
            '制造': ['制造', '工业', '机械', '装备', '自动化', '智能制造', '工业互联网']
        }

        # 保存到数据存储
        self.data_storage.save('unified_news_monitor', 'keyword_map', keyword_map)

        logger.info(f"创建默认关键词映射，包含{len(keyword_map)}个类别")
        return keyword_map

    def _load_stopwords(self) -> list:
        """加载停用词"""
        try:
            stopwords_path = 'data/unified_news_monitor/stopwords.txt'

            if os.path.exists(stopwords_path):
                with open(stopwords_path, 'r', encoding='utf-8') as f:
                    return [line.strip() for line in f]
            else:
                # 创建默认停用词文件
                default_stopwords = [
                    '的', '了', '和', '是', '就', '都', '而', '及', '与', '这', '那', '有', '在',
                    '中', '为', '对', '到', '以', '等', '上', '下', '由', '于', '从', '之', '或',
                    '也', '此', '但', '并', '个', '其', '已', '无', '我', '你', '他', '她', '它',
                    '们', '可', '能', '好', '将', '要', '不', '如', '这样', '那样', '只是', '因为',
                    '所以', '然后', '虽然', '但是', '如果', '什么', '怎么', '一些', '一个', '一样',
                    '一般', '一直', '一种', '一项', '一起', '一边', '一面', '万一', '三天两头'
                ]

                os.makedirs(os.path.dirname(stopwords_path), exist_ok=True)
                with open(stopwords_path, 'w', encoding='utf-8') as f:
                    f.write('\n'.join(default_stopwords))

                return default_stopwords

        except Exception as e:
            logger.error(f"加载停用词失败: {str(e)}")
            return []

    def initialize(self):
        """初始化模块"""
        logger.info("初始化统一新闻监控模块...")

        # 注册定时任务
        if self.scheduler:
            # 每5分钟获取一次新闻
            self.schedule_task(
                function='fetch_news',
                params={},
                priority='medium',
                schedule_time=datetime.now() + timedelta(minutes=5)
            )

            # 每小时分析一次热点
            self.schedule_task(
                function='analyze_hot_topics',
                params={},
                priority='low',
                schedule_time=datetime.now() + timedelta(minutes=10)
            )

            # 每天生成一次日报
            self.schedule_task(
                function='generate_daily_report',
                params={},
                priority='low',
                schedule_time=datetime.now().replace(hour=18, minute=0, second=0)
            )

            # 每天凌晨2点进行一次系统维护
            self.schedule_task(
                function='system_maintenance',
                params={},
                priority='low',
                schedule_time=datetime.now().replace(hour=2, minute=0, second=0) + timedelta(days=1)
            )

        logger.info("统一新闻监控模块初始化完成")

    def get_status(self) -> dict:
        """获取模块状态"""
        return {
            'module_name': self.module_name,
            'enabled': self.config.get('enabled', True),
            'is_running': self.is_running,
            'last_update_time': self.last_update_time.isoformat() if self.last_update_time else None,
            'error_counts': dict(self.error_counts),
            'hot_sectors_count': len(self.hot_sectors),
            'hot_stocks_count': len(self.hot_stocks),
            'important_news_count': len(self.important_news)
        }

    def health_check(self) -> dict:
        """健康检查"""
        try:
            # 检查上次更新时间
            time_since_last_update = datetime.now() - self.last_update_time

            if time_since_last_update > timedelta(hours=2):
                status = 'warning'
                message = f'上次更新时间超过2小时: {self.last_update_time.isoformat()}'
            else:
                status = 'healthy'
                message = f'上次更新时间: {self.last_update_time.isoformat()}'

            # 检查错误计数
            error_sources = [source for source, count in self.error_counts.items() if count >= self.max_retries]
            if error_sources:
                status = 'warning'
                message += f'，{len(error_sources)}个数据源出错过多'

            return {
                'status': status,
                'message': message,
                'last_update_time': self.last_update_time.isoformat(),
                'time_since_last_update': str(time_since_last_update),
                'error_sources': error_sources
            }

        except Exception as e:
            logger.error(f"健康检查失败: {str(e)}")
            return {
                'status': 'error',
                'message': f'健康检查失败: {str(e)}',
                'error': str(e)
            }

    def fetch_news(self, **kwargs):
        """获取新闻"""
        try:
            logger.info("开始获取新闻...")

            # 记录当前时间
            current_time = datetime.now()

            # 从所有来源获取新闻
            all_news = self._fetch_news_from_all_sources()

            if not all_news:
                logger.warning("没有获取到新闻")
                return {
                    'status': 'warning',
                    'message': '没有获取到新闻',
                    'count': 0
                }

            # 处理新闻
            processed_news = self.process_news(all_news)

            # 更新上次更新时间
            self.last_update_time = current_time

            logger.info(f"新闻获取和处理成功，共{len(processed_news)}条")

            return {
                'status': 'success',
                'message': f'新闻获取和处理成功，共{len(processed_news)}条',
                'count': len(processed_news)
            }

        except Exception as e:
            logger.error(f"获取新闻失败: {str(e)}")
            return {
                'status': 'error',
                'message': f'获取新闻失败: {str(e)}',
                'error': str(e)
            }

    def _fetch_news_from_all_sources(self):
        """从所有来源获取新闻"""
        all_news = []

        # 定义数据源和对应的获取方法
        sources = [
            {
                'name': '财经早餐',
                'method': self.policy_data.get_financial_breakfast,
                'params': {},
                'max_items': 50
            },
            {
                'name': '全球财经快讯-东财财富',
                'method': self.policy_data.get_global_news_em,
                'params': {},
                'max_items': 50
            },
            {
                'name': '全球财经快讯-新浪财经',
                'method': self.policy_data.get_global_news_sina,
                'params': {},
                'max_items': 50
            },
            {
                'name': '快讯-富途牛牛',
                'method': self.policy_data.get_global_news_futu,
                'params': {},
                'max_items': 50
            },
            {
                'name': '全球财经直播-同花顺财经',
                'method': self.policy_data.get_global_news_ths,
                'params': {},
                'max_items': 50
            },
            {
                'name': '电报-财联社',
                'method': self.policy_data.get_global_news_cls,
                'params': {'symbol': '全部'},
                'max_items': 50
            },
            {
                'name': '证券原创-新浪财经',
                'method': self.policy_data.get_broker_news_sina,
                'params': {'page': '1'},
                'max_items': 20
            },
            {
                'name': '财经内容精选-财新网',
                'method': self.policy_data.get_news_main_cx,
                'params': {},
                'max_items': 50
            },
            {
                'name': '国务院政策',
                'method': self.policy_data.get_gov_policy,
                'params': {'page': 1, 'limit': 20},
                'max_items': 20
            },
            {
                'name': '发改委政策',
                'method': self.policy_data.get_ndrc_policy,
                'params': {'page': 1, 'limit': 20},
                'max_items': 20
            }
        ]

        # 添加个股新闻源
        for stock_name, stock_code in list(self.stock_code_map.items())[:5]:  # 限制获取前5只股票的新闻
            sources.append({
                'name': f'个股新闻-{stock_name}',
                'method': self.policy_data.get_stock_news_em,
                'params': {'symbol': stock_code},
                'max_items': 20
            })

        # 随机打乱数据源顺序，避免总是同一个顺序
        random.shuffle(sources)

        # 获取每个数据源的新闻
        for source in sources:
            source_name = source['name']

            # 获取上次更新时间
            last_update = self.source_last_update.get(source_name, datetime.now() - timedelta(days=1))

            try:
                logger.info(f"从{source_name}获取新闻")
                start_time = time.time()

                # 调用获取方法
                news_df = source['method'](**source['params'], use_cache=False)

                # 限制条数
                if len(news_df) > source['max_items']:
                    news_df = news_df.head(source['max_items'])

                # 如果是增量更新模式，只保留上次更新后的新闻
                if self.incremental_update:
                    # 尝试将发布时间转换为datetime对象
                    if 'publish_date' in news_df.columns:
                        try:
                            news_df['publish_datetime'] = pd.to_datetime(news_df['publish_date'], errors='coerce')
                            # 只保留上次更新后的新闻
                            news_df = news_df[news_df['publish_datetime'] > last_update]
                            # 删除临时列
                            news_df = news_df.drop(columns=['publish_datetime'])
                        except:
                            logger.warning(f"解析{source_name}的发布时间失败")

                # 将DataFrame转换为字典列表
                for _, row in news_df.iterrows():
                    all_news.append(row.to_dict())

                # 更新上次更新时间
                self.source_last_update[source_name] = datetime.now()

                # 重置错误计数
                self.error_counts[source_name] = 0

                end_time = time.time()
                logger.info(f"从{source_name}获取了{len(news_df)}条新闻，耗时{end_time - start_time:.2f}秒")

            except Exception as e:
                # 增加错误计数
                self.error_counts[source_name] = self.error_counts.get(source_name, 0) + 1

                # 计算退避时间
                backoff_time = self.backoff_factor ** self.error_counts[source_name]

                logger.error(f"从{source_name}获取新闻失败: {str(e)}")
                logger.info(f"将在{backoff_time:.2f}秒后重试{source_name}")

        logger.info(f"从所有来源获取了{len(all_news)}条新闻")
        return all_news

    def analyze_hot_topics(self, **kwargs):
        """分析热点话题"""
        try:
            logger.info("开始分析热点话题...")

            # 加载处理后的新闻
            processed_news = self.data_storage.load('unified_news_monitor', 'processed_news', [])

            if not processed_news:
                logger.warning("没有处理后的新闻可供分析")
                return {
                    'status': 'warning',
                    'message': '没有处理后的新闻可供分析',
                    'hot_sectors': {},
                    'hot_stocks': {}
                }

            # 重置热点统计
            self.hot_sectors = defaultdict(float)
            self.hot_stocks = defaultdict(float)

            # 提取重要新闻（分数高的前20条）
            self.important_news = sorted(processed_news, key=lambda x: x.get('score', 0), reverse=True)[:20]

            # 分析新闻，统计热点板块和个股
            for news in processed_news:
                title = news.get('title', '')
                content = news.get('content', '')
                score = news.get('score', 1.0)

                if not title:
                    continue

                # 分析板块
                for sector, keywords in self.keyword_map.items():
                    for keyword in keywords:
                        if isinstance(title, str) and keyword in title or (isinstance(content, str) and keyword in content):
                            self.hot_sectors[sector] += score
                            break

                # 分析个股
                for stock_name in self.stock_code_map.keys():
                    if isinstance(title, str) and stock_name in title or (isinstance(content, str) and stock_name in content):
                        self.hot_stocks[stock_name] += score

                # 分析板块中的个股
                for sector, stocks in self.sector_map.items():
                    for stock in stocks:
                        if isinstance(title, str) and stock in title or (isinstance(content, str) and stock in content):
                            self.hot_stocks[stock] += score
                            self.hot_sectors[sector] += score * 0.5  # 增加板块热度（权重较低）

            # 保存热点分析结果
            self.data_storage.save('unified_news_monitor', 'hot_sectors', dict(self.hot_sectors), StorageLevel.HOT)
            self.data_storage.save('unified_news_monitor', 'hot_stocks', dict(self.hot_stocks), StorageLevel.HOT)
            self.data_storage.save('unified_news_monitor', 'important_news', self.important_news, StorageLevel.HOT)

            logger.info(f"热点话题分析成功，热点板块: {len(self.hot_sectors)}个，热点个股: {len(self.hot_stocks)}个")

            return {
                'status': 'success',
                'message': f'热点话题分析成功，热点板块: {len(self.hot_sectors)}个，热点个股: {len(self.hot_stocks)}个',
                'hot_sectors': dict(self.hot_sectors),
                'hot_stocks': dict(self.hot_stocks)
            }

        except Exception as e:
            logger.error(f"分析热点话题失败: {str(e)}")
            return {
                'status': 'error',
                'message': f'分析热点话题失败: {str(e)}',
                'error': str(e),
                'hot_sectors': {},
                'hot_stocks': {}
            }

    def generate_daily_report(self, **kwargs):
        """生成每日报告"""
        try:
            logger.info("开始生成每日报告...")

            # 当前日期
            today = datetime.now().strftime('%Y-%m-%d')

            # 报告文件名
            report_file = f'daily_report_{today}.md'

            # 加载处理后的新闻
            processed_news = self.data_storage.load('unified_news_monitor', 'processed_news', [])

            if not processed_news:
                logger.warning("没有处理后的新闻可供生成报告")
                return {
                    'status': 'warning',
                    'message': '没有处理后的新闻可供生成报告',
                    'report_file': None
                }

            # 分析热点
            self.analyze_hot_topics()

            # 生成报告内容
            report_content = f"# 每日新闻热点分析报告 ({today})\n\n"

            # 添加热点板块
            report_content += "## 热点板块\n\n"
            hot_sectors = sorted(self.hot_sectors.items(), key=lambda x: x[1], reverse=True)
            for sector, score in hot_sectors[:10]:  # 取前10个热点板块
                report_content += f"- **{sector}**: 热度 {score:.2f}\n"

            # 添加热点个股
            report_content += "\n## 热点个股\n\n"
            hot_stocks = sorted(self.hot_stocks.items(), key=lambda x: x[1], reverse=True)
            for stock, score in hot_stocks[:10]:  # 取前10个热点个股
                stock_code = self.stock_code_map.get(stock, '')
                if stock_code:
                    report_content += f"- **{stock} ({stock_code})**: 热度 {score:.2f}\n"
                else:
                    report_content += f"- **{stock}**: 热度 {score:.2f}\n"

            # 添加重要新闻
            report_content += "\n## 今日重要新闻\n\n"
            for i, news in enumerate(self.important_news[:15]):  # 取前15条重要新闻
                title = news.get('title', '')
                source = news.get('source', '')
                url = news.get('url', '')

                if url:
                    report_content += f"{i+1}. [{title}]({url}) (来源: {source})\n"
                else:
                    report_content += f"{i+1}. {title} (来源: {source})\n"

            # 添加政策动态
            report_content += "\n## 政策动态\n\n"
            policy_news = [news for news in processed_news if news.get('source') in ['国务院', '发改委', '证监会']]
            policy_news = sorted(policy_news, key=lambda x: x.get('score', 0), reverse=True)

            for i, news in enumerate(policy_news[:10]):  # 取前10条政策新闻
                title = news.get('title', '')
                source = news.get('source', '')
                url = news.get('url', '')

                if url:
                    report_content += f"{i+1}. [{title}]({url}) (来源: {source})\n"
                else:
                    report_content += f"{i+1}. {title} (来源: {source})\n"

            # 添加板块关注建议
            report_content += "\n## 板块关注建议\n\n"
            # 确保hot_sectors不为空
            if hot_sectors:
                for sector, score in hot_sectors[:5]:  # 取前5个热点板块
                    report_content += f"### {sector} (热度: {score:.2f})\n\n"
                    report_content += "相关个股:\n"

                    # 查找板块相关个股
                    sector_stocks = self.sector_map.get(sector, [])
                    for stock in sector_stocks:
                        stock_code = self.stock_code_map.get(stock, '')
                        stock_score = self.hot_stocks.get(stock, 0)

                        if stock_code:
                            report_content += f"- **{stock} ({stock_code})**: 热度 {stock_score:.2f}\n"
                        else:
                            report_content += f"- **{stock}**: 热度 {stock_score:.2f}\n"

                    # 查找板块相关新闻
                    report_content += "\n相关新闻:\n"
                    sector_news = []

                    for news in processed_news:
                        title = news.get('title', '')
                        content = news.get('content', '')

                        # 检查板块关键词
                        keywords = self.keyword_map.get(sector, [])
                        for keyword in keywords:
                            if isinstance(title, str) and keyword in title or (isinstance(content, str) and keyword in content):
                                sector_news.append(news)
                                break

                    # 按热度排序，取前5条
                    sector_news = sorted(sector_news, key=lambda x: x.get('score', 0), reverse=True)[:5]

                    for news in sector_news:
                        title = news.get('title', '')
                        source = news.get('source', '')
                        url = news.get('url', '')

                        if url:
                            report_content += f"- [{title}]({url}) (来源: {source})\n"
                        else:
                            report_content += f"- {title} (来源: {source})\n"

                    report_content += "\n"

            # 保存报告
            self.data_storage.save('unified_news_monitor', f'reports/{report_file}', report_content, StorageLevel.WARM)

            # 保存到文件
            reports_dir = os.path.join('data', 'unified_news_monitor', 'reports')
            os.makedirs(reports_dir, exist_ok=True)
            with open(os.path.join(reports_dir, report_file), 'w', encoding='utf-8') as f:
                f.write(report_content)

            logger.info(f"每日报告生成成功: {report_file}")

            return {
                'status': 'success',
                'message': f'每日报告生成成功: {report_file}',
                'report_file': report_file,
                'report_content': report_content
            }

        except Exception as e:
            logger.error(f"生成每日报告失败: {str(e)}")
            return {
                'status': 'error',
                'message': f'生成每日报告失败: {str(e)}',
                'error': str(e),
                'report_file': None
            }

    def system_maintenance(self, **kwargs):
        """系统维护"""
        try:
            logger.info("开始系统维护...")

            # 清理错误计数
            self.error_counts.clear()

            # 备份数据
            self.data_storage.backup('unified_news_monitor')

            # 清理过期数据
            self._clean_expired_data()

            logger.info("系统维护完成")

            return {
                'status': 'success',
                'message': '系统维护完成'
            }

        except Exception as e:
            logger.error(f"系统维护失败: {str(e)}")
            return {
                'status': 'error',
                'message': f'系统维护失败: {str(e)}',
                'error': str(e)
            }

    def _clean_expired_data(self):
        """清理过期数据"""
        try:
            # 清理过期新闻
            if self.processed_news:
                # 只保留最近30天的新闻
                cutoff_date = datetime.now() - timedelta(days=30)
                self.processed_news = [
                    news for news in self.processed_news
                    if news.get('processed_at') and
                    datetime.fromisoformat(news['processed_at']) > cutoff_date
                ]

                # 保存清理后的新闻
                self.data_storage.save('unified_news_monitor', 'processed_news', self.processed_news, StorageLevel.HOT)

                logger.info(f"清理过期数据完成，保留{len(self.processed_news)}条新闻")

        except Exception as e:
            logger.error(f"清理过期数据失败: {str(e)}")

    def _remove_duplicates(self, news_list: list) -> list:
        """
        去除重复新闻

        Args:
            news_list: 新闻列表

        Returns:
            unique_news: 去重后的新闻列表
        """
        # 按ID去重
        id_set = set()
        unique_news = []

        for news in news_list:
            if 'id' in news and news['id'] not in id_set:
                id_set.add(news['id'])
                unique_news.append(news)

        # 按内容相似度去重
        if len(unique_news) > 1:
            unique_news = self._remove_similar_news(unique_news)

        return unique_news

    def _remove_similar_news(self, news_list: list, threshold: float = 0.8) -> list:
        """
        去除相似新闻

        Args:
            news_list: 新闻列表
            threshold: 相似度阈值

        Returns:
            unique_news: 去重后的新闻列表
        """
        # 提取标题和内容
        texts = []
        for news in news_list:
            title = news.get('title', '')
            content = news.get('content', '')
            texts.append(f"{title} {content}")

        # 计算TF-IDF向量
        try:
            tfidf_matrix = self.vectorizer.fit_transform(texts)

            # 计算相似度矩阵
            similarity_matrix = cosine_similarity(tfidf_matrix)

            # 找出相似的新闻
            to_remove = set()
            for i in range(len(news_list)):
                if i in to_remove:
                    continue

                for j in range(i + 1, len(news_list)):
                    if j in to_remove:
                        continue

                    if similarity_matrix[i, j] > threshold:
                        # 保留分数高的或者更新的
                        score_i = self._calculate_news_score(news_list[i])
                        score_j = self._calculate_news_score(news_list[j])

                        if score_i >= score_j:
                            to_remove.add(j)
                        else:
                            to_remove.add(i)
                            break

            # 去除相似新闻
            return [news for i, news in enumerate(news_list) if i not in to_remove]

        except Exception as e:
            logger.error(f"计算相似度失败: {str(e)}")
            return news_list

    def _calculate_news_score(self, news: dict) -> float:
        """
        计算新闻热度分数

        Args:
            news: 新闻

        Returns:
            score: 热度分数
        """
        # 基础分数
        score = 1.0

        # 来源权重
        source = news.get('source', 'default')
        source_weight = self.source_weights.get(source, self.source_weights.get('default', 1.0))
        score *= source_weight

        # 时间衰减
        publish_date = news.get('publish_date')
        if publish_date:
            try:
                publish_time = datetime.fromisoformat(publish_date)
                time_diff = datetime.now() - publish_time

                # 24小时内的新闻，线性衰减
                if time_diff.total_seconds() < 86400:  # 24小时
                    decay = 1.0 - (time_diff.total_seconds() / 86400)
                    score *= (0.5 + 0.5 * decay)  # 最低衰减到一半
                else:
                    # 24小时后，指数衰减
                    days = time_diff.total_seconds() / 86400
                    score *= 0.5 * (0.9 ** (days - 1))
            except:
                pass

        # 标题长度奖励
        title = news.get('title', '')
        if title and len(title) > 10:
            score *= 1.0 + min(0.2, (len(title) - 10) / 50)

        # 内容长度奖励
        content = news.get('content', '')
        if content and len(content) > 100:
            score *= 1.0 + min(0.3, (len(content) - 100) / 1000)

        return score

    def _extract_keywords(self, news: dict, top_k: int = 5) -> list:
        """
        提取关键词

        Args:
            news: 新闻
            top_k: 关键词数量

        Returns:
            keywords: 关键词列表
        """
        title = news.get('title', '')
        content = news.get('content', '')
        text = f"{title} {content}"

        try:
            # 使用jieba提取关键词
            keywords = jieba.analyse.extract_tags(text, topK=top_k)
            return keywords
        except Exception as e:
            logger.error(f"提取关键词失败: {str(e)}")
            return []

    def process_news(self, news_list: list) -> list:
        """
        处理新闻列表

        Args:
            news_list: 新闻列表

        Returns:
            processed_news: 处理后的新闻列表
        """
        if not news_list:
            logger.warning("新闻列表为空")
            return []

        logger.info(f"开始处理{len(news_list)}条新闻")

        # 去重
        if CLUSTERING_AVAILABLE and self.news_clustering:
            # 使用新闻去重与聚类模块去重
            unique_news = self.news_clustering.deduplicate_news(news_list)
            logger.info(f"使用新闻去重与聚类模块去重后剩余{len(unique_news)}条新闻")
        else:
            # 使用简化版去重
            unique_news = self._remove_duplicates(news_list)
            logger.info(f"使用简化版去重后剩余{len(unique_news)}条新闻")

        # 计算热度分数
        for news in unique_news:
            news['score'] = self._calculate_news_score(news)

            # 如果没有关键词，提取关键词
            if 'keywords' not in news or not news['keywords']:
                news['keywords'] = self._extract_keywords(news)

            news['processed_at'] = datetime.now().isoformat()

        # 聚类
        if CLUSTERING_AVAILABLE and self.news_clustering:
            # 使用新闻去重与聚类模块聚类
            clustered_news = self.news_clustering.cluster_news(unique_news)
            logger.info(f"新闻聚类完成，共{len(self.news_clustering.clusters)}个聚类")
            unique_news = clustered_news

        # 更新处理后的新闻
        self.processed_news.extend(unique_news)

        # 只保留最近的10000条新闻
        if len(self.processed_news) > 10000:
            self.processed_news = sorted(
                self.processed_news,
                key=lambda x: datetime.fromisoformat(x['processed_at']),
                reverse=True
            )[:10000]

        # 保存处理后的新闻
        self.data_storage.save('unified_news_monitor', 'processed_news', self.processed_news, StorageLevel.HOT)

        logger.info(f"新闻处理完成，共{len(unique_news)}条")

        return unique_news
