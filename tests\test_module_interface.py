"""
模块接口测试

测试模块接口的基本功能
"""

import os
import sys
import unittest
import json
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('test_module_interface')

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入被测试的模块
from core.module_interface import ModuleInterface
from core.data_storage import DataStorage

# 创建测试用的模块接口实现类
class TestModule(ModuleInterface):
    """测试用的模块接口实现类"""
    
    def __init__(self, module_name="test_module", config_path=None):
        """初始化测试模块"""
        super().__init__(module_name, config_path)
        self.initialized = False
    
    def initialize(self):
        """初始化模块"""
        self.initialized = True
        return True
    
    def get_status(self):
        """获取模块状态"""
        return {
            'module_name': self.module_name,
            'initialized': self.initialized,
            'status': 'running',
            'last_update': datetime.now().isoformat()
        }

class TestModuleInterface(unittest.TestCase):
    """模块接口测试类"""
    
    @classmethod
    def setUpClass(cls):
        """测试前的准备工作"""
        logger.info("初始化模块接口测试...")
        
        # 创建测试目录
        cls.test_dir = os.path.join('data', 'test_module_interface')
        os.makedirs(cls.test_dir, exist_ok=True)
        os.makedirs(os.path.join('config'), exist_ok=True)
        
        # 创建测试配置文件
        cls.config_path = os.path.join('config', 'test_module_config.json')
        with open(cls.config_path, 'w', encoding='utf-8') as f:
            json.dump({
                'module_name': 'test_module',
                'enabled': True,
                'log_level': 'INFO',
                'task_priority': 'medium',
                'test_param': 'test_value'
            }, f, indent=4)
        
        logger.info("模块接口测试初始化完成")
    
    @classmethod
    def tearDownClass(cls):
        """测试后的清理工作"""
        logger.info("清理模块接口测试...")
        
        # 删除测试配置文件
        if os.path.exists(cls.config_path):
            os.remove(cls.config_path)
        
        # 删除测试目录
        import shutil
        if os.path.exists(cls.test_dir):
            shutil.rmtree(cls.test_dir)
        
        logger.info("模块接口测试清理完成")
    
    def test_init(self):
        """测试初始化"""
        logger.info("测试模块接口初始化...")
        
        # 创建测试模块
        module = TestModule()
        
        # 验证模块名称
        self.assertEqual(module.module_name, "test_module")
        
        # 验证配置路径
        self.assertEqual(module.config_path, "config/test_module_config.json")
        
        logger.info("模块接口初始化测试通过")
    
    def test_load_config(self):
        """测试加载配置"""
        logger.info("测试加载配置...")
        
        # 创建测试模块
        module = TestModule(config_path=self.config_path)
        
        # 验证配置
        self.assertIsNotNone(module.config)
        self.assertEqual(module.config.get('module_name'), 'test_module')
        self.assertEqual(module.config.get('enabled'), True)
        self.assertEqual(module.config.get('test_param'), 'test_value')
        
        logger.info("加载配置测试通过")
    
    def test_save_and_load_data(self):
        """测试保存和加载数据"""
        logger.info("测试保存和加载数据...")
        
        # 创建测试模块
        module = TestModule()
        
        # 测试数据
        test_data = {
            'test_key': 'test_value',
            'timestamp': datetime.now().isoformat()
        }
        
        # 保存数据
        result = module.save_data('test_data.json', test_data)
        self.assertTrue(result)
        
        # 加载数据
        loaded_data = module.load_data('test_data.json')
        self.assertIsNotNone(loaded_data)
        self.assertEqual(loaded_data.get('test_key'), 'test_value')
        
        logger.info("保存和加载数据测试通过")
    
    def test_log_methods(self):
        """测试日志方法"""
        logger.info("测试日志方法...")
        
        # 创建测试模块
        module = TestModule()
        
        # 测试日志方法
        module.log_info("测试信息日志")
        module.log_warning("测试警告日志")
        module.log_error("测试错误日志")
        module.log_debug("测试调试日志")
        
        # 无法直接验证日志输出，但至少确保方法不会抛出异常
        self.assertTrue(True)
        
        logger.info("日志方法测试通过")
    
    def test_initialize(self):
        """测试初始化方法"""
        logger.info("测试初始化方法...")
        
        # 创建测试模块
        module = TestModule()
        
        # 验证初始状态
        self.assertFalse(module.initialized)
        
        # 调用初始化方法
        result = module.initialize()
        
        # 验证初始化结果
        self.assertTrue(result)
        self.assertTrue(module.initialized)
        
        logger.info("初始化方法测试通过")
    
    def test_get_status(self):
        """测试获取状态方法"""
        logger.info("测试获取状态方法...")
        
        # 创建测试模块
        module = TestModule()
        
        # 调用初始化方法
        module.initialize()
        
        # 获取状态
        status = module.get_status()
        
        # 验证状态
        self.assertIsNotNone(status)
        self.assertEqual(status.get('module_name'), 'test_module')
        self.assertTrue(status.get('initialized'))
        self.assertEqual(status.get('status'), 'running')
        
        logger.info("获取状态方法测试通过")

if __name__ == '__main__':
    unittest.main()
