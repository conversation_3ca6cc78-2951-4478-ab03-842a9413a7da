"""
实时提示系统模块
负责在发现异动后针对行业板块或具体股票的机遇或风险给出实时提示
"""

import os
import json
import smtplib
from datetime import datetime
from typing import Dict, List, Any, Optional
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import logging

from utils.logger import logger
from utils.config_loader import config_loader

class RealTimeAlertSystem:
    """实时提示系统类"""
    
    def __init__(self, config=None):
        """
        初始化实时提示系统
        
        Args:
            config: 配置对象
        """
        self.config = config if config else config_loader
        
        # 提示配置
        self.alert_config = {
            'enable_email': self.config.get('alert.email.enabled', False),
            'enable_file': self.config.get('alert.file.enabled', True),
            'enable_console': self.config.get('alert.console.enabled', True),
            'email_recipients': self.config.get('alert.email.recipients', []),
            'alert_levels': ['LOW', 'MEDIUM', 'HIGH', 'CRITICAL']
        }
        
        # 行业股票映射
        self.industry_stocks = {
            '银行': ['600000', '600036', '000001', '601318', '601166'],
            '保险': ['601318', '601601', '601628', '000858', '601336'],
            '证券': ['600030', '000166', '002736', '600837', '000776'],
            '房地产': ['000002', '001979', '600048', '000069', '600340'],
            '医药': ['000858', '600276', '002007', '000963', '600867'],
            '科技': ['000063', '002415', '300059', '002230', '000725'],
            '新能源': ['300750', '002594', '300014', '002129', '300274'],
            '消费': ['600519', '000858', '002304', '600887', '000596']
        }
        
        # 创建提示目录
        os.makedirs('alerts', exist_ok=True)
        
        logger.info("实时提示系统初始化完成")
    
    def send_alert(self, alert_data: Dict[str, Any]) -> bool:
        """
        发送提示
        
        Args:
            alert_data: 提示数据
            
        Returns:
            bool: 发送是否成功
        """
        try:
            # 生成提示内容
            alert_content = self._generate_alert_content(alert_data)
            
            # 确定提示级别
            alert_level = self._determine_alert_level(alert_data)
            
            # 发送到不同渠道
            success = True
            
            if self.alert_config['enable_console']:
                success &= self._send_console_alert(alert_content, alert_level)
            
            if self.alert_config['enable_file']:
                success &= self._send_file_alert(alert_content, alert_level)
            
            if self.alert_config['enable_email']:
                success &= self._send_email_alert(alert_content, alert_level)
            
            return success
            
        except Exception as e:
            logger.error(f"发送提示失败: {str(e)}")
            return False
    
    def send_policy_alert(self, policy_anomaly: Dict[str, Any]) -> bool:
        """
        发送政策异动提示
        
        Args:
            policy_anomaly: 政策异动数据
            
        Returns:
            bool: 发送是否成功
        """
        try:
            for anomaly in policy_anomaly.get('anomalies', []):
                # 分析受影响的行业和股票
                affected_analysis = self._analyze_policy_impact(anomaly)
                
                alert_data = {
                    'type': 'policy_alert',
                    'title': f"政策异动提示: {anomaly.get('title', '未知政策')}",
                    'description': anomaly.get('description', ''),
                    'importance_score': anomaly.get('importance_score', 0),
                    'affected_industries': anomaly.get('affected_industries', []),
                    'affected_stocks': affected_analysis.get('affected_stocks', []),
                    'investment_suggestions': affected_analysis.get('suggestions', []),
                    'risk_level': affected_analysis.get('risk_level', 'MEDIUM'),
                    'timestamp': anomaly.get('timestamp', datetime.now().isoformat())
                }
                
                self.send_alert(alert_data)
            
            return True
            
        except Exception as e:
            logger.error(f"发送政策异动提示失败: {str(e)}")
            return False
    
    def send_market_alert(self, market_anomaly: Dict[str, Any]) -> bool:
        """
        发送市场异动提示
        
        Args:
            market_anomaly: 市场异动数据
            
        Returns:
            bool: 发送是否成功
        """
        try:
            for anomaly in market_anomaly.get('anomalies', []):
                # 分析市场影响
                market_analysis = self._analyze_market_impact(anomaly)
                
                alert_data = {
                    'type': 'market_alert',
                    'title': f"市场异动提示: {anomaly.get('type', '未知异动')}",
                    'description': anomaly.get('description', ''),
                    'anomaly_type': anomaly.get('type', ''),
                    'affected_sectors': market_analysis.get('affected_sectors', []),
                    'affected_stocks': market_analysis.get('affected_stocks', []),
                    'trading_suggestions': market_analysis.get('suggestions', []),
                    'risk_level': market_analysis.get('risk_level', 'MEDIUM'),
                    'timestamp': anomaly.get('timestamp', datetime.now().isoformat())
                }
                
                self.send_alert(alert_data)
            
            return True
            
        except Exception as e:
            logger.error(f"发送市场异动提示失败: {str(e)}")
            return False
    
    def _generate_alert_content(self, alert_data: Dict[str, Any]) -> str:
        """
        生成提示内容
        
        Args:
            alert_data: 提示数据
            
        Returns:
            str: 格式化的提示内容
        """
        content = f"""
=== {alert_data.get('title', '系统提示')} ===
时间: {alert_data.get('timestamp', datetime.now().isoformat())}
类型: {alert_data.get('type', '未知')}
风险级别: {alert_data.get('risk_level', 'MEDIUM')}

描述: {alert_data.get('description', '无描述')}

"""
        
        # 添加受影响的行业
        if alert_data.get('affected_industries'):
            content += f"受影响行业: {', '.join(alert_data['affected_industries'])}\n"
        
        if alert_data.get('affected_sectors'):
            content += f"受影响板块: {', '.join(alert_data['affected_sectors'])}\n"
        
        # 添加受影响的股票
        if alert_data.get('affected_stocks'):
            content += f"受影响股票: {', '.join(alert_data['affected_stocks'])}\n"
        
        # 添加投资建议
        if alert_data.get('investment_suggestions'):
            content += "\n投资建议:\n"
            for suggestion in alert_data['investment_suggestions']:
                content += f"- {suggestion}\n"
        
        if alert_data.get('trading_suggestions'):
            content += "\n交易建议:\n"
            for suggestion in alert_data['trading_suggestions']:
                content += f"- {suggestion}\n"
        
        content += "\n" + "="*50 + "\n"
        
        return content
    
    def _determine_alert_level(self, alert_data: Dict[str, Any]) -> str:
        """
        确定提示级别
        
        Args:
            alert_data: 提示数据
            
        Returns:
            str: 提示级别
        """
        risk_level = alert_data.get('risk_level', 'MEDIUM')
        importance_score = alert_data.get('importance_score', 0.5)
        
        if risk_level == 'CRITICAL' or importance_score > 0.9:
            return 'CRITICAL'
        elif risk_level == 'HIGH' or importance_score > 0.7:
            return 'HIGH'
        elif risk_level == 'MEDIUM' or importance_score > 0.5:
            return 'MEDIUM'
        else:
            return 'LOW'
    
    def _send_console_alert(self, content: str, level: str) -> bool:
        """发送控制台提示"""
        try:
            print(f"\n[{level}] {content}")
            return True
        except Exception as e:
            logger.error(f"控制台提示发送失败: {str(e)}")
            return False
    
    def _send_file_alert(self, content: str, level: str) -> bool:
        """发送文件提示"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d")
            filename = f"alerts/alerts_{timestamp}.log"
            
            with open(filename, 'a', encoding='utf-8') as f:
                f.write(f"[{level}] {content}\n")
            
            return True
        except Exception as e:
            logger.error(f"文件提示发送失败: {str(e)}")
            return False
    
    def _send_email_alert(self, content: str, level: str) -> bool:
        """发送邮件提示"""
        try:
            # 这里应该实现邮件发送逻辑
            # 暂时只记录日志
            logger.info(f"邮件提示 [{level}]: {content[:100]}...")
            return True
        except Exception as e:
            logger.error(f"邮件提示发送失败: {str(e)}")
            return False
    
    def _analyze_policy_impact(self, anomaly: Dict[str, Any]) -> Dict[str, Any]:
        """分析政策影响"""
        affected_industries = anomaly.get('affected_industries', [])
        affected_stocks = []
        suggestions = []
        
        # 根据受影响行业找到相关股票
        for industry in affected_industries:
            if industry in self.industry_stocks:
                affected_stocks.extend(self.industry_stocks[industry])
        
        # 生成投资建议
        importance_score = anomaly.get('importance_score', 0.5)
        if importance_score > 0.8:
            suggestions.append("重大政策发布，建议密切关注相关行业动向")
            suggestions.append("考虑调整相关行业配置比例")
            risk_level = 'HIGH'
        elif importance_score > 0.6:
            suggestions.append("重要政策发布，建议关注后续实施细则")
            risk_level = 'MEDIUM'
        else:
            suggestions.append("政策发布，建议持续跟踪")
            risk_level = 'LOW'
        
        return {
            'affected_stocks': list(set(affected_stocks)),
            'suggestions': suggestions,
            'risk_level': risk_level
        }
    
    def _analyze_market_impact(self, anomaly: Dict[str, Any]) -> Dict[str, Any]:
        """分析市场影响"""
        anomaly_type = anomaly.get('type', '')
        affected_sectors = []
        affected_stocks = []
        suggestions = []
        
        # 根据异动类型分析影响
        if 'volatility' in anomaly_type:
            affected_sectors = ['全市场']
            suggestions.append("市场波动率异常，建议控制仓位")
            suggestions.append("关注避险资产配置")
            risk_level = 'HIGH'
        elif 'fund_flow' in anomaly_type:
            affected_sectors = ['资金流入行业']
            suggestions.append("资金流异动，建议关注资金流向")
            risk_level = 'MEDIUM'
        elif 'sentiment' in anomaly_type:
            affected_sectors = ['情绪敏感行业']
            suggestions.append("市场情绪变化，建议谨慎操作")
            risk_level = 'MEDIUM'
        else:
            risk_level = 'LOW'
        
        return {
            'affected_sectors': affected_sectors,
            'affected_stocks': affected_stocks,
            'suggestions': suggestions,
            'risk_level': risk_level
        }
