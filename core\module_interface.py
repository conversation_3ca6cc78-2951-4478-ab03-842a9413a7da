"""
模块接口

定义系统各模块与中央调度器交互的标准接口
"""

import os
import logging
import json
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/module_interface.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('module_interface')

class ModuleInterface(ABC):
    """模块接口基类"""
    
    def __init__(self, module_name: str, config_path: Optional[str] = None):
        """
        初始化模块接口
        
        Args:
            module_name: 模块名称
            config_path: 配置文件路径
        """
        self.module_name = module_name
        self.config_path = config_path or f'config/{module_name}_config.json'
        self.config = self._load_config()
        self.scheduler = None  # 由调度器注入
        
        # 创建必要的目录
        os.makedirs('logs', exist_ok=True)
        os.makedirs(f'data/{module_name}', exist_ok=True)
        
        logger.info(f"模块接口初始化: {module_name}")
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置"""
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                # 默认配置
                default_config = {
                    'enabled': True,
                    'max_concurrent_tasks': 2,
                    'task_priority': 'medium',
                    'cache_enabled': True,
                    'cache_ttl': 3600,
                    'retry_count': 3,
                    'retry_delay': 5
                }
                
                # 保存默认配置
                os.makedirs(os.path.dirname(self.config_path), exist_ok=True)
                with open(self.config_path, 'w', encoding='utf-8') as f:
                    json.dump(default_config, f, indent=4)
                
                return default_config
        except Exception as e:
            logger.error(f"加载配置失败: {str(e)}")
            return {
                'enabled': True,
                'max_concurrent_tasks': 2,
                'task_priority': 'medium'
            }
    
    def set_scheduler(self, scheduler):
        """
        设置调度器
        
        Args:
            scheduler: 调度器实例
        """
        self.scheduler = scheduler
        logger.info(f"模块 {self.module_name} 设置调度器")
    
    def schedule_task(self, function: str, params: Dict[str, Any] = None, 
                     priority: str = None, schedule_time: Optional[datetime] = None) -> str:
        """
        调度任务
        
        Args:
            function: 函数名称
            params: 函数参数
            priority: 任务优先级
            schedule_time: 计划执行时间
            
        Returns:
            task_id: 任务ID
        """
        if not self.scheduler:
            raise ValueError("调度器未设置")
        
        # 获取优先级
        priority = priority or self.config.get('task_priority', 'medium')
        
        # 调度任务
        task_id = self.scheduler.schedule_task(
            task_type=self.module_name,
            module=self.module_name,
            function=function,
            params=params,
            priority=priority,
            schedule_time=schedule_time
        )
        
        logger.info(f"模块 {self.module_name} 调度任务: {function}, ID: {task_id}")
        
        return task_id
    
    def get_task_status(self, task_id: str):
        """
        获取任务状态
        
        Args:
            task_id: 任务ID
            
        Returns:
            status: 任务状态
        """
        if not self.scheduler:
            raise ValueError("调度器未设置")
        
        return self.scheduler.get_task_status(task_id)
    
    def get_task_result(self, task_id: str):
        """
        获取任务结果
        
        Args:
            task_id: 任务ID
            
        Returns:
            result: 任务结果
        """
        if not self.scheduler:
            raise ValueError("调度器未设置")
        
        return self.scheduler.get_task_result(task_id)
    
    def save_data(self, data: Any, filename: str):
        """
        保存数据
        
        Args:
            data: 数据
            filename: 文件名
        """
        try:
            file_path = f'data/{self.module_name}/{filename}'
            
            # 确保目录存在
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            
            # 根据文件扩展名选择保存方式
            if filename.endswith('.json'):
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(data, f, indent=4)
            elif filename.endswith('.csv'):
                if isinstance(data, list):
                    import pandas as pd
                    pd.DataFrame(data).to_csv(file_path, index=False)
                else:
                    data.to_csv(file_path, index=False)
            else:
                with open(file_path, 'wb') as f:
                    import pickle
                    pickle.dump(data, f)
            
            logger.info(f"模块 {self.module_name} 保存数据: {filename}")
        
        except Exception as e:
            logger.error(f"保存数据失败: {str(e)}")
    
    def load_data(self, filename: str) -> Any:
        """
        加载数据
        
        Args:
            filename: 文件名
            
        Returns:
            data: 数据
        """
        try:
            file_path = f'data/{self.module_name}/{filename}'
            
            if not os.path.exists(file_path):
                logger.warning(f"文件不存在: {file_path}")
                return None
            
            # 根据文件扩展名选择加载方式
            if filename.endswith('.json'):
                with open(file_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            elif filename.endswith('.csv'):
                import pandas as pd
                return pd.read_csv(file_path)
            else:
                with open(file_path, 'rb') as f:
                    import pickle
                    return pickle.load(f)
        
        except Exception as e:
            logger.error(f"加载数据失败: {str(e)}")
            return None
    
    @abstractmethod
    def initialize(self):
        """初始化模块"""
        pass
    
    @abstractmethod
    def get_status(self) -> Dict[str, Any]:
        """
        获取模块状态
        
        Returns:
            status: 模块状态
        """
        pass
    
    @abstractmethod
    def health_check(self) -> Dict[str, Any]:
        """
        健康检查
        
        Returns:
            health: 健康状态
        """
        pass
