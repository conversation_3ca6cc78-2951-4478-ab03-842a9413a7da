@echo off
echo ========================================
echo    智能金融分析驾驶舱启动
echo    AI驱动的政策-流动性-波动率系统
echo ========================================
echo.

cd /d "%~dp0"

echo 正在启动智能分析驾驶舱...
echo 系统将启动以下组件:
echo - FinBERT情感分析引擎
echo - 多因子耦合分析引擎  
echo - 知识图谱可视化
echo - 实时数据监控
echo.
echo 启动后请访问: http://127.0.0.1:5001
echo 按 Ctrl+C 可以停止服务
echo.

python web_ui/intelligent_app.py

echo.
echo 智能分析驾驶舱已停止运行
echo.
pause
