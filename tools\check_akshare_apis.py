"""
检查AKShare中可用的API函数
"""

import akshare as ak
import inspect

def check_fund_flow_apis():
    """检查资金流相关的API"""
    print("=== 检查AKShare中的资金流相关API ===")
    
    # 获取所有akshare的函数
    all_functions = [name for name, obj in inspect.getmembers(ak) if inspect.isfunction(obj)]
    
    # 筛选资金流相关的函数
    fund_flow_functions = [func for func in all_functions if 'fund' in func.lower() or 'flow' in func.lower()]
    
    print(f"找到 {len(fund_flow_functions)} 个资金流相关函数：")
    for func in sorted(fund_flow_functions):
        print(f"  - {func}")
    
    return fund_flow_functions

def check_industry_apis():
    """检查行业相关的API"""
    print("\n=== 检查AKShare中的行业相关API ===")
    
    # 获取所有akshare的函数
    all_functions = [name for name, obj in inspect.getmembers(ak) if inspect.isfunction(obj)]
    
    # 筛选行业相关的函数
    industry_functions = [func for func in all_functions if 'industry' in func.lower() or 'board' in func.lower()]
    
    print(f"找到 {len(industry_functions)} 个行业相关函数：")
    for func in sorted(industry_functions):
        print(f"  - {func}")
    
    return industry_functions

def check_stock_apis():
    """检查股票相关的API"""
    print("\n=== 检查AKShare中的股票相关API ===")
    
    # 获取所有akshare的函数
    all_functions = [name for name, obj in inspect.getmembers(ak) if inspect.isfunction(obj)]
    
    # 筛选股票相关的函数
    stock_functions = [func for func in all_functions if func.startswith('stock_')]
    
    print(f"找到 {len(stock_functions)} 个股票相关函数：")
    
    # 按类别分组
    categories = {}
    for func in stock_functions:
        if 'fund' in func or 'flow' in func:
            category = 'fund_flow'
        elif 'news' in func:
            category = 'news'
        elif 'board' in func or 'industry' in func:
            category = 'industry'
        elif 'index' in func:
            category = 'index'
        elif 'spot' in func or 'daily' in func or 'hist' in func:
            category = 'market_data'
        else:
            category = 'other'
        
        if category not in categories:
            categories[category] = []
        categories[category].append(func)
    
    for category, functions in categories.items():
        print(f"\n  {category.upper()}:")
        for func in sorted(functions):
            print(f"    - {func}")
    
    return stock_functions

def test_specific_apis():
    """测试特定的API"""
    print("\n=== 测试特定API ===")
    
    # 测试可能的资金流API
    test_apis = [
        'stock_fund_flow_individual',
        'stock_fund_flow_concept',
        'stock_fund_flow_industry',
        'stock_board_concept_fund_flow_em',
        'stock_board_industry_fund_flow_em',
        'stock_individual_fund_flow_rank_em'
    ]
    
    for api_name in test_apis:
        if hasattr(ak, api_name):
            print(f"✅ {api_name} - 存在")
            try:
                func = getattr(ak, api_name)
                sig = inspect.signature(func)
                print(f"   参数: {sig}")
            except Exception as e:
                print(f"   获取签名失败: {e}")
        else:
            print(f"❌ {api_name} - 不存在")

if __name__ == "__main__":
    print(f"AKShare版本: {ak.__version__}")
    
    fund_flow_apis = check_fund_flow_apis()
    industry_apis = check_industry_apis()
    stock_apis = check_stock_apis()
    test_specific_apis()
