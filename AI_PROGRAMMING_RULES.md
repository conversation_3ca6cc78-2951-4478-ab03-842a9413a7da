# AI协助编程规范文档 (AI Programming Rules)

## 📋 文档概述

本文档规范人工智能在政策-流动性-波动率套利系统开发中的协助行为，确保开发过程的一致性、质量和效率。

## 🎯 核心原则

### 1. 深度理解优先
- **系统架构理解**：AI必须深入理解双系统架构（主分析系统+24小时监控系统+Web界面）
- **业务逻辑理解**：理解政策、新闻、资金流、波动率四大因子的耦合关系
- **技术栈理解**：掌握Python、Flask、FinBERT、AKShare、机器学习等技术栈

### 2. 渐进式开发
- **分块处理**：将大型任务分解为小块，避免"too large input"错误
- **模块化设计**：每个功能模块独立开发，便于测试和维护
- **迭代优化**：先实现基础功能，再逐步增强

### 3. 质量保证
- **代码规范**：遵循PEP8规范，添加详细注释和文档字符串
- **错误处理**：完善的异常处理和日志记录
- **测试驱动**：为每个模块编写测试用例

## 🏗️ 系统架构规范

### 双系统架构设计
```
┌─────────────────────────────────────────────────────────────┐
│                智能金融分析生态系统                            │
├─────────────────────────────────────────────────────────────┤
│  数据感知层 → AI智能分析层 → 决策执行层 → 可视化展示层        │
│     ↓            ↓            ↓            ↓               │
│  监控系统    →  AI引擎    →  主系统    →  Web界面           │
└─────────────────────────────────────────────────────────────┘
```

### 核心组件规范
1. **数据感知层**：24小时监控系统，实时数据收集和预处理
2. **AI智能分析层**：FinBERT引擎、多因子耦合分析、知识图谱
3. **决策执行层**：主分析系统，股票筛选和推荐生成
4. **可视化展示层**：Web界面，实时监控和交互操作

## 💻 编程规范

### 1. 文件组织规范
```
project_root/
├── core/                    # 核心引擎
│   ├── intelligent_finbert_engine.py
│   ├── multi_factor_coupling_engine.py
│   ├── data_storage.py
│   └── scheduler.py
├── engines/                 # 功能引擎
│   ├── news_policy/
│   ├── sentiment/
│   ├── tiered_fund_flow/
│   └── volatility/
├── tools/                   # 开发工具
├── web_ui/                  # Web界面
├── config/                  # 配置文件
├── docs/                    # 文档
└── tests/                   # 测试文件
```

### 2. 代码风格规范
```python
"""
模块文档字符串
描述模块功能、主要类和函数
"""

import os
import sys
from typing import Dict, List, Any, Optional
from datetime import datetime

class ExampleClass:
    """
    类文档字符串
    
    Args:
        param1: 参数1描述
        param2: 参数2描述
    """
    
    def __init__(self, param1: str, param2: int = 0):
        self.param1 = param1
        self.param2 = param2
        
    def example_method(self, data: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """
        方法文档字符串
        
        Args:
            data: 输入数据列表
            
        Returns:
            处理结果字典，失败时返回None
            
        Raises:
            ValueError: 当输入数据格式错误时
        """
        try:
            # 实现逻辑
            result = self.process_data(data)
            return result
        except Exception as e:
            logger.error(f"处理失败: {str(e)}")
            return None
```

### 3. 错误处理规范
```python
# 标准错误处理模式
try:
    result = risky_operation()
    logger.info("操作成功完成")
    return result
except SpecificException as e:
    logger.error(f"特定错误: {str(e)}")
    return default_value
except Exception as e:
    logger.error(f"未知错误: {str(e)}")
    raise
```

## 🤖 AI开发行为规范

### 1. 任务分解规范
- **大任务分解**：将复杂任务分解为多个小任务
- **依赖关系明确**：明确任务间的依赖关系
- **优先级排序**：按重要性和紧急性排序

### 2. 代码生成规范
- **单文件限制**：每次生成的代码文件不超过300行
- **功能完整性**：确保生成的代码功能完整可运行
- **测试覆盖**：为关键功能提供测试代码

### 3. 问题解决规范
- **问题诊断**：先诊断问题根因，再提供解决方案
- **多方案对比**：提供多个解决方案并说明优缺点
- **风险评估**：评估解决方案的潜在风险

## 📊 数据处理规范

### 1. API数据获取
```python
def fetch_api_data(api_name: str, params: Dict[str, Any]) -> Optional[pd.DataFrame]:
    """
    标准API数据获取模式
    """
    try:
        data = api_function(**params)
        if data is not None and not data.empty:
            logger.info(f"API数据获取成功: {api_name}, 数据量: {len(data)}")
            return data
        else:
            logger.warning(f"API返回空数据: {api_name}")
            return None
    except Exception as e:
        logger.error(f"API数据获取失败: {api_name}, 错误: {str(e)}")
        return None
```

### 2. 数据存储规范
```python
def store_data_safely(data: Any, data_type: str, storage_level: str = "HOT") -> bool:
    """
    安全数据存储模式
    """
    try:
        success = data_storage.store_data(data_type, data, storage_level)
        if success:
            logger.info(f"数据存储成功: {data_type}")
        else:
            logger.error(f"数据存储失败: {data_type}")
        return success
    except Exception as e:
        logger.error(f"数据存储异常: {data_type}, 错误: {str(e)}")
        return False
```

### 3. 数据验证规范
- **数据完整性检查**：检查必要字段是否存在
- **数据类型验证**：验证数据类型是否正确
- **数据范围检查**：检查数值是否在合理范围内

## 🧠 AI模型集成规范

### 1. FinBERT模型使用
```python
def analyze_text_sentiment(texts: List[str]) -> List[Dict[str, Any]]:
    """
    标准FinBERT分析模式
    """
    try:
        finbert_engine = get_finbert_engine()
        results = finbert_engine.analyze_sentiment(texts)
        logger.info(f"情感分析完成: {len(texts)}条文本")
        return results
    except Exception as e:
        logger.error(f"情感分析失败: {str(e)}")
        return []
```

### 2. 多因子耦合分析
```python
def analyze_factor_coupling(data_streams: Dict[str, pd.DataFrame]) -> Dict[str, Any]:
    """
    标准多因子耦合分析模式
    """
    try:
        coupling_engine = MultiFactorCouplingEngine()
        results = coupling_engine.analyze_coupling(data_streams)
        logger.info("多因子耦合分析完成")
        return results
    except Exception as e:
        logger.error(f"耦合分析失败: {str(e)}")
        return {}
```

## 🔄 开发流程规范

### 1. 功能开发流程
1. **需求分析**：明确功能需求和技术要求
2. **设计方案**：设计技术方案和接口
3. **编码实现**：按规范编写代码
4. **单元测试**：编写和执行测试用例
5. **集成测试**：与其他模块集成测试
6. **文档更新**：更新相关文档

### 2. 问题修复流程
1. **问题重现**：重现和确认问题
2. **根因分析**：分析问题根本原因
3. **解决方案**：设计和实施解决方案
4. **回归测试**：确保修复不引入新问题
5. **文档记录**：记录问题和解决方案

### 3. 性能优化流程
1. **性能测试**：测量当前性能指标
2. **瓶颈识别**：识别性能瓶颈
3. **优化实施**：实施优化措施
4. **效果验证**：验证优化效果
5. **监控部署**：部署性能监控

## 🚀 部署和维护规范

### 1. 系统部署
- **环境准备**：确保运行环境满足要求
- **依赖安装**：安装所有必要依赖
- **配置检查**：验证配置文件正确性
- **功能测试**：执行完整功能测试

### 2. 系统监控
- **性能监控**：监控系统性能指标
- **错误监控**：监控和报告系统错误
- **数据质量监控**：监控数据获取和处理质量
- **用户体验监控**：监控用户界面响应时间

### 3. 系统维护
- **定期备份**：定期备份重要数据和配置
- **日志清理**：定期清理过期日志文件
- **依赖更新**：定期更新系统依赖
- **安全检查**：定期进行安全检查和更新

## 📝 文档规范

### 1. 代码文档
- **模块文档**：每个模块都要有详细说明
- **API文档**：所有公开接口都要有文档
- **配置文档**：配置参数的详细说明

### 2. 用户文档
- **安装指南**：详细的安装和配置步骤
- **使用手册**：功能使用说明和示例
- **故障排除**：常见问题和解决方案

### 3. 开发文档
- **架构设计**：系统架构和设计理念
- **开发指南**：开发环境搭建和开发流程
- **测试指南**：测试策略和测试用例

## ⚠️ 注意事项

### 1. 安全考虑
- **数据安全**：保护敏感数据，避免泄露
- **访问控制**：实施适当的访问控制
- **输入验证**：验证所有外部输入

### 2. 性能考虑
- **资源使用**：合理使用CPU、内存和存储资源
- **并发处理**：正确处理并发访问
- **缓存策略**：合理使用缓存提高性能

### 3. 可维护性
- **代码可读性**：编写清晰易读的代码
- **模块化设计**：保持模块间的低耦合
- **版本控制**：使用版本控制管理代码变更

---

**版本**: 1.0  
**创建日期**: 2025-05-25  
**最后更新**: 2025-05-25  
**维护者**: AI开发团队
