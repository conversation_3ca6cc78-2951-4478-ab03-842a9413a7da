# 开发指南

## 1. 开发环境设置

### 1.1 环境要求

- Python 3.8+
- Git
- 代码编辑器（推荐 VS Code 或 PyCharm）

### 1.2 环境设置

1. 克隆仓库：
   ```bash
   git clone <repository_url>
   cd policy_liquidity_volatility_arbitrage
   ```

2. 创建虚拟环境：
   ```bash
   python -m venv venv
   
   # Windows
   venv\Scripts\activate
   
   # Linux/macOS
   source venv/bin/activate
   ```

3. 安装依赖：
   ```bash
   pip install -r requirements.txt
   ```

4. 设置开发环境变量：
   ```bash
   # Windows
   set PYTHONPATH=%PYTHONPATH%;%CD%
   
   # Linux/macOS
   export PYTHONPATH=$PYTHONPATH:$(pwd)
   ```

## 2. 代码规范

### 2.1 Python 代码规范

- 遵循 PEP 8 编码规范
- 使用 4 个空格缩进
- 行长度不超过 100 个字符
- 使用有意义的变量名和函数名
- 使用类型注解
- 编写文档字符串

### 2.2 文档规范

- 所有模块、类和函数都应有文档字符串
- 文档字符串应包含参数、返回值和异常说明
- 复杂逻辑应有注释说明

### 2.3 测试规范

- 所有功能模块都应有单元测试
- 测试文件命名为 `test_<module_name>.py`
- 测试类命名为 `Test<ClassName>`
- 测试方法命名为 `test_<method_name>`

## 3. 项目结构

### 3.1 目录结构

```
policy_liquidity_volatility_arbitrage/
├── config/                 # 配置文件
├── core/                   # 核心模块
│   ├── module_interface.py # 模块接口
│   ├── data_storage.py     # 数据存储
│   └── scheduler.py        # 任务调度器
├── data/                   # 数据存储
│   ├── hot/                # 热存储
│   ├── warm/               # 温存储
│   └── cold/               # 冷存储
├── data_sources/           # 数据源模块
│   ├── policy_analyzer.py  # 政策分析模块
│   ├── unified_news_monitor.py # 新闻监控模块
│   ├── fund_flow_analyzer.py # 资金流分析模块
│   ├── volatility_analyzer.py # 波动率分析模块
│   ├── sentiment_resonance.py # 情绪共振模块
│   └── arbitrage_detector.py # 套利检测模块
├── database/               # 数据库模块
│   ├── unified_data_access.py # 统一数据访问接口
│   ├── data_sync_service.py # 数据同步服务
│   └── file_storage.py     # 文件存储管理器
├── docs/                   # 文档
│   ├── system_architecture.md # 系统架构文档
│   ├── system_dashboard_design.md # 系统操作面板设计
│   ├── test_progress_report.md # 测试进度报告
│   └── development_guide.md # 开发指南
├── gui/                    # 图形界面
│   ├── dashboard.py        # 操作面板
│   └── __init__.py         # 初始化文件
├── logs/                   # 日志
├── models/                 # 模型
│   └── finbert/            # FinBERT 模型
├── tests/                  # 测试
│   ├── test_module_interface.py # 模块接口测试
│   ├── test_data_storage.py # 数据存储测试
│   ├── test_scheduler.py   # 任务调度器测试
│   ├── test_policy_analyzer.py # 政策分析模块测试
│   ├── test_news_monitor.py # 新闻监控模块测试
│   ├── test_fund_flow_analyzer.py # 资金流分析模块测试
│   ├── test_volatility_analyzer.py # 波动率分析模块测试
│   └── integration/        # 集成测试
├── main.py                 # 主入口
├── requirements.txt        # 依赖列表
└── README.md               # 项目说明
```

### 3.2 模块依赖关系

```
+------------------+     +------------------+     +------------------+
|                  |     |                  |     |                  |
| 操作面板 (GUI)    |---->| 系统管理器        |---->| 中央调度器       |
|                  |     |                  |     |                  |
+------------------+     +------------------+     +------------------+
                                 |                        |
                                 v                        v
+------------------+     +------------------+     +------------------+
|                  |     |                  |     |                  |
| 数据存储          |<----| 功能模块          |---->| 统一数据访问接口  |
|                  |     |                  |     |                  |
+------------------+     +------------------+     +------------------+
                                                          |
                                                          v
                                               +------------------+
                                               |                  |
                                               | 数据库            |
                                               |                  |
                                               +------------------+
```

## 4. 开发流程

### 4.1 功能开发流程

1. 从 `develop` 分支创建功能分支：
   ```bash
   git checkout develop
   git pull
   git checkout -b feature/new-feature
   ```

2. 开发新功能，包括代码、测试和文档

3. 提交代码：
   ```bash
   git add .
   git commit -m "Add new feature"
   ```

4. 推送到远程仓库：
   ```bash
   git push origin feature/new-feature
   ```

5. 创建 Pull Request，等待代码审查

6. 合并到 `develop` 分支

### 4.2 Bug 修复流程

1. 从 `develop` 分支创建修复分支：
   ```bash
   git checkout develop
   git pull
   git checkout -b fix/bug-fix
   ```

2. 修复 Bug，包括代码、测试和文档

3. 提交代码：
   ```bash
   git add .
   git commit -m "Fix bug"
   ```

4. 推送到远程仓库：
   ```bash
   git push origin fix/bug-fix
   ```

5. 创建 Pull Request，等待代码审查

6. 合并到 `develop` 分支

### 4.3 发布流程

1. 从 `develop` 分支创建发布分支：
   ```bash
   git checkout develop
   git pull
   git checkout -b release/v1.0.0
   ```

2. 进行最终测试和修复

3. 更新版本号和文档

4. 提交代码：
   ```bash
   git add .
   git commit -m "Prepare for release v1.0.0"
   ```

5. 推送到远程仓库：
   ```bash
   git push origin release/v1.0.0
   ```

6. 创建 Pull Request，等待代码审查

7. 合并到 `main` 和 `develop` 分支

8. 创建标签：
   ```bash
   git tag -a v1.0.0 -m "Release v1.0.0"
   git push origin v1.0.0
   ```

## 5. 模块开发指南

### 5.1 创建新模块

1. 创建模块文件：
   ```python
   # data_sources/new_module.py
   
   from core.module_interface import ModuleInterface
   
   class NewModule(ModuleInterface):
       """
       新模块
       
       提供新功能
       """
       
       def __init__(self, config_path=None):
           """
           初始化新模块
           
           Args:
               config_path: 配置文件路径
           """
           super().__init__("new_module", config_path)
       
       def initialize(self):
           """
           初始化模块
           
           Returns:
               bool: 初始化结果
           """
           self.logger.info("初始化新模块")
           # 初始化代码
           return True
       
       def get_status(self):
           """
           获取模块状态
           
           Returns:
               dict: 模块状态
           """
           return {
               "module_name": self.module_name,
               "status": "running",
               "last_update": self.last_update.isoformat() if hasattr(self, "last_update") else None
           }
       
       def new_function(self, **kwargs):
           """
           新功能
           
           Args:
               **kwargs: 参数
           
           Returns:
               dict: 结果
           """
           self.logger.info("执行新功能")
           # 功能代码
           return {"status": "success"}
   ```

2. 创建模块配置文件：
   ```json
   // config/new_module_config.json
   {
       "module_name": "new_module",
       "enabled": true,
       "log_level": "INFO",
       "parameters": {
           "param1": "value1",
           "param2": "value2"
       }
   }
   ```

3. 创建模块测试文件：
   ```python
   # tests/test_new_module.py
   
   import unittest
   import os
   import sys
   import json
   import logging
   
   # 添加项目根目录到路径
   sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
   
   # 导入被测试的模块
   from data_sources.new_module import NewModule
   
   class TestNewModule(unittest.TestCase):
       """新模块测试类"""
       
       @classmethod
       def setUpClass(cls):
           """测试前的准备工作"""
           # 创建测试配置文件
           cls.config_path = os.path.join('config', 'test_new_module_config.json')
           with open(cls.config_path, 'w', encoding='utf-8') as f:
               json.dump({
                   "module_name": "new_module",
                   "enabled": True,
                   "log_level": "INFO",
                   "parameters": {
                       "param1": "test_value1",
                       "param2": "test_value2"
                   }
               }, f, indent=4)
       
       @classmethod
       def tearDownClass(cls):
           """测试后的清理工作"""
           # 删除测试配置文件
           if os.path.exists(cls.config_path):
               os.remove(cls.config_path)
       
       def test_init(self):
           """测试初始化"""
           module = NewModule(config_path=self.config_path)
           self.assertEqual(module.module_name, "new_module")
           self.assertEqual(module.config.get("parameters").get("param1"), "test_value1")
       
       def test_initialize(self):
           """测试初始化模块"""
           module = NewModule(config_path=self.config_path)
           result = module.initialize()
           self.assertTrue(result)
       
       def test_get_status(self):
           """测试获取模块状态"""
           module = NewModule(config_path=self.config_path)
           status = module.get_status()
           self.assertEqual(status.get("module_name"), "new_module")
           self.assertEqual(status.get("status"), "running")
       
       def test_new_function(self):
           """测试新功能"""
           module = NewModule(config_path=self.config_path)
           result = module.new_function(param="test")
           self.assertEqual(result.get("status"), "success")
   
   if __name__ == '__main__':
       unittest.main()
   ```

4. 在系统配置文件中添加模块配置：
   ```json
   // config/system_config.json
   {
       "modules": {
           "new_module": {
               "enabled": true,
               "config_path": "config/new_module_config.json"
           }
       }
   }
   ```

5. 在系统管理器中注册模块：
   ```python
   # main.py
   
   # 导入新模块
   from data_sources.new_module import NewModule
   
   # 在 _initialize_modules 方法中添加
   if self.config.get("modules", {}).get("new_module", {}).get("enabled", True):
       new_module = NewModule()
       new_module.data_storage = self.data_storage
       if self.data_access:
           new_module.data_access = self.data_access
       self.modules["new_module"] = new_module
       self.scheduler.register_module("new_module", new_module)
       logger.info("新模块初始化成功")
   ```

### 5.2 使用数据存储

```python
# 保存数据
self.data_storage.save(
    module_name=self.module_name,
    data_name="data_name",
    data=data,
    level=StorageLevel.HOT
)

# 加载数据
data = self.data_storage.load(
    module_name=self.module_name,
    data_name="data_name",
    default=None,
    level=StorageLevel.HOT
)

# 检查数据是否存在
exists = self.data_storage.exists(
    module_name=self.module_name,
    data_name="data_name"
)

# 删除数据
self.data_storage.delete(
    module_name=self.module_name,
    data_name="data_name"
)
```

### 5.3 使用统一数据访问接口

```python
# 获取数据
data = self.data_access.get_data(
    data_type="data_type",
    query_params={"param1": "value1"},
    options={"limit": 10}
)

# 保存数据
self.data_access.save_data(
    data_type="data_type",
    data=data,
    options={"upsert": True}
)

# 删除数据
self.data_access.delete_data(
    data_type="data_type",
    query_params={"param1": "value1"},
    options={}
)
```

### 5.4 使用任务调度器

```python
# 调度任务
task_id = self.scheduler.schedule_task(
    task_type="task_type",
    module=self.module_name,
    function="function_name",
    params={"param1": "value1"},
    priority=TaskPriority.HIGH
)

# 执行任务
result = self.scheduler.execute_task(task_id)
```

## 6. 测试指南

### 6.1 单元测试

```bash
# 运行所有测试
python -m unittest discover -s tests

# 运行特定测试
python -m unittest tests.test_module_name

# 运行特定测试方法
python -m unittest tests.test_module_name.TestClassName.test_method_name
```

### 6.2 集成测试

```bash
# 运行所有集成测试
python -m unittest discover -s tests.integration

# 运行特定集成测试
python -m unittest tests.integration.test_integration_name
```

### 6.3 系统测试

```bash
# 运行所有系统测试
python -m unittest discover -s tests.system

# 运行特定系统测试
python -m unittest tests.system.test_system_name
```

## 7. 文档指南

### 7.1 代码文档

- 使用文档字符串记录模块、类和函数的用途和用法
- 使用类型注解说明参数和返回值类型
- 使用注释说明复杂逻辑

### 7.2 系统文档

- 系统架构文档：描述系统的整体架构和模块关系
- 操作面板设计文档：描述系统操作面板的设计和使用方法
- 测试进度报告：记录系统测试的进度和结果
- 开发指南：指导开发人员如何开发和维护系统

## 8. 常见问题

### 8.1 导入错误

问题：`ModuleNotFoundError: No module named 'xxx'`

解决方案：
- 确保已安装所有依赖：`pip install -r requirements.txt`
- 确保项目根目录在 Python 路径中：`export PYTHONPATH=$PYTHONPATH:$(pwd)`
- 检查导入语句是否正确

### 8.2 配置错误

问题：`FileNotFoundError: [Errno 2] No such file or directory: 'config/xxx.json'`

解决方案：
- 确保配置文件存在
- 确保配置文件路径正确
- 确保配置文件格式正确

### 8.3 数据存储错误

问题：`PermissionError: [Errno 13] Permission denied: 'data/xxx'`

解决方案：
- 确保数据目录存在
- 确保有数据目录的读写权限
- 确保磁盘空间充足
