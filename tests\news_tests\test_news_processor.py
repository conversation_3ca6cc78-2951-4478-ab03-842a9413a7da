"""
测试新闻处理模块
"""

import os
import pandas as pd
from datetime import datetime, timedelta
import time

# 创建必要的目录
os.makedirs('logs', exist_ok=True)
os.makedirs('data/cache/policy', exist_ok=True)
os.makedirs('data/cache/news_processor', exist_ok=True)

# 导入政策数据源和新闻处理器
from data_sources.policy_data import PolicyDataSource
from data_sources.news_processor import NewsProcessor

def test_news_processor():
    """测试新闻处理器"""
    print("\n测试新闻处理器...")
    
    # 创建新闻处理器
    processor = NewsProcessor()
    
    # 创建政策数据源
    policy_data = PolicyDataSource()
    
    # 获取多个来源的新闻
    print("获取多个来源的新闻...")
    
    # 获取财经早餐
    financial_breakfast = policy_data.get_financial_breakfast()
    print(f"财经早餐: {len(financial_breakfast)} 条")
    
    # 获取全球财经快讯-东财财富
    global_news_em = policy_data.get_global_news_em()
    print(f"全球财经快讯-东财财富: {len(global_news_em)} 条")
    
    # 获取全球财经快讯-新浪财经
    global_news_sina = policy_data.get_global_news_sina()
    print(f"全球财经快讯-新浪财经: {len(global_news_sina)} 条")
    
    # 获取个股新闻-东方财富
    stock_news_em = policy_data.get_stock_news_em(symbol="300059")
    print(f"个股新闻-东方财富: {len(stock_news_em)} 条")
    
    # 合并新闻
    all_news = []
    
    # 添加财经早餐
    for _, row in financial_breakfast.iterrows():
        all_news.append(row.to_dict())
    
    # 添加全球财经快讯-东财财富
    for _, row in global_news_em.iterrows():
        all_news.append(row.to_dict())
    
    # 添加全球财经快讯-新浪财经
    for _, row in global_news_sina.iterrows():
        all_news.append(row.to_dict())
    
    # 添加个股新闻-东方财富
    for _, row in stock_news_em.iterrows():
        all_news.append(row.to_dict())
    
    print(f"合并后的新闻总数: {len(all_news)} 条")
    
    # 处理新闻
    print("\n处理新闻...")
    start_time = time.time()
    processed_news = processor.process_news(all_news)
    end_time = time.time()
    
    print(f"处理完成，耗时: {end_time - start_time:.2f}秒")
    print(f"处理后的新闻数量: {len(processed_news)} 条")
    
    # 打印前10条新闻
    print("\n处理后的前10条新闻:")
    for i, news in enumerate(processed_news[:10]):
        print(f"{i+1}. {news['title']} (来源: {news['source']}, 热度: {news['score']:.2f})")
    
    # 保存处理后的新闻
    processor.save_processed_news(processed_news)
    
    # 加载处理后的新闻
    loaded_news = processor.load_processed_news()
    print(f"\n加载的新闻数量: {len(loaded_news)} 条")
    
    # 模拟新增新闻
    print("\n模拟新增新闻...")
    
    # 创建一些重复的新闻和一些新的新闻
    new_news = []
    
    # 添加一些重复的新闻（修改标题）
    for i in range(5):
        if i < len(processed_news):
            news = processed_news[i].copy()
            news['title'] = news['title'] + " (更新)"
            new_news.append(news)
    
    # 添加一些全新的新闻
    for i in range(5):
        new_news.append({
            'title': f"测试新闻 {i+1}",
            'source': '测试来源',
            'publish_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'url': f"http://example.com/news/{i+1}"
        })
    
    print(f"新增新闻数量: {len(new_news)} 条")
    
    # 更新处理后的新闻
    updated_news = processor.update_processed_news(new_news)
    print(f"更新后的新闻数量: {len(updated_news)} 条")
    
    # 打印前10条更新后的新闻
    print("\n更新后的前10条新闻:")
    for i, news in enumerate(updated_news[:10]):
        print(f"{i+1}. {news['title']} (来源: {news['source']}, 热度: {news['score']:.2f})")
    
    return processed_news, updated_news

if __name__ == "__main__":
    print("开始测试新闻处理模块...")
    processed_news, updated_news = test_news_processor()
    print("\n测试完成!")
