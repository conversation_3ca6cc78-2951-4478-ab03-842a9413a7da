"""
政策三层解析框架示例

展示如何使用政策三层解析框架
"""

import os
import sys
import time
import json
from datetime import datetime, timedelta
import pandas as pd

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入政策解析器
from data_sources.policy_analyzer import PolicyAnalyzer

# 导入中央调度器
from core.scheduler import CentralScheduler, TaskPriority

def example_basic_usage():
    """基本使用示例"""
    print("\n=== 基本使用示例 ===")
    
    # 创建调度器
    scheduler = CentralScheduler()
    
    # 创建政策解析器
    analyzer = PolicyAnalyzer()
    
    # 设置调度器
    analyzer.set_scheduler(scheduler)
    
    # 启动调度器
    scheduler.start()
    
    # 初始化模块
    analyzer.initialize()
    
    # 等待初始化完成
    print("等待初始化完成...")
    time.sleep(3)
    
    # 获取模块状态
    status = analyzer.get_status()
    print(f"模块状态: {json.dumps(status, indent=2, ensure_ascii=False)}")
    
    # 停止调度器
    scheduler.stop()
    
    print("基本使用示例完成")

def example_fetch_and_parse_policies():
    """获取并解析政策示例"""
    print("\n=== 获取并解析政策示例 ===")
    
    # 创建调度器
    scheduler = CentralScheduler()
    
    # 创建政策解析器
    analyzer = PolicyAnalyzer()
    
    # 设置调度器
    analyzer.set_scheduler(scheduler)
    
    # 启动调度器
    scheduler.start()
    
    # 手动获取并解析政策
    print("开始获取并解析政策...")
    result = analyzer.fetch_and_parse_policies()
    
    print(f"获取结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
    
    # 如果获取成功，显示部分解析后的政策
    if result['status'] == 'success' and result['count'] > 0:
        print("\n解析后的政策示例:")
        for i, policy in enumerate(analyzer.parsed_policies[:3]):  # 只显示前3条
            print(f"\n政策 {i+1}:")
            print(f"  标题: {policy.get('title', '')}")
            print(f"  来源: {policy.get('source', '')}")
            print(f"  政策类型: {policy.get('policy_type', '')}")
            print(f"  政策主体: {policy.get('subject', '')}")
            print(f"  政策动作: {policy.get('action', '')}")
            print(f"  影响对象: {policy.get('object', '')}")
            
            # 显示行业影响
            industry_impacts = policy.get('industry_impacts', {})
            if industry_impacts:
                print("\n  行业影响:")
                # 按影响程度排序
                sorted_impacts = sorted(industry_impacts.items(), key=lambda x: x[1], reverse=True)
                for industry, impact in sorted_impacts[:5]:  # 只显示前5个行业
                    print(f"    {industry}: {impact:.2f}")
    
    # 停止调度器
    scheduler.stop()
    
    print("获取并解析政策示例完成")

def example_calculate_policy_heat():
    """计算政策热度示例"""
    print("\n=== 计算政策热度示例 ===")
    
    # 创建调度器
    scheduler = CentralScheduler()
    
    # 创建政策解析器
    analyzer = PolicyAnalyzer()
    
    # 设置调度器
    analyzer.set_scheduler(scheduler)
    
    # 启动调度器
    scheduler.start()
    
    # 先获取并解析政策
    print("先获取并解析政策...")
    analyzer.fetch_and_parse_policies()
    
    # 计算政策热度
    print("\n开始计算政策热度...")
    result = analyzer.calculate_policy_heat(days=30)
    
    print(f"计算结果状态: {result['status']}")
    print(f"计算结果消息: {result['message']}")
    
    # 显示政策热度
    if result['status'] == 'success' and 'policy_heats' in result:
        print("\n政策热度排名:")
        for i, policy in enumerate(result['policy_heats'][:5]):  # 只显示前5条
            print(f"{i+1}. {policy['title']} (来源: {policy['source']}, 热度: {policy['heat']:.2f})")
    
    # 显示行业热度
    if result['status'] == 'success' and 'industry_heats' in result:
        print("\n行业热度排名:")
        # 按热度排序
        sorted_heats = sorted(result['industry_heats'].items(), key=lambda x: x[1], reverse=True)
        for i, (industry, heat) in enumerate(sorted_heats[:5]):  # 只显示前5个行业
            print(f"{i+1}. {industry}: {heat:.2f}")
    
    # 停止调度器
    scheduler.stop()
    
    print("计算政策热度示例完成")

def example_industry_impact_matrix():
    """行业影响矩阵示例"""
    print("\n=== 行业影响矩阵示例 ===")
    
    # 创建政策解析器
    analyzer = PolicyAnalyzer()
    
    # 获取行业影响矩阵
    matrix = analyzer.industry_impact_matrix
    
    print("行业影响矩阵示例:")
    
    # 选择几个政策类型和行业进行展示
    policy_types = ['货币政策', '财政政策', '产业政策', '环保政策', '医疗政策']
    industries = ['银行', '房地产', '电子', '医药生物', '钢铁']
    
    # 创建DataFrame展示矩阵
    df = pd.DataFrame(index=industries, columns=policy_types)
    
    for policy_type in policy_types:
        for industry in industries:
            df.loc[industry, policy_type] = matrix.get(policy_type, {}).get(industry, 0)
    
    print(df)
    
    print("行业影响矩阵示例完成")

def example_policy_decay_factors():
    """政策衰减因子示例"""
    print("\n=== 政策衰减因子示例 ===")
    
    # 创建政策解析器
    analyzer = PolicyAnalyzer()
    
    # 获取政策衰减因子
    factors = analyzer.policy_decay_factors
    
    print("政策衰减因子:")
    
    # 按衰减因子排序
    sorted_factors = sorted(factors.items(), key=lambda x: x[1], reverse=True)
    
    for policy_type, factor in sorted_factors:
        print(f"{policy_type}: {factor:.3f}")
    
    # 展示衰减效果
    print("\n衰减效果示例:")
    print("假设政策发布时热度为1.0")
    
    days = [0, 7, 30, 90, 180, 365]
    
    # 选择几个政策类型
    selected_types = ['货币政策', '财政政策', '产业政策', '科技政策']
    
    # 创建DataFrame展示衰减效果
    df = pd.DataFrame(index=selected_types, columns=[f"{d}天" for d in days])
    
    for policy_type in selected_types:
        decay_factor = factors.get(policy_type, 0.05)
        for day in days:
            df.loc[policy_type, f"{day}天"] = round(1.0 * (2.71828 ** (-decay_factor * day)), 3)
    
    print(df)
    
    print("政策衰减因子示例完成")

def main():
    """主函数"""
    print("=== 政策三层解析框架示例 ===")
    
    # 创建必要的目录
    os.makedirs('logs', exist_ok=True)
    os.makedirs('data', exist_ok=True)
    os.makedirs('config', exist_ok=True)
    
    # 询问用户选择示例
    print("\n请选择要运行的示例:")
    print("1. 基本使用示例")
    print("2. 获取并解析政策示例")
    print("3. 计算政策热度示例")
    print("4. 行业影响矩阵示例")
    print("5. 政策衰减因子示例")
    print("6. 运行所有示例")
    print("0. 退出")
    
    choice = input("\n请输入选项（0-6）: ")
    
    if choice == '1':
        example_basic_usage()
    elif choice == '2':
        example_fetch_and_parse_policies()
    elif choice == '3':
        example_calculate_policy_heat()
    elif choice == '4':
        example_industry_impact_matrix()
    elif choice == '5':
        example_policy_decay_factors()
    elif choice == '6':
        example_basic_usage()
        example_fetch_and_parse_policies()
        example_calculate_policy_heat()
        example_industry_impact_matrix()
        example_policy_decay_factors()
    elif choice == '0':
        print("退出")
    else:
        print("无效选项")

if __name__ == '__main__':
    main()
