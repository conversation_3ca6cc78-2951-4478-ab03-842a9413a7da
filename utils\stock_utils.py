"""
Stock utilities for policy_liquidity_volatility_arbitrage.
"""

import pandas as pd
import numpy as np
import akshare as ak
from datetime import datetime, timedelta
from utils.logger import logger
from utils.error_utils import retry, handle_api_error
from utils.cache_utils import cached

@cached(expiry=86400)  # Cache for 1 day
@retry(max_retries=3, delay=2)
def get_stock_list():
    """
    Get a list of all A-share stocks.

    Returns:
        pandas.DataFrame: DataFrame containing stock information.
    """
    try:
        # Get A-share stock list from API
        stock_list_df = ak.stock_info_a_code_name()

        # Rename columns for consistency
        stock_list_df.columns = ['stock_code', 'stock_name']

        return stock_list_df
    except Exception as e:
        logger.error(f"Error getting stock list: {str(e)}")
        return pd.DataFrame(columns=['stock_code', 'stock_name'])

@cached(expiry=86400)  # Cache for 1 day
@retry(max_retries=3, delay=2)
def get_industry_classification(source='sw'):
    """
    Get industry classification for A-share stocks.

    Args:
        source (str): Industry classification source. Options: 'sw' (申万), 'zjw' (证监会).

    Returns:
        pandas.DataFrame: DataFrame containing stock industry classification.
    """
    try:
        if source == 'sw':
            # Get Shenwan industry classification
            industry_df = ak.stock_sector_spot()

            # Extract stock code and industry
            result_df = pd.DataFrame({
                'stock_code': industry_df['代码'].str.replace('sh', '').str.replace('sz', ''),
                'industry_name': industry_df['板块名称'],
                'industry_code': industry_df['板块代码']
            })

            return result_df
        elif source == 'zjw':
            # Get CSRC industry classification
            industry_df = ak.stock_sector_detail()

            # Extract stock code and industry
            result_df = pd.DataFrame({
                'stock_code': industry_df['代码'].str.replace('sh', '').str.replace('sz', ''),
                'industry_name': industry_df['所属行业'],
                'industry_code': ''  # CSRC doesn't provide industry codes
            })

            return result_df
        else:
            logger.error(f"Invalid industry classification source: {source}")
            return pd.DataFrame(columns=['stock_code', 'industry_name', 'industry_code'])
    except Exception as e:
        logger.error(f"Error getting industry classification: {str(e)}")
        return pd.DataFrame(columns=['stock_code', 'industry_name', 'industry_code'])

@handle_api_error
def get_stock_price(stock_code, start_date, end_date=None, adjust='qfq'):
    """
    Get historical stock prices.

    Args:
        stock_code (str): Stock code, e.g., '600000'.
        start_date (str): Start date in format 'YYYY-MM-DD'.
        end_date (str, optional): End date in format 'YYYY-MM-DD'. Defaults to today.
        adjust (str): Price adjustment method. Options: 'qfq' (前复权), 'hfq' (后复权), '' (不复权).

    Returns:
        pandas.DataFrame: DataFrame containing historical stock prices.
    """
    try:
        # Format stock code
        if stock_code.startswith('6'):
            symbol = f"sh{stock_code}"
        else:
            symbol = f"sz{stock_code}"

        # Get historical prices
        price_df = ak.stock_zh_a_hist(symbol=symbol, start_date=start_date, end_date=end_date, adjust=adjust)

        # Rename columns for consistency
        price_df.columns = ['date', 'open', 'close', 'high', 'low', 'volume', 'amount', 'amplitude', 'change_pct', 'change', 'turnover']

        # Convert date to datetime
        price_df['date'] = pd.to_datetime(price_df['date'])

        # Add stock code column
        price_df['stock_code'] = stock_code

        return price_df
    except Exception as e:
        logger.error(f"Error getting stock price for {stock_code}: {str(e)}")
        return pd.DataFrame()

@handle_api_error
def get_stock_financial_indicator(stock_code):
    """
    Get financial indicators for a stock.

    Args:
        stock_code (str): Stock code, e.g., '600000'.

    Returns:
        pandas.DataFrame: DataFrame containing financial indicators.
    """
    try:
        # Get financial indicators
        indicator_df = ak.stock_financial_analysis_indicator(symbol=stock_code)

        # Add stock code column
        indicator_df['stock_code'] = stock_code

        return indicator_df
    except Exception as e:
        logger.error(f"Error getting financial indicators for {stock_code}: {str(e)}")
        return pd.DataFrame()

@handle_api_error
def get_stock_valuation(stock_code):
    """
    Get valuation metrics for a stock.

    Args:
        stock_code (str): Stock code, e.g., '600000'.

    Returns:
        dict: Dictionary containing valuation metrics.
    """
    try:
        # Get stock information
        stock_info_df = ak.stock_individual_info_em(symbol=f"sh{stock_code}" if stock_code.startswith('6') else f"sz{stock_code}")

        # Extract valuation metrics
        valuation = {}
        for _, row in stock_info_df.iterrows():
            if row['item'] in ['市盈率(动态)', '市净率', '市销率', '市现率']:
                valuation[row['item']] = row['value']

        return valuation
    except Exception as e:
        logger.error(f"Error getting valuation for {stock_code}: {str(e)}")
        return {}



@cached(expiry=86400)  # Cache for 1 day
@retry(max_retries=3, delay=2)
def get_stock_info(stock_code):
    """
    Get comprehensive information for a stock.

    Args:
        stock_code (str): Stock code, e.g., '600000'.

    Returns:
        dict: Dictionary containing stock information.
    """
    try:
        # Initialize result
        stock_info = {'stock_code': stock_code}

        # Get stock name
        stock_list_df = get_stock_list()
        if not stock_list_df.empty:
            stock_row = stock_list_df[stock_list_df['stock_code'] == stock_code]
            if not stock_row.empty:
                stock_info['stock_name'] = stock_row.iloc[0]['stock_name']

        # Get industry classification
        industry_df = get_industry_classification()
        if not industry_df.empty:
            industry_row = industry_df[industry_df['stock_code'] == stock_code]
            if not industry_row.empty:
                stock_info['industry_name'] = industry_row.iloc[0]['industry_name']
                stock_info['industry_code'] = industry_row.iloc[0]['industry_code']

        # Get market cap and other basic info
        try:
            # Format stock code
            symbol = f"sh{stock_code}" if stock_code.startswith('6') else f"sz{stock_code}"

            # Get stock quote
            quote_df = ak.stock_zh_a_spot_em()
            quote_row = quote_df[quote_df['代码'] == symbol]

            if not quote_row.empty:
                stock_info['current_price'] = quote_row.iloc[0]['最新价']
                stock_info['change_pct'] = quote_row.iloc[0]['涨跌幅']
                stock_info['turnover_rate'] = quote_row.iloc[0]['换手率']

                # Calculate market cap
                if 'total_share' in stock_info and 'current_price' in stock_info:
                    stock_info['market_cap'] = stock_info['total_share'] * stock_info['current_price']
        except Exception as e:
            logger.warning(f"Error getting quote for {stock_code}: {str(e)}")

        # Get valuation metrics
        valuation = get_stock_valuation(stock_code)
        stock_info.update(valuation)

        # Get financial indicators
        try:
            indicator_df = get_stock_financial_indicator(stock_code)
            if not indicator_df.empty:
                latest_indicator = indicator_df.iloc[0]
                stock_info['roe'] = latest_indicator.get('净资产收益率(%)', None)
                stock_info['roa'] = latest_indicator.get('总资产报酬率(%)', None)
                stock_info['gross_margin'] = latest_indicator.get('销售毛利率(%)', None)
                stock_info['net_margin'] = latest_indicator.get('销售净利率(%)', None)
        except Exception as e:
            logger.warning(f"Error getting financial indicators for {stock_code}: {str(e)}")

        return stock_info
    except Exception as e:
        logger.error(f"Error getting stock info for {stock_code}: {str(e)}")
        return {'stock_code': stock_code}
