# Configuration for policy_liquidity_volatility_arbitrage

# Paths
paths:
  data_dir: "data"
  logs_dir: "logs"
  models_dir: "models"
  cache_dir: "data/cache"

# API settings
api:
  akshare:
    timeout: 30

# News and Policy Engine settings
engines:
  news_policy:
    sources:
      policy:
        - "国务院"
        - "央行"
        - "财政部"
        - "发改委"
        - "证监会"
        - "交易所"
        - "银保监会"
      news:
        - "新浪财经"
        - "东方财富网"
        - "中国证券报"
        - "上海证券报"
        - "证券时报"
    rss_feeds:
      - "http://www.gov.cn/zhengce/zuixin.xml"  # 国务院最新政策
      - "http://www.pbc.gov.cn/goutongjiaoliu/113456/113469/rss.xml"  # 央行
    lookback_days: 7
    bert_model_path: "models/finbert"
    industry_mapping_path: "data/policy_industry_mapping.json"

    # Policy weights
    policy_source_weights:
      "国务院": 1.0
      "央行": 0.9
      "财政部": 0.8
      "发改委": 0.8
      "证监会": 0.7
      "交易所": 0.6
      "银保监会": 0.7

    policy_type_weights:
      "规划": 1.0
      "条例": 0.9
      "意见": 0.8
      "通知": 0.7
      "公告": 0.6

# Tiered Fund Flow Engine settings
engines:
  tiered_fund_flow:
    lookback_days: 30

    # Northbound flow settings
    northbound:
      qfii_weight: 0.3
      etf_premium_weight: 0.2
      connect_arbitrage_weight: 0.2
      custody_weight: 0.2
      swap_arbitrage_weight: 0.1

    # Fund flow weights by market cap
    flow_weights:
      large_cap:  # > 1000亿
        northbound: 0.3
        institutional: 0.3
        margin: 0.2
        hot_money: 0.1
        retail: 0.1
      mid_cap:  # 100亿 - 1000亿
        northbound: 0.2
        institutional: 0.2
        margin: 0.2
        hot_money: 0.2
        retail: 0.2
      small_cap:  # < 100亿
        northbound: 0.1
        institutional: 0.1
        margin: 0.2
        hot_money: 0.3
        retail: 0.3

# Volatility Engine settings
engines:
  volatility:
    historical_window: 20
    garch_params:
      p: 1
      q: 1

    # Volatility regime thresholds
    regime_thresholds:
      low_volatility: 0.15
      high_volatility: 0.3

    # Volatility adjustment factors
    adjustment_factors:
      high_volatility_market:
        high_stock_vol: -0.2  # Penalize high volatility stocks in high volatility market
        low_stock_vol: 0.2   # Reward low volatility stocks in high volatility market
      low_volatility_market:
        high_stock_vol: 0.1   # Slightly reward high volatility stocks in low volatility market
        low_stock_vol: 0.0   # Neutral for low volatility stocks in low volatility market

# Decision Engine settings
decision_engine:
  # Factor weights
  weights:
    default:  # Default weights
      news_policy_score: 0.3
      tiered_flow_score: 0.4
      volatility_factor: 0.3

    high_volatility_market:  # Weights during high volatility
      news_policy_score: 0.4
      tiered_flow_score: 0.4
      volatility_factor: 0.2

    low_volatility_market:  # Weights during low volatility
      news_policy_score: 0.2
      tiered_flow_score: 0.5
      volatility_factor: 0.3

  # Number of top stocks to recommend
  top_n: 30

# Sentiment Engine settings
engines:
  sentiment:
    model_path: "models/sentiment"

# Alert system settings
alert:
  email:
    enabled: false
    recipients: []
  file:
    enabled: true
  console:
    enabled: true

# Execution settings
execution:
  qmt:
    account: "DEMO"
    initial_capital: 1000000
    max_position_pct: 0.1  # Maximum position size as percentage of portfolio
    stop_loss_pct: 0.05    # Stop loss percentage
    take_profit_pct: 0.1   # Take profit percentage
