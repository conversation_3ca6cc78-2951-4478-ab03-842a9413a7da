"""
统一数据访问接口

提供统一的数据访问接口，屏蔽底层存储差异
"""

import logging
import json
import os
from typing import Dict, Any, List, Optional, Union

# 导入数据库客户端
from .relational_db import PostgreSQLClient
from .time_series_db import InfluxDBClient
from .document_db import MongoDBClient
from .memory_db import RedisClient

# 导入数据同步服务
from .data_sync_service import DataSyncService

# 导入数据类型常量
from .data_types import (
    RELATIONAL_DATA_TYPES,
    TIME_SERIES_DATA_TYPES,
    DOCUMENT_DATA_TYPES,
    MEMORY_DATA_TYPES
)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('unified_data_access')

class UnifiedDataAccess:
    """统一数据访问接口"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化统一数据访问接口
        
        Args:
            config: 配置信息，包含各数据库的连接参数
        """
        # 加载配置
        self.config = config or self._load_default_config()
        
        # 初始化数据库客户端
        self._init_db_clients()
        
        # 初始化数据同步服务
        self.sync_service = DataSyncService(self)
        
        logger.info("统一数据访问接口初始化完成")
    
    def _load_default_config(self) -> Dict[str, Any]:
        """
        加载默认配置
        
        Returns:
            配置信息
        """
        config_path = os.path.join(os.path.dirname(__file__), 'config', 'database_config.json')
        try:
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                # 返回默认配置
                return {
                    'postgresql': {
                        'enabled': False,
                        'host': 'localhost',
                        'port': 5432,
                        'database': 'financial_system',
                        'user': 'postgres',
                        'password': 'postgres'
                    },
                    'influxdb': {
                        'enabled': False,
                        'host': 'localhost',
                        'port': 8086,
                        'database': 'financial_system',
                        'user': 'admin',
                        'password': 'admin'
                    },
                    'mongodb': {
                        'enabled': False,
                        'host': 'localhost',
                        'port': 27017,
                        'database': 'financial_system',
                        'user': 'admin',
                        'password': 'admin'
                    },
                    'redis': {
                        'enabled': False,
                        'host': 'localhost',
                        'port': 6379,
                        'db': 0,
                        'password': None
                    },
                    'fallback': {
                        'enabled': True,
                        'data_dir': 'data'
                    }
                }
        except Exception as e:
            logger.error(f"加载配置文件失败: {str(e)}")
            # 返回最小配置
            return {
                'fallback': {
                    'enabled': True,
                    'data_dir': 'data'
                }
            }
    
    def _init_db_clients(self):
        """初始化数据库客户端"""
        # 初始化关系型数据库客户端
        if self.config.get('postgresql', {}).get('enabled', False):
            try:
                self.relational_db = PostgreSQLClient(self.config.get('postgresql', {}))
                logger.info("关系型数据库客户端初始化成功")
            except Exception as e:
                logger.error(f"关系型数据库客户端初始化失败: {str(e)}")
                self.relational_db = None
        else:
            self.relational_db = None
            logger.info("关系型数据库未启用")
        
        # 初始化时序数据库客户端
        if self.config.get('influxdb', {}).get('enabled', False):
            try:
                self.time_series_db = InfluxDBClient(self.config.get('influxdb', {}))
                logger.info("时序数据库客户端初始化成功")
            except Exception as e:
                logger.error(f"时序数据库客户端初始化失败: {str(e)}")
                self.time_series_db = None
        else:
            self.time_series_db = None
            logger.info("时序数据库未启用")
        
        # 初始化文档数据库客户端
        if self.config.get('mongodb', {}).get('enabled', False):
            try:
                self.document_db = MongoDBClient(self.config.get('mongodb', {}))
                logger.info("文档数据库客户端初始化成功")
            except Exception as e:
                logger.error(f"文档数据库客户端初始化失败: {str(e)}")
                self.document_db = None
        else:
            self.document_db = None
            logger.info("文档数据库未启用")
        
        # 初始化内存数据库客户端
        if self.config.get('redis', {}).get('enabled', False):
            try:
                self.memory_db = RedisClient(self.config.get('redis', {}))
                logger.info("内存数据库客户端初始化成功")
            except Exception as e:
                logger.error(f"内存数据库客户端初始化失败: {str(e)}")
                self.memory_db = None
        else:
            self.memory_db = None
            logger.info("内存数据库未启用")
    
    def get_data(self, data_type: str, query_params: Dict[str, Any], options: Optional[Dict[str, Any]] = None) -> Any:
        """
        获取数据
        
        Args:
            data_type: 数据类型
            query_params: 查询参数
            options: 选项参数
        
        Returns:
            查询结果
        """
        options = options or {}
        
        # 尝试从缓存获取
        if options.get('use_cache', True) and self.memory_db:
            cache_key = self._generate_cache_key(data_type, query_params)
            cached_data = self.memory_db.get(cache_key)
            if cached_data:
                logger.debug(f"从缓存获取数据: {data_type}")
                return cached_data
        
        # 根据数据类型路由到相应的数据库
        result = None
        try:
            if data_type in RELATIONAL_DATA_TYPES and self.relational_db:
                result = self.relational_db.query(data_type, query_params, options)
            elif data_type in TIME_SERIES_DATA_TYPES and self.time_series_db:
                result = self.time_series_db.query(data_type, query_params, options)
            elif data_type in DOCUMENT_DATA_TYPES and self.document_db:
                result = self.document_db.query(data_type, query_params, options)
            elif data_type in MEMORY_DATA_TYPES and self.memory_db:
                result = self.memory_db.get(data_type, query_params, options)
            else:
                # 回退到文件存储
                result = self._fallback_get(data_type, query_params, options)
        except Exception as e:
            logger.error(f"获取数据失败: {data_type}, {str(e)}")
            # 回退到文件存储
            result = self._fallback_get(data_type, query_params, options)
        
        # 更新缓存
        if result and options.get('update_cache', True) and self.memory_db:
            cache_key = self._generate_cache_key(data_type, query_params)
            cache_ttl = options.get('cache_ttl', 3600)  # 默认缓存1小时
            self.memory_db.set(cache_key, result, {'ttl': cache_ttl})
        
        return result
    
    def save_data(self, data_type: str, data: Any, options: Optional[Dict[str, Any]] = None) -> Any:
        """
        保存数据
        
        Args:
            data_type: 数据类型
            data: 要保存的数据
            options: 选项参数
        
        Returns:
            保存结果
        """
        options = options or {}
        
        # 根据数据类型路由到相应的数据库
        result = None
        try:
            if data_type in RELATIONAL_DATA_TYPES and self.relational_db:
                result = self.relational_db.save(data_type, data, options)
            elif data_type in TIME_SERIES_DATA_TYPES and self.time_series_db:
                result = self.time_series_db.save(data_type, data, options)
            elif data_type in DOCUMENT_DATA_TYPES and self.document_db:
                result = self.document_db.save(data_type, data, options)
            elif data_type in MEMORY_DATA_TYPES and self.memory_db:
                result = self.memory_db.set(data_type, data, options)
            else:
                # 回退到文件存储
                result = self._fallback_save(data_type, data, options)
        except Exception as e:
            logger.error(f"保存数据失败: {data_type}, {str(e)}")
            # 回退到文件存储
            result = self._fallback_save(data_type, data, options)
        
        # 触发数据同步
        if result and options.get('sync', True):
            try:
                self.sync_service.sync_data(data_type, data, result)
            except Exception as e:
                logger.error(f"数据同步失败: {data_type}, {str(e)}")
        
        # 清除相关缓存
        if result and options.get('clear_cache', True) and self.memory_db:
            cache_key = self._generate_cache_key(data_type, self._extract_query_params(data_type, data))
            self.memory_db.delete(cache_key)
        
        return result
    
    def delete_data(self, data_type: str, query_params: Dict[str, Any], options: Optional[Dict[str, Any]] = None) -> bool:
        """
        删除数据
        
        Args:
            data_type: 数据类型
            query_params: 查询参数
            options: 选项参数
        
        Returns:
            删除结果
        """
        options = options or {}
        
        # 根据数据类型路由到相应的数据库
        result = False
        try:
            if data_type in RELATIONAL_DATA_TYPES and self.relational_db:
                result = self.relational_db.delete(data_type, query_params, options)
            elif data_type in TIME_SERIES_DATA_TYPES and self.time_series_db:
                result = self.time_series_db.delete(data_type, query_params, options)
            elif data_type in DOCUMENT_DATA_TYPES and self.document_db:
                result = self.document_db.delete(data_type, query_params, options)
            elif data_type in MEMORY_DATA_TYPES and self.memory_db:
                result = self.memory_db.delete(data_type, query_params, options)
            else:
                # 回退到文件存储
                result = self._fallback_delete(data_type, query_params, options)
        except Exception as e:
            logger.error(f"删除数据失败: {data_type}, {str(e)}")
            # 回退到文件存储
            result = self._fallback_delete(data_type, query_params, options)
        
        # 清除相关缓存
        if result and options.get('clear_cache', True) and self.memory_db:
            cache_key = self._generate_cache_key(data_type, query_params)
            self.memory_db.delete(cache_key)
        
        return result
    
    def _generate_cache_key(self, data_type: str, query_params: Dict[str, Any]) -> str:
        """
        生成缓存键
        
        Args:
            data_type: 数据类型
            query_params: 查询参数
        
        Returns:
            缓存键
        """
        # 将查询参数转换为排序后的字符串
        params_str = json.dumps(query_params, sort_keys=True)
        return f"cache:{data_type}:{hash(params_str)}"
    
    def _extract_query_params(self, data_type: str, data: Any) -> Dict[str, Any]:
        """
        从数据中提取查询参数
        
        Args:
            data_type: 数据类型
            data: 数据
        
        Returns:
            查询参数
        """
        query_params = {}
        
        if data_type in RELATIONAL_DATA_TYPES:
            primary_key = RELATIONAL_DATA_TYPES[data_type].get('primary_key')
            if primary_key and primary_key in data:
                query_params[primary_key] = data[primary_key]
        elif data_type in DOCUMENT_DATA_TYPES:
            index_fields = DOCUMENT_DATA_TYPES[data_type].get('index_fields', [])
            for field in index_fields:
                if field in data:
                    query_params[field] = data[field]
        elif data_type in TIME_SERIES_DATA_TYPES:
            tags = TIME_SERIES_DATA_TYPES[data_type].get('tags', [])
            for tag in tags:
                if tag in data:
                    query_params[tag] = data[tag]
        
        return query_params
    
    def _fallback_get(self, data_type: str, query_params: Dict[str, Any], options: Dict[str, Any]) -> Any:
        """
        回退到文件存储获取数据
        
        Args:
            data_type: 数据类型
            query_params: 查询参数
            options: 选项参数
        
        Returns:
            查询结果
        """
        logger.info(f"回退到文件存储获取数据: {data_type}")
        
        # 使用现有的DataStorage类获取数据
        from core.data_storage import DataStorage, StorageLevel
        
        data_storage = DataStorage()
        module_name = options.get('module_name', 'unified_data_access')
        
        # 尝试从不同存储级别获取数据
        for level in [StorageLevel.HOT, StorageLevel.WARM, StorageLevel.COLD]:
            data = data_storage.load(module_name, data_type, default=None, level=level)
            if data is not None:
                # 如果是列表或字典，尝试根据查询参数过滤
                if isinstance(data, list) and query_params:
                    filtered_data = []
                    for item in data:
                        if all(item.get(k) == v for k, v in query_params.items()):
                            filtered_data.append(item)
                    return filtered_data
                elif isinstance(data, dict) and query_params:
                    # 如果是字典，尝试根据查询参数获取特定项
                    if len(query_params) == 1 and list(query_params.keys())[0] in data:
                        key = list(query_params.keys())[0]
                        return data.get(query_params[key])
                return data
        
        return None
    
    def _fallback_save(self, data_type: str, data: Any, options: Dict[str, Any]) -> Any:
        """
        回退到文件存储保存数据
        
        Args:
            data_type: 数据类型
            data: 要保存的数据
            options: 选项参数
        
        Returns:
            保存结果
        """
        logger.info(f"回退到文件存储保存数据: {data_type}")
        
        # 使用现有的DataStorage类保存数据
        from core.data_storage import DataStorage, StorageLevel
        
        data_storage = DataStorage()
        module_name = options.get('module_name', 'unified_data_access')
        level = options.get('storage_level', StorageLevel.WARM)
        
        # 如果是更新操作，先获取现有数据
        if options.get('update', False):
            existing_data = data_storage.load(module_name, data_type, default=None, level=level)
            if existing_data is not None:
                if isinstance(existing_data, list) and isinstance(data, dict):
                    # 查找并更新列表中的项
                    updated = False
                    for i, item in enumerate(existing_data):
                        if all(item.get(k) == data.get(k) for k in self._extract_query_params(data_type, data)):
                            existing_data[i] = {**item, **data}
                            updated = True
                            break
                    if not updated:
                        existing_data.append(data)
                    data_storage.save(module_name, data_type, existing_data, level)
                    return data
                elif isinstance(existing_data, dict) and isinstance(data, dict):
                    # 更新字典
                    merged_data = {**existing_data, **data}
                    data_storage.save(module_name, data_type, merged_data, level)
                    return data
        
        # 直接保存数据
        data_storage.save(module_name, data_type, data, level)
        return data
    
    def _fallback_delete(self, data_type: str, query_params: Dict[str, Any], options: Dict[str, Any]) -> bool:
        """
        回退到文件存储删除数据
        
        Args:
            data_type: 数据类型
            query_params: 查询参数
            options: 选项参数
        
        Returns:
            删除结果
        """
        logger.info(f"回退到文件存储删除数据: {data_type}")
        
        # 使用现有的DataStorage类删除数据
        from core.data_storage import DataStorage, StorageLevel
        
        data_storage = DataStorage()
        module_name = options.get('module_name', 'unified_data_access')
        level = options.get('storage_level', StorageLevel.WARM)
        
        # 获取现有数据
        existing_data = data_storage.load(module_name, data_type, default=None, level=level)
        if existing_data is None:
            return False
        
        if isinstance(existing_data, list) and query_params:
            # 过滤列表
            filtered_data = [item for item in existing_data if not all(item.get(k) == v for k, v in query_params.items())]
            if len(filtered_data) < len(existing_data):
                data_storage.save(module_name, data_type, filtered_data, level)
                return True
        elif isinstance(existing_data, dict) and query_params:
            # 从字典中删除项
            if len(query_params) == 1 and list(query_params.keys())[0] in existing_data:
                key = list(query_params.keys())[0]
                if existing_data.get(key) == query_params[key]:
                    del existing_data[key]
                    data_storage.save(module_name, data_type, existing_data, level)
                    return True
        
        return False
