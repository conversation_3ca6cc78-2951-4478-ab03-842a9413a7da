"""
数据存储模块

实现多级数据存储系统，支持高效的数据访问和分析
"""

import os
import logging
import json
import pickle
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Union, Tuple
import pandas as pd
import numpy as np
import hashlib
import threading
import shutil

# 尝试导入可选依赖
try:
    import redis
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False

try:
    from sqlalchemy import create_engine, MetaData, Table, Column, Integer, String, Float, DateTime, JSON, select
    from sqlalchemy.ext.declarative import declarative_base
    from sqlalchemy.orm import sessionmaker
    SQLALCHEMY_AVAILABLE = True
except ImportError:
    SQLALCHEMY_AVAILABLE = False

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/data_storage.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('data_storage')

class StorageLevel:
    """存储级别枚举"""
    HOT = "hot"  # 热数据，内存存储
    WARM = "warm"  # 温数据，本地文件存储
    COLD = "cold"  # 冷数据，压缩文件存储或数据库

class DataStorage:
    """数据存储类"""

    def __init__(self, config_path: str = 'config/data_storage_config.json'):
        """
        初始化数据存储

        Args:
            config_path: 配置文件路径
        """
        self.config_path = config_path
        self.config = self._load_config()

        # 创建必要的目录
        os.makedirs('data/hot', exist_ok=True)
        os.makedirs('data/warm', exist_ok=True)
        os.makedirs('data/cold', exist_ok=True)
        os.makedirs('data/backup', exist_ok=True)

        # 内存缓存
        self.memory_cache = {}
        self.memory_cache_lock = threading.RLock()

        # Redis客户端
        self.redis_client = None
        if self.config.get('redis', {}).get('enabled', False) and REDIS_AVAILABLE:
            try:
                self.redis_client = redis.Redis(
                    host=self.config['redis'].get('host', 'localhost'),
                    port=self.config['redis'].get('port', 6379),
                    db=self.config['redis'].get('db', 0),
                    password=self.config['redis'].get('password', None),
                    decode_responses=True
                )
                logger.info("Redis客户端初始化成功")
            except Exception as e:
                logger.error(f"Redis客户端初始化失败: {str(e)}")

        # 数据库引擎
        self.db_engine = None
        self.db_session = None
        if self.config.get('database', {}).get('enabled', False) and SQLALCHEMY_AVAILABLE:
            try:
                db_url = self.config['database'].get('url', 'sqlite:///data/storage.db')
                self.db_engine = create_engine(db_url)
                Session = sessionmaker(bind=self.db_engine)
                self.db_session = Session()
                logger.info(f"数据库引擎初始化成功: {db_url}")
            except Exception as e:
                logger.error(f"数据库引擎初始化失败: {str(e)}")

        # 启动清理线程
        self.cleanup_thread = threading.Thread(target=self._cleanup_thread, daemon=True)
        self.cleanup_thread.start()

        logger.info("数据存储初始化完成")

    def _load_config(self) -> Dict[str, Any]:
        """加载配置"""
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                # 默认配置
                default_config = {
                    "memory_cache": {
                        "enabled": True,
                        "max_size_mb": 512,
                        "ttl_seconds": 3600
                    },
                    "redis": {
                        "enabled": False,
                        "host": "localhost",
                        "port": 6379,
                        "db": 0,
                        "password": None,
                        "ttl_seconds": 86400
                    },
                    "database": {
                        "enabled": False,
                        "url": "sqlite:///data/storage.db",
                        "auto_create_tables": True
                    },
                    "file_storage": {
                        "hot_dir": "data/hot",
                        "warm_dir": "data/warm",
                        "cold_dir": "data/cold",
                        "backup_dir": "data/backup",
                        "hot_to_warm_days": 7,
                        "warm_to_cold_days": 30,
                        "compression": True
                    },
                    "cleanup": {
                        "enabled": True,
                        "interval_hours": 24,
                        "max_hot_size_mb": 1024,
                        "max_warm_size_mb": 5120,
                        "max_memory_cache_size_mb": 512
                    }
                }

                # 保存默认配置
                os.makedirs(os.path.dirname(self.config_path), exist_ok=True)
                with open(self.config_path, 'w', encoding='utf-8') as f:
                    json.dump(default_config, f, indent=4)

                return default_config
        except Exception as e:
            logger.error(f"加载配置失败: {str(e)}")
            return {
                "memory_cache": {"enabled": True, "max_size_mb": 512, "ttl_seconds": 3600},
                "file_storage": {
                    "hot_dir": "data/hot",
                    "warm_dir": "data/warm",
                    "cold_dir": "data/cold"
                }
            }

    def _generate_key(self, module: str, name: str) -> str:
        """
        生成存储键

        Args:
            module: 模块名称
            name: 数据名称

        Returns:
            key: 存储键
        """
        return f"{module}:{name}"

    def _generate_file_path(self, level: str, module: str, name: str, ext: str = 'json') -> str:
        """
        生成文件路径

        Args:
            level: 存储级别
            module: 模块名称
            name: 数据名称
            ext: 文件扩展名

        Returns:
            path: 文件路径
        """
        base_dir = self.config['file_storage'].get(f'{level}_dir', f'data/{level}')
        os.makedirs(os.path.join(base_dir, module), exist_ok=True)
        return os.path.join(base_dir, module, f"{name}.{ext}")

    def _get_data_size(self, data: Any) -> int:
        """
        获取数据大小（字节）

        Args:
            data: 数据

        Returns:
            size: 数据大小
        """
        try:
            if isinstance(data, (str, bytes)):
                return len(data)
            elif isinstance(data, (dict, list)):
                return len(json.dumps(data).encode('utf-8'))
            elif isinstance(data, pd.DataFrame):
                return data.memory_usage(deep=True).sum()
            else:
                # 使用pickle估算大小
                return len(pickle.dumps(data))
        except Exception as e:
            logger.error(f"计算数据大小失败: {str(e)}")
            return 0

    def _cleanup_thread(self):
        """清理线程"""
        if not self.config.get('cleanup', {}).get('enabled', True):
            return

        interval_hours = self.config.get('cleanup', {}).get('interval_hours', 24)

        while True:
            try:
                # 休眠指定时间
                time.sleep(interval_hours * 3600)

                # 执行清理
                self._cleanup_storage()

            except Exception as e:
                logger.error(f"清理线程发生错误: {str(e)}")
                time.sleep(3600)  # 出错后休眠1小时

    def _cleanup_storage(self):
        """清理存储"""
        logger.info("开始清理存储...")

        try:
            # 清理内存缓存
            self._cleanup_memory_cache()

            # 清理文件存储
            self._cleanup_file_storage()

            # 移动过期数据
            self._move_expired_data()

            logger.info("存储清理完成")

        except Exception as e:
            logger.error(f"存储清理失败: {str(e)}")

    def _cleanup_memory_cache(self):
        """清理内存缓存"""
        if not self.config.get('memory_cache', {}).get('enabled', True):
            return

        with self.memory_cache_lock:
            # 清理过期数据
            current_time = time.time()
            expired_keys = []

            for key, (data, timestamp, _) in self.memory_cache.items():
                ttl = self.config.get('memory_cache', {}).get('ttl_seconds', 3600)
                if current_time - timestamp > ttl:
                    expired_keys.append(key)

            # 删除过期数据
            for key in expired_keys:
                del self.memory_cache[key]

            if expired_keys:
                logger.info(f"清理内存缓存: 删除{len(expired_keys)}个过期项")

            # 如果缓存仍然过大，删除最旧的数据
            max_size = self.config.get('cleanup', {}).get('max_memory_cache_size_mb', 512) * 1024 * 1024
            current_size = sum(size for _, _, size in self.memory_cache.values())

            if current_size > max_size:
                # 按时间戳排序
                sorted_items = sorted(self.memory_cache.items(), key=lambda x: x[1][1])

                # 删除最旧的数据，直到大小合适
                deleted_count = 0
                for key, (_, _, size) in sorted_items:
                    del self.memory_cache[key]
                    current_size -= size
                    deleted_count += 1

                    if current_size <= max_size:
                        break

                logger.info(f"清理内存缓存: 删除{deleted_count}个最旧项以减小缓存大小")

    def _cleanup_file_storage(self):
        """清理文件存储"""
        # 清理热存储
        hot_dir = self.config['file_storage'].get('hot_dir', 'data/hot')
        max_hot_size = self.config.get('cleanup', {}).get('max_hot_size_mb', 1024) * 1024 * 1024

        self._cleanup_directory(hot_dir, max_hot_size)

        # 清理温存储
        warm_dir = self.config['file_storage'].get('warm_dir', 'data/warm')
        max_warm_size = self.config.get('cleanup', {}).get('max_warm_size_mb', 5120) * 1024 * 1024

        self._cleanup_directory(warm_dir, max_warm_size)

    def _cleanup_directory(self, directory: str, max_size: int):
        """
        清理目录

        Args:
            directory: 目录路径
            max_size: 最大大小（字节）
        """
        if not os.path.exists(directory):
            return

        # 获取所有文件及其修改时间和大小
        files_info = []
        total_size = 0

        for root, _, files in os.walk(directory):
            for file in files:
                file_path = os.path.join(root, file)
                file_size = os.path.getsize(file_path)
                file_mtime = os.path.getmtime(file_path)

                files_info.append((file_path, file_mtime, file_size))
                total_size += file_size

        # 如果总大小超过限制，删除最旧的文件
        if total_size > max_size:
            # 按修改时间排序
            files_info.sort(key=lambda x: x[1])

            # 删除最旧的文件，直到大小合适
            deleted_count = 0
            deleted_size = 0

            for file_path, _, file_size in files_info:
                os.remove(file_path)
                deleted_size += file_size
                deleted_count += 1

                if total_size - deleted_size <= max_size:
                    break

            logger.info(f"清理目录 {directory}: 删除{deleted_count}个文件，释放{deleted_size/1024/1024:.2f}MB空间")

    def _move_expired_data(self):
        """移动过期数据"""
        # 从热存储移动到温存储
        hot_dir = self.config['file_storage'].get('hot_dir', 'data/hot')
        warm_dir = self.config['file_storage'].get('warm_dir', 'data/warm')
        hot_to_warm_days = self.config['file_storage'].get('hot_to_warm_days', 7)

        self._move_old_files(hot_dir, warm_dir, hot_to_warm_days)

        # 从温存储移动到冷存储
        cold_dir = self.config['file_storage'].get('cold_dir', 'data/cold')
        warm_to_cold_days = self.config['file_storage'].get('warm_to_cold_days', 30)

        self._move_old_files(warm_dir, cold_dir, warm_to_cold_days)

    def _move_old_files(self, source_dir: str, target_dir: str, days: int):
        """
        移动旧文件

        Args:
            source_dir: 源目录
            target_dir: 目标目录
            days: 天数阈值
        """
        if not os.path.exists(source_dir):
            return

        # 获取所有文件及其修改时间
        current_time = time.time()
        threshold = current_time - days * 86400

        moved_count = 0

        for root, _, files in os.walk(source_dir):
            for file in files:
                file_path = os.path.join(root, file)
                file_mtime = os.path.getmtime(file_path)

                if file_mtime < threshold:
                    # 计算目标路径
                    rel_path = os.path.relpath(file_path, source_dir)
                    target_path = os.path.join(target_dir, rel_path)

                    # 确保目标目录存在
                    os.makedirs(os.path.dirname(target_path), exist_ok=True)

                    # 移动文件
                    shutil.move(file_path, target_path)
                    moved_count += 1

        if moved_count > 0:
            logger.info(f"移动旧文件: 从{source_dir}移动{moved_count}个文件到{target_dir}")

    def save(self, module: str, name: str, data: Any, level: str = StorageLevel.HOT) -> bool:
        """
        保存数据

        Args:
            module: 模块名称
            name: 数据名称
            data: 数据
            level: 存储级别

        Returns:
            success: 是否成功
        """
        try:
            key = self._generate_key(module, name)
            data_size = self._get_data_size(data)

            # 保存到内存缓存
            if level == StorageLevel.HOT and self.config.get('memory_cache', {}).get('enabled', True):
                with self.memory_cache_lock:
                    self.memory_cache[key] = (data, time.time(), data_size)

            # 保存到Redis
            if level == StorageLevel.HOT and self.redis_client is not None:
                try:
                    # 序列化数据
                    if isinstance(data, pd.DataFrame):
                        serialized_data = data.to_json(orient='split')
                    else:
                        serialized_data = json.dumps(data)

                    # 设置TTL
                    ttl = self.config.get('redis', {}).get('ttl_seconds', 86400)

                    # 保存到Redis
                    self.redis_client.setex(key, ttl, serialized_data)
                except Exception as e:
                    logger.error(f"保存到Redis失败: {str(e)}")

            # 保存到文件
            file_ext = 'json'
            if isinstance(data, pd.DataFrame):
                file_ext = 'csv'

            file_path = self._generate_file_path(level.lower(), module, name, file_ext)

            if isinstance(data, pd.DataFrame):
                data.to_csv(file_path, index=False)
            elif isinstance(data, (dict, list)):
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2)
            else:
                with open(file_path, 'wb') as f:
                    pickle.dump(data, f)

            logger.info(f"保存数据成功: {key}, 级别: {level}, 大小: {data_size/1024:.2f}KB")
            return True

        except Exception as e:
            logger.error(f"保存数据失败: {module}:{name}, 错误: {str(e)}")
            return False

    def store_data(self, data_type: str, data: Any, level: str = StorageLevel.HOT) -> bool:
        """
        存储数据（统一接口）

        Args:
            data_type: 数据类型（如news, fund_flow, market等）
            data: 数据
            level: 存储级别

        Returns:
            success: 是否成功
        """
        try:
            # 生成唯一的数据名称
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            data_name = f"{data_type}_{timestamp}"

            return self.save(data_type, data_name, data, level)

        except Exception as e:
            logger.error(f"存储数据失败: {data_type}, 错误: {str(e)}")
            return False

    def get_latest_data(self, data_type: str, limit: int = 10) -> List[Any]:
        """
        获取最新数据

        Args:
            data_type: 数据类型
            limit: 限制数量

        Returns:
            data_list: 数据列表
        """
        try:
            # 从文件系统获取最新数据
            data_list = []

            # 检查各个存储级别
            for level in [StorageLevel.HOT, StorageLevel.WARM, StorageLevel.COLD]:
                level_dir = self.config['file_storage'].get(f'{level}_dir', f'data/{level}')
                type_dir = os.path.join(level_dir, data_type)

                if os.path.exists(type_dir):
                    # 获取所有文件
                    files = []
                    for file in os.listdir(type_dir):
                        if file.startswith(data_type):
                            file_path = os.path.join(type_dir, file)
                            file_mtime = os.path.getmtime(file_path)
                            files.append((file_path, file_mtime))

                    # 按修改时间排序
                    files.sort(key=lambda x: x[1], reverse=True)

                    # 加载最新的文件
                    for file_path, _ in files[:limit]:
                        try:
                            if file_path.endswith('.json'):
                                with open(file_path, 'r', encoding='utf-8') as f:
                                    data = json.load(f)
                                    data_list.append(data)
                            elif file_path.endswith('.csv'):
                                data = pd.read_csv(file_path)
                                data_list.append(data.to_dict('records'))
                            else:
                                with open(file_path, 'rb') as f:
                                    data = pickle.load(f)
                                    data_list.append(data)
                        except Exception as e:
                            logger.warning(f"加载文件失败: {file_path}, 错误: {str(e)}")

                    if len(data_list) >= limit:
                        break

            return data_list[:limit]

        except Exception as e:
            logger.error(f"获取最新数据失败: {data_type}, 错误: {str(e)}")
            return []

    def get_storage_status(self) -> Dict[str, Any]:
        """
        获取存储状态

        Returns:
            status: 存储状态信息
        """
        try:
            status = {
                'memory_cache': {
                    'enabled': self.config.get('memory_cache', {}).get('enabled', True),
                    'items': len(self.memory_cache),
                    'size_mb': sum(size for _, _, size in self.memory_cache.values()) / 1024 / 1024
                },
                'redis': {
                    'enabled': self.redis_client is not None,
                    'connected': False
                },
                'database': {
                    'enabled': self.db_engine is not None,
                    'connected': False
                },
                'file_storage': {}
            }

            # 检查Redis连接
            if self.redis_client:
                try:
                    self.redis_client.ping()
                    status['redis']['connected'] = True
                except:
                    pass

            # 检查数据库连接
            if self.db_engine:
                try:
                    self.db_engine.execute("SELECT 1")
                    status['database']['connected'] = True
                except:
                    pass

            # 检查文件存储
            for level in [StorageLevel.HOT, StorageLevel.WARM, StorageLevel.COLD]:
                level_dir = self.config['file_storage'].get(f'{level}_dir', f'data/{level}')
                if os.path.exists(level_dir):
                    # 计算目录大小
                    total_size = 0
                    file_count = 0

                    for root, _, files in os.walk(level_dir):
                        for file in files:
                            file_path = os.path.join(root, file)
                            total_size += os.path.getsize(file_path)
                            file_count += 1

                    status['file_storage'][level] = {
                        'size_mb': total_size / 1024 / 1024,
                        'file_count': file_count
                    }

            return status

        except Exception as e:
            logger.error(f"获取存储状态失败: {str(e)}")
            return {}

    def load(self, module: str, name: str, default: Any = None) -> Any:
        """
        加载数据

        Args:
            module: 模块名称
            name: 数据名称
            default: 默认值

        Returns:
            data: 数据
        """
        try:
            key = self._generate_key(module, name)

            # 从内存缓存加载
            if self.config.get('memory_cache', {}).get('enabled', True):
                with self.memory_cache_lock:
                    if key in self.memory_cache:
                        data, timestamp, _ = self.memory_cache[key]

                        # 检查是否过期
                        ttl = self.config.get('memory_cache', {}).get('ttl_seconds', 3600)
                        if time.time() - timestamp <= ttl:
                            return data

            # 从Redis加载
            if self.redis_client is not None:
                try:
                    serialized_data = self.redis_client.get(key)
                    if serialized_data:
                        # 尝试解析为DataFrame
                        try:
                            data = pd.read_json(serialized_data, orient='split')

                            # 更新内存缓存
                            if self.config.get('memory_cache', {}).get('enabled', True):
                                with self.memory_cache_lock:
                                    self.memory_cache[key] = (data, time.time(), self._get_data_size(data))

                            return data
                        except:
                            # 解析为普通JSON
                            data = json.loads(serialized_data)

                            # 更新内存缓存
                            if self.config.get('memory_cache', {}).get('enabled', True):
                                with self.memory_cache_lock:
                                    self.memory_cache[key] = (data, time.time(), self._get_data_size(data))

                            return data
                except Exception as e:
                    logger.error(f"从Redis加载失败: {str(e)}")

            # 从文件加载
            for level in [StorageLevel.HOT, StorageLevel.WARM, StorageLevel.COLD]:
                # 尝试不同的文件扩展名
                for ext in ['json', 'csv', 'pickle']:
                    file_path = self._generate_file_path(level.lower(), module, name, ext)

                    if os.path.exists(file_path):
                        if ext == 'json':
                            with open(file_path, 'r', encoding='utf-8') as f:
                                data = json.load(f)
                        elif ext == 'csv':
                            data = pd.read_csv(file_path)
                        else:  # pickle
                            with open(file_path, 'rb') as f:
                                data = pickle.load(f)

                        # 更新内存缓存
                        if self.config.get('memory_cache', {}).get('enabled', True):
                            with self.memory_cache_lock:
                                self.memory_cache[key] = (data, time.time(), self._get_data_size(data))

                        # 如果是从冷存储或温存储加载的，复制到热存储
                        if level != StorageLevel.HOT:
                            self.save(module, name, data, StorageLevel.HOT)

                        return data

            # 未找到数据，返回默认值
            return default

        except Exception as e:
            logger.error(f"加载数据失败: {module}:{name}, 错误: {str(e)}")
            return default

    def exists(self, module: str, name: str) -> bool:
        """
        检查数据是否存在

        Args:
            module: 模块名称
            name: 数据名称

        Returns:
            exists: 是否存在
        """
        try:
            key = self._generate_key(module, name)

            # 检查内存缓存
            if self.config.get('memory_cache', {}).get('enabled', True):
                with self.memory_cache_lock:
                    if key in self.memory_cache:
                        _, timestamp, _ = self.memory_cache[key]

                        # 检查是否过期
                        ttl = self.config.get('memory_cache', {}).get('ttl_seconds', 3600)
                        if time.time() - timestamp <= ttl:
                            return True

            # 检查Redis
            if self.redis_client is not None:
                try:
                    if self.redis_client.exists(key):
                        return True
                except Exception as e:
                    logger.error(f"检查Redis失败: {str(e)}")

            # 检查文件
            for level in [StorageLevel.HOT, StorageLevel.WARM, StorageLevel.COLD]:
                # 尝试不同的文件扩展名
                for ext in ['json', 'csv', 'pickle']:
                    file_path = self._generate_file_path(level.lower(), module, name, ext)

                    if os.path.exists(file_path):
                        return True

            return False

        except Exception as e:
            logger.error(f"检查数据存在失败: {module}:{name}, 错误: {str(e)}")
            return False

    def delete(self, module: str, name: str) -> bool:
        """
        删除数据

        Args:
            module: 模块名称
            name: 数据名称

        Returns:
            success: 是否成功
        """
        try:
            key = self._generate_key(module, name)

            # 从内存缓存删除
            if self.config.get('memory_cache', {}).get('enabled', True):
                with self.memory_cache_lock:
                    if key in self.memory_cache:
                        del self.memory_cache[key]

            # 从Redis删除
            if self.redis_client is not None:
                try:
                    self.redis_client.delete(key)
                except Exception as e:
                    logger.error(f"从Redis删除失败: {str(e)}")

            # 从文件删除
            deleted = False
            for level in [StorageLevel.HOT, StorageLevel.WARM, StorageLevel.COLD]:
                # 尝试不同的文件扩展名
                for ext in ['json', 'csv', 'pickle']:
                    file_path = self._generate_file_path(level.lower(), module, name, ext)

                    if os.path.exists(file_path):
                        os.remove(file_path)
                        deleted = True

            if deleted:
                logger.info(f"删除数据成功: {key}")

            return True

        except Exception as e:
            logger.error(f"删除数据失败: {module}:{name}, 错误: {str(e)}")
            return False

    def backup(self, module: str = None) -> bool:
        """
        备份数据

        Args:
            module: 模块名称，如果为None则备份所有模块

        Returns:
            success: 是否成功
        """
        try:
            backup_dir = self.config['file_storage'].get('backup_dir', 'data/backup')
            timestamp = datetime.now().strftime('%Y%m%d%H%M%S')

            if module:
                # 备份单个模块
                for level in [StorageLevel.HOT, StorageLevel.WARM]:
                    source_dir = os.path.join(self.config['file_storage'].get(f'{level.lower()}_dir', f'data/{level.lower()}'), module)

                    if os.path.exists(source_dir):
                        target_dir = os.path.join(backup_dir, timestamp, level.lower(), module)
                        os.makedirs(target_dir, exist_ok=True)

                        # 复制文件
                        for root, _, files in os.walk(source_dir):
                            for file in files:
                                source_file = os.path.join(root, file)
                                rel_path = os.path.relpath(source_file, source_dir)
                                target_file = os.path.join(target_dir, rel_path)

                                os.makedirs(os.path.dirname(target_file), exist_ok=True)
                                shutil.copy2(source_file, target_file)

                logger.info(f"备份模块成功: {module}, 时间戳: {timestamp}")
            else:
                # 备份所有模块
                for level in [StorageLevel.HOT, StorageLevel.WARM]:
                    source_dir = self.config['file_storage'].get(f'{level.lower()}_dir', f'data/{level.lower()}')

                    if os.path.exists(source_dir):
                        target_dir = os.path.join(backup_dir, timestamp, level.lower())
                        os.makedirs(target_dir, exist_ok=True)

                        # 复制文件
                        for root, _, files in os.walk(source_dir):
                            for file in files:
                                source_file = os.path.join(root, file)
                                rel_path = os.path.relpath(source_file, source_dir)
                                target_file = os.path.join(target_dir, rel_path)

                                os.makedirs(os.path.dirname(target_file), exist_ok=True)
                                shutil.copy2(source_file, target_file)

                logger.info(f"备份所有模块成功, 时间戳: {timestamp}")

            return True

        except Exception as e:
            logger.error(f"备份数据失败: {str(e)}")
            return False
