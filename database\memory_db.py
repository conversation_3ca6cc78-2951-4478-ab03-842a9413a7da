"""
内存数据库客户端

实现Redis数据库的访问，并提供内存回退方案
"""

import logging
import json
import time
from typing import Dict, Any, List, Optional, Union
import threading

from .db_client import DBClient
from .data_types import MEMORY_DATA_TYPES

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('memory_db')

class RedisClient(DBClient):
    """Redis数据库客户端"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化Redis数据库客户端
        
        Args:
            config: 配置信息
        """
        super().__init__(config)
        
        # 如果配置了使用内存回退，则初始化内存存储
        self.use_memory_fallback = config.get('use_memory_fallback', True)
        self.memory_store = {}
        self.expiry_times = {}
        self.lock = threading.RLock()
        
        # 启动过期检查线程
        if self.use_memory_fallback:
            self._start_expiry_checker()
        
        # 尝试连接数据库
        self.connect()
    
    def connect(self) -> bool:
        """
        连接数据库
        
        Returns:
            连接结果
        """
        try:
            # 尝试导入redis
            import redis
            
            # 连接Redis
            self.connection = redis.Redis(
                host=self.config.get('host', 'localhost'),
                port=self.config.get('port', 6379),
                db=self.config.get('db', 0),
                password=self.config.get('password')
            )
            
            # 测试连接
            self.connection.ping()
            
            self._log_info("Redis数据库连接成功")
            return True
        
        except ImportError:
            self._log_error("redis模块未安装，无法连接Redis", Exception("Module not found"))
            
            # 如果配置了使用内存回退，则使用内存存储
            if self.use_memory_fallback:
                self._log_info("使用内存存储作为Redis的回退")
                return True
            
            return False
        
        except Exception as e:
            self._log_error("Redis数据库连接失败", e)
            
            # 如果配置了使用内存回退，则使用内存存储
            if self.use_memory_fallback:
                self._log_info("使用内存存储作为Redis的回退")
                return True
            
            return False
    
    def _start_expiry_checker(self):
        """启动过期检查线程"""
        def check_expiry():
            while True:
                try:
                    current_time = time.time()
                    with self.lock:
                        # 找出已过期的键
                        expired_keys = [k for k, v in self.expiry_times.items() if v <= current_time]
                        
                        # 删除过期的键
                        for key in expired_keys:
                            if key in self.memory_store:
                                del self.memory_store[key]
                            if key in self.expiry_times:
                                del self.expiry_times[key]
                    
                    # 每秒检查一次
                    time.sleep(1)
                except Exception as e:
                    self._log_error("过期检查线程异常", e)
                    time.sleep(5)
        
        # 启动线程
        thread = threading.Thread(target=check_expiry, daemon=True)
        thread.start()
    
    def disconnect(self) -> bool:
        """
        断开数据库连接
        
        Returns:
            断开结果
        """
        try:
            if self.connection:
                self.connection.close()
                self.connection = None
            
            self._log_info("Redis连接已断开")
            return True
        
        except Exception as e:
            self._log_error("断开Redis连接失败", e)
            return False
    
    def is_connected(self) -> bool:
        """
        检查数据库连接状态
        
        Returns:
            连接状态
        """
        if self.connection:
            try:
                # 尝试执行ping命令
                self.connection.ping()
                return True
            except:
                return False
        
        # 如果使用内存回退，则始终返回True
        return self.use_memory_fallback
    
    def get(self, data_type: str, query_params: Dict[str, Any] = None, options: Optional[Dict[str, Any]] = None) -> Any:
        """
        获取数据
        
        Args:
            data_type: 数据类型或键
            query_params: 查询参数，用于生成键
            options: 选项参数
        
        Returns:
            查询结果
        """
        options = options or {}
        
        # 生成键
        key = self._generate_key(data_type, query_params)
        
        # 使用Redis或内存存储获取数据
        if self.connection:
            try:
                return self._get_redis(key, options)
            except Exception as e:
                self._log_error(f"Redis获取失败: {key}", e)
                if self.use_memory_fallback:
                    return self._get_memory(key, options)
                raise
        elif self.use_memory_fallback:
            return self._get_memory(key, options)
        else:
            raise ConnectionError("Redis未连接且未启用内存回退")
    
    def _get_redis(self, key: str, options: Dict[str, Any]) -> Any:
        """
        从Redis获取数据
        
        Args:
            key: 键
            options: 选项参数
        
        Returns:
            查询结果
        """
        # 获取数据类型
        data_type = options.get('data_type', 'string')
        
        if data_type == 'hash':
            # 获取哈希表
            result = self.connection.hgetall(key)
            if result:
                return {k.decode('utf-8'): self._decode_value(v) for k, v in result.items()}
            return None
        
        elif data_type == 'list':
            # 获取列表
            result = self.connection.lrange(key, 0, -1)
            if result:
                return [self._decode_value(item) for item in result]
            return None
        
        elif data_type == 'set':
            # 获取集合
            result = self.connection.smembers(key)
            if result:
                return [self._decode_value(item) for item in result]
            return None
        
        elif data_type == 'zset':
            # 获取有序集合
            result = self.connection.zrange(key, 0, -1, withscores=True)
            if result:
                return [{self._decode_value(item[0]): item[1]} for item in result]
            return None
        
        else:
            # 获取字符串
            result = self.connection.get(key)
            if result:
                return self._decode_value(result)
            return None
    
    def _get_memory(self, key: str, options: Dict[str, Any]) -> Any:
        """
        从内存存储获取数据
        
        Args:
            key: 键
            options: 选项参数
        
        Returns:
            查询结果
        """
        with self.lock:
            # 检查键是否存在
            if key in self.memory_store:
                # 检查是否已过期
                if key in self.expiry_times and self.expiry_times[key] <= time.time():
                    # 删除过期数据
                    del self.memory_store[key]
                    del self.expiry_times[key]
                    return None
                
                return self.memory_store[key]
            
            return None
    
    def set(self, data_type: str, data: Any, options: Optional[Dict[str, Any]] = None) -> bool:
        """
        设置数据
        
        Args:
            data_type: 数据类型或键
            data: 要设置的数据
            options: 选项参数
        
        Returns:
            设置结果
        """
        options = options or {}
        
        # 生成键
        key = self._generate_key(data_type, options.get('query_params'))
        
        # 使用Redis或内存存储设置数据
        if self.connection:
            try:
                return self._set_redis(key, data, options)
            except Exception as e:
                self._log_error(f"Redis设置失败: {key}", e)
                if self.use_memory_fallback:
                    return self._set_memory(key, data, options)
                raise
        elif self.use_memory_fallback:
            return self._set_memory(key, data, options)
        else:
            raise ConnectionError("Redis未连接且未启用内存回退")
    
    def _set_redis(self, key: str, data: Any, options: Dict[str, Any]) -> bool:
        """
        向Redis设置数据
        
        Args:
            key: 键
            data: 要设置的数据
            options: 选项参数
        
        Returns:
            设置结果
        """
        # 获取数据类型和过期时间
        data_type = options.get('data_type', 'string')
        ttl = options.get('ttl')
        
        if data_type == 'hash' and isinstance(data, dict):
            # 设置哈希表
            encoded_data = {k: self._encode_value(v) for k, v in data.items()}
            self.connection.delete(key)  # 先删除旧数据
            if encoded_data:
                self.connection.hset(key, mapping=encoded_data)
        
        elif data_type == 'list' and isinstance(data, list):
            # 设置列表
            self.connection.delete(key)  # 先删除旧数据
            if data:
                encoded_data = [self._encode_value(item) for item in data]
                self.connection.rpush(key, *encoded_data)
        
        elif data_type == 'set' and isinstance(data, (list, set)):
            # 设置集合
            self.connection.delete(key)  # 先删除旧数据
            if data:
                encoded_data = [self._encode_value(item) for item in data]
                self.connection.sadd(key, *encoded_data)
        
        elif data_type == 'zset' and isinstance(data, (list, dict)):
            # 设置有序集合
            self.connection.delete(key)  # 先删除旧数据
            if isinstance(data, list) and all(isinstance(item, dict) for item in data):
                # 列表中的每个元素都是一个字典，键为成员，值为分数
                for item in data:
                    for member, score in item.items():
                        self.connection.zadd(key, {self._encode_value(member): score})
            elif isinstance(data, dict):
                # 字典，键为成员，值为分数
                encoded_data = {self._encode_value(k): v for k, v in data.items()}
                self.connection.zadd(key, encoded_data)
        
        else:
            # 设置字符串
            encoded_data = self._encode_value(data)
            self.connection.set(key, encoded_data)
        
        # 设置过期时间
        if ttl:
            self.connection.expire(key, ttl)
        
        return True
    
    def _set_memory(self, key: str, data: Any, options: Dict[str, Any]) -> bool:
        """
        向内存存储设置数据
        
        Args:
            key: 键
            data: 要设置的数据
            options: 选项参数
        
        Returns:
            设置结果
        """
        with self.lock:
            # 存储数据
            self.memory_store[key] = data
            
            # 设置过期时间
            ttl = options.get('ttl')
            if ttl:
                self.expiry_times[key] = time.time() + ttl
            elif key in self.expiry_times:
                del self.expiry_times[key]
            
            return True
    
    def delete(self, data_type: str, query_params: Dict[str, Any] = None, options: Optional[Dict[str, Any]] = None) -> bool:
        """
        删除数据
        
        Args:
            data_type: 数据类型或键
            query_params: 查询参数，用于生成键
            options: 选项参数
        
        Returns:
            删除结果
        """
        options = options or {}
        
        # 生成键
        key = self._generate_key(data_type, query_params)
        
        # 使用Redis或内存存储删除数据
        if self.connection:
            try:
                return self._delete_redis(key)
            except Exception as e:
                self._log_error(f"Redis删除失败: {key}", e)
                if self.use_memory_fallback:
                    return self._delete_memory(key)
                raise
        elif self.use_memory_fallback:
            return self._delete_memory(key)
        else:
            raise ConnectionError("Redis未连接且未启用内存回退")
    
    def _delete_redis(self, key: str) -> bool:
        """
        从Redis删除数据
        
        Args:
            key: 键
        
        Returns:
            删除结果
        """
        return bool(self.connection.delete(key))
    
    def _delete_memory(self, key: str) -> bool:
        """
        从内存存储删除数据
        
        Args:
            key: 键
        
        Returns:
            删除结果
        """
        with self.lock:
            # 检查键是否存在
            if key in self.memory_store:
                del self.memory_store[key]
                if key in self.expiry_times:
                    del self.expiry_times[key]
                return True
            
            return False
    
    def query(self, data_type: str, query_params: Dict[str, Any], options: Optional[Dict[str, Any]] = None) -> Any:
        """
        查询数据（兼容DBClient接口）
        
        Args:
            data_type: 数据类型
            query_params: 查询参数
            options: 选项参数
        
        Returns:
            查询结果
        """
        return self.get(data_type, query_params, options)
    
    def save(self, data_type: str, data: Any, options: Optional[Dict[str, Any]] = None) -> Any:
        """
        保存数据（兼容DBClient接口）
        
        Args:
            data_type: 数据类型
            data: 要保存的数据
            options: 选项参数
        
        Returns:
            保存结果
        """
        return self.set(data_type, data, options)
    
    def _generate_key(self, data_type: str, query_params: Optional[Dict[str, Any]] = None) -> str:
        """
        生成键
        
        Args:
            data_type: 数据类型或键模板
            query_params: 查询参数，用于替换键模板中的占位符
        
        Returns:
            生成的键
        """
        # 如果data_type已经是完整的键，则直接返回
        if ':' in data_type and '{' not in data_type:
            return data_type
        
        # 如果data_type是数据类型，则查找对应的键模板
        if data_type in MEMORY_DATA_TYPES:
            key_pattern = MEMORY_DATA_TYPES[data_type].get('key_pattern', '')
            if key_pattern and query_params:
                try:
                    return key_pattern.format(**query_params)
                except KeyError:
                    # 如果格式化失败，则使用原始键模板
                    return key_pattern
            return key_pattern
        
        # 如果data_type是键模板，则尝试使用query_params进行格式化
        if '{' in data_type and '}' in data_type and query_params:
            try:
                return data_type.format(**query_params)
            except KeyError:
                # 如果格式化失败，则使用原始键模板
                return data_type
        
        return data_type
    
    def _encode_value(self, value: Any) -> bytes:
        """
        编码值
        
        Args:
            value: 要编码的值
        
        Returns:
            编码后的值
        """
        if value is None:
            return b''
        
        if isinstance(value, (bytes, bytearray)):
            return value
        
        if isinstance(value, (int, float, bool, str)):
            return str(value).encode('utf-8')
        
        # 对于复杂类型，使用JSON编码
        return json.dumps(value).encode('utf-8')
    
    def _decode_value(self, value: bytes) -> Any:
        """
        解码值
        
        Args:
            value: 要解码的值
        
        Returns:
            解码后的值
        """
        if value is None or value == b'':
            return None
        
        # 尝试解码为字符串
        try:
            decoded = value.decode('utf-8')
            
            # 尝试解析为JSON
            try:
                return json.loads(decoded)
            except json.JSONDecodeError:
                # 如果不是有效的JSON，则返回字符串
                return decoded
        
        except UnicodeDecodeError:
            # 如果无法解码为字符串，则返回原始字节
            return value
