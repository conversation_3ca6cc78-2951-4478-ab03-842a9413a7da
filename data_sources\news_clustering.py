"""
新闻去重与聚类模块

提供高效的新闻去重和基于主题的聚类功能
"""

import os
import logging
import json
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import time
import re
from typing import Dict, List, Any, Optional, Union, Tuple
import math
from collections import defaultdict
import hashlib
import jieba
import jieba.analyse
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.cluster import DBSCAN

# 导入模块接口
from core.module_interface import ModuleInterface

# 导入数据存储
from core.data_storage import DataStorage, StorageLevel

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/news_clustering.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('news_clustering')

# 尝试导入NLP相关库
try:
    import torch
    from transformers import BertTokenizer, BertModel
    NLP_AVAILABLE = True
except ImportError:
    NLP_AVAILABLE = False
    logger.warning("NLP相关库未安装，将使用简化版文本表示")

class NewsClusteringModule(ModuleInterface):
    """新闻去重与聚类模块类"""

    def __init__(self):
        """初始化新闻去重与聚类模块"""
        super().__init__(module_name='news_clustering')

        # 创建数据存储
        self.data_storage = DataStorage()

        # 上次更新时间
        self.last_update_time = datetime.now()

        # 加载停用词
        self.stopwords = self._load_stopwords()

        # 初始化分词器
        jieba.initialize()

        # 初始化TF-IDF向量化器
        self.vectorizer = TfidfVectorizer(
            max_features=5000,
            min_df=2,
            max_df=0.8
        )

        # 初始化BERT模型
        self.bert_model = None
        self.tokenizer = None
        if NLP_AVAILABLE:
            self._init_bert_model()

        # 聚类参数
        self.clustering_params = {
            'similarity_threshold': 0.7,  # 相似度阈值
            'min_cluster_size': 2,        # 最小聚类大小
            'max_cluster_size': 50,       # 最大聚类大小
            'dbscan_eps': 0.3,            # DBSCAN邻域大小
            'dbscan_min_samples': 2       # DBSCAN最小样本数
        }

        # 去重参数
        self.deduplication_params = {
            'title_similarity_threshold': 0.8,  # 标题相似度阈值
            'content_similarity_threshold': 0.7, # 内容相似度阈值
            'time_window_hours': 24,            # 时间窗口（小时）
            'use_simhash': True                 # 是否使用SimHash
        }

        # 聚类结果
        self.clusters = []

        # 已处理的新闻指纹
        self.news_fingerprints = self.data_storage.load('news_clustering', 'news_fingerprints', {})

        logger.info("新闻去重与聚类模块初始化完成")

    def _load_stopwords(self) -> List[str]:
        """
        加载停用词

        Returns:
            stopwords: 停用词列表
        """
        # 尝试从数据存储加载
        stopwords = self.data_storage.load('news_clustering', 'stopwords')
        if stopwords:
            logger.info(f"从数据存储加载了{len(stopwords)}个停用词")
            return stopwords

        # 如果数据存储中没有，尝试从文件加载
        try:
            stopwords_file = os.path.join('data', 'news_clustering', 'stopwords.txt')
            if os.path.exists(stopwords_file):
                with open(stopwords_file, 'r', encoding='utf-8') as f:
                    stopwords = [line.strip() for line in f if line.strip()]
                logger.info(f"从文件加载了{len(stopwords)}个停用词")

                # 保存到数据存储
                self.data_storage.save('news_clustering', 'stopwords', stopwords)

                return stopwords
        except Exception as e:
            logger.error(f"加载停用词文件失败: {str(e)}")

        # 如果都没有，使用简单的停用词列表
        stopwords = [
            '的', '了', '和', '是', '就', '都', '而', '及', '与', '着',
            '或', '一个', '没有', '我们', '你们', '他们', '她们', '它们',
            '这个', '那个', '这些', '那些', '不', '在', '人', '有', '是',
            '为', '以', '于', '上', '下', '但', '不', '这', '那', '所',
            '因', '由', '被', '之', '所', '以', '于', '则', '乃', '其',
            '且', '云', '尔', '尚', '矣', '盖', '兮', '焉', '也', '矣'
        ]

        # 保存到数据存储
        self.data_storage.save('news_clustering', 'stopwords', stopwords)

        logger.info(f"使用默认停用词列表，包含{len(stopwords)}个词")
        return stopwords

    def _init_bert_model(self):
        """初始化BERT模型"""
        try:
            # 加载BERT模型和分词器
            model_name = self.config.get('bert_model', 'bert-base-chinese')
            self.tokenizer = BertTokenizer.from_pretrained(model_name)
            self.bert_model = BertModel.from_pretrained(model_name)

            # 设置为评估模式
            self.bert_model.eval()

            # 如果有GPU，使用GPU
            if torch.cuda.is_available():
                self.bert_model = self.bert_model.cuda()
                logger.info("使用GPU进行BERT编码")

            logger.info(f"BERT模型初始化成功: {model_name}")

        except Exception as e:
            logger.error(f"BERT模型初始化失败: {str(e)}")
            self.tokenizer = None
            self.bert_model = None

    def initialize(self):
        """初始化模块"""
        logger.info("初始化新闻去重与聚类模块...")

        # 注册定时任务
        if self.scheduler:
            # 每天清理过期的新闻指纹
            self.schedule_task(
                function='clean_expired_fingerprints',
                params={},
                priority='low',
                schedule_time=datetime.now() + timedelta(days=1)
            )

        logger.info("新闻去重与聚类模块初始化完成")

    def get_status(self) -> Dict[str, Any]:
        """获取模块状态"""
        return {
            'module_name': self.module_name,
            'enabled': self.config.get('enabled', True),
            'last_update_time': self.last_update_time.isoformat() if self.last_update_time else None,
            'fingerprints_count': len(self.news_fingerprints),
            'clusters_count': len(self.clusters),
            'nlp_available': NLP_AVAILABLE
        }

    def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        try:
            # 检查上次更新时间
            time_since_last_update = datetime.now() - self.last_update_time

            if time_since_last_update > timedelta(days=2):
                status = 'warning'
                message = f'上次更新时间超过2天: {self.last_update_time.isoformat()}'
            else:
                status = 'healthy'
                message = f'上次更新时间: {self.last_update_time.isoformat()}'

            return {
                'status': status,
                'message': message,
                'last_update_time': self.last_update_time.isoformat(),
                'time_since_last_update': str(time_since_last_update),
                'fingerprints_count': len(self.news_fingerprints),
                'clusters_count': len(self.clusters),
                'nlp_available': NLP_AVAILABLE
            }

        except Exception as e:
            logger.error(f"健康检查失败: {str(e)}")
            return {
                'status': 'error',
                'message': f'健康检查失败: {str(e)}',
                'error': str(e)
            }

    def deduplicate_news(self, news_list: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        去除重复新闻

        Args:
            news_list: 新闻列表

        Returns:
            unique_news: 去重后的新闻列表
        """
        if not news_list:
            return []

        logger.info(f"开始去重，共{len(news_list)}条新闻")

        # 获取参数
        title_threshold = self.deduplication_params['title_similarity_threshold']
        content_threshold = self.deduplication_params['content_similarity_threshold']
        time_window = self.deduplication_params['time_window_hours']
        use_simhash = self.deduplication_params['use_simhash']

        # 当前时间
        current_time = datetime.now()

        # 去重结果
        unique_news = []
        duplicate_count = 0

        # 遍历新闻
        for news in news_list:
            # 生成指纹
            fingerprint = self._generate_news_fingerprint(news)

            # 检查是否是重复新闻
            is_duplicate = False

            # 如果使用SimHash
            if use_simhash:
                # 检查指纹是否已存在
                if fingerprint in self.news_fingerprints:
                    # 检查时间窗口
                    fp_time = datetime.fromisoformat(self.news_fingerprints[fingerprint]['timestamp'])
                    if (current_time - fp_time).total_seconds() / 3600 <= time_window:
                        is_duplicate = True
            else:
                # 使用相似度检查
                is_duplicate = self._check_similarity(news, title_threshold, content_threshold, time_window)

            # 如果不是重复新闻，添加到结果
            if not is_duplicate:
                unique_news.append(news)

                # 更新指纹
                self.news_fingerprints[fingerprint] = {
                    'id': news.get('id', ''),
                    'title': news.get('title', ''),
                    'timestamp': current_time.isoformat()
                }
            else:
                duplicate_count += 1

        # 保存指纹
        self.data_storage.save('news_clustering', 'news_fingerprints', self.news_fingerprints, StorageLevel.WARM)

        # 更新时间
        self.last_update_time = current_time

        logger.info(f"去重完成，共{len(unique_news)}条唯一新闻，{duplicate_count}条重复新闻")

        return unique_news

    def _generate_news_fingerprint(self, news: Dict[str, Any]) -> str:
        """
        生成新闻指纹

        Args:
            news: 新闻数据

        Returns:
            fingerprint: 新闻指纹
        """
        # 提取标题和内容
        title = news.get('title', '')
        content = news.get('content', '')

        # 如果使用SimHash
        if self.deduplication_params['use_simhash']:
            # 使用标题和内容的前100个字符生成指纹
            text = f"{title} {content[:100]}"

            # 分词
            words = jieba.lcut(text)

            # 过滤停用词
            words = [w for w in words if w not in self.stopwords]

            # 生成SimHash
            return self._simhash(' '.join(words))
        else:
            # 使用MD5生成指纹
            text = f"{title}_{content[:100]}"
            return hashlib.md5(text.encode('utf-8')).hexdigest()

    def _simhash(self, text: str, bits: int = 64) -> str:
        """
        计算文本的SimHash值

        Args:
            text: 文本
            bits: 位数

        Returns:
            simhash: SimHash值
        """
        # 特征向量
        features = defaultdict(int)

        # 分词
        words = jieba.lcut(text)

        # 计算特征
        for word in words:
            features[word] += 1

        # 计算哈希
        v = [0] * bits
        for f, w in features.items():
            h = bin(int(hashlib.md5(f.encode('utf-8')).hexdigest(), 16))[2:]
            h = h.zfill(bits)

            for i in range(bits):
                if h[i] == '1':
                    v[i] += w
                else:
                    v[i] -= w

        # 生成指纹
        fingerprint = ''
        for i in v:
            if i > 0:
                fingerprint += '1'
            else:
                fingerprint += '0'

        return fingerprint

    def _check_similarity(self, news: Dict[str, Any], title_threshold: float, content_threshold: float, time_window: int) -> bool:
        """
        检查新闻是否与已有新闻相似

        Args:
            news: 新闻数据
            title_threshold: 标题相似度阈值
            content_threshold: 内容相似度阈值
            time_window: 时间窗口（小时）

        Returns:
            is_similar: 是否相似
        """
        # 提取标题和内容
        title = news.get('title', '')
        content = news.get('content', '')

        # 当前时间
        current_time = datetime.now()

        # 遍历已有指纹
        for fingerprint, fp_data in self.news_fingerprints.items():
            # 检查时间窗口
            fp_time = datetime.fromisoformat(fp_data['timestamp'])
            if (current_time - fp_time).total_seconds() / 3600 > time_window:
                continue

            # 计算标题相似度
            title_similarity = self._calculate_text_similarity(title, fp_data['title'])

            # 如果标题相似度高于阈值，认为是重复新闻
            if title_similarity > title_threshold:
                return True

            # 如果有内容，计算内容相似度
            if 'content' in fp_data and content:
                content_similarity = self._calculate_text_similarity(content[:200], fp_data['content'][:200])

                # 如果内容相似度高于阈值，认为是重复新闻
                if content_similarity > content_threshold:
                    return True

        return False

    def _calculate_text_similarity(self, text1: str, text2: str) -> float:
        """
        计算文本相似度

        Args:
            text1: 文本1
            text2: 文本2

        Returns:
            similarity: 相似度
        """
        # 如果有BERT模型，使用BERT计算相似度
        if NLP_AVAILABLE and self.bert_model and self.tokenizer:
            try:
                return self._bert_similarity(text1, text2)
            except Exception as e:
                logger.error(f"BERT相似度计算失败: {str(e)}")

        # 使用TF-IDF计算相似度
        return self._tfidf_similarity(text1, text2)

    def _bert_similarity(self, text1: str, text2: str) -> float:
        """
        使用BERT计算文本相似度

        Args:
            text1: 文本1
            text2: 文本2

        Returns:
            similarity: 相似度
        """
        # 编码文本
        with torch.no_grad():
            # 编码文本1
            inputs1 = self.tokenizer(text1, return_tensors='pt', truncation=True, max_length=128)
            if torch.cuda.is_available():
                inputs1 = {k: v.cuda() for k, v in inputs1.items()}
            outputs1 = self.bert_model(**inputs1)
            embedding1 = outputs1.last_hidden_state.mean(dim=1).cpu().numpy()

            # 编码文本2
            inputs2 = self.tokenizer(text2, return_tensors='pt', truncation=True, max_length=128)
            if torch.cuda.is_available():
                inputs2 = {k: v.cuda() for k, v in inputs2.items()}
            outputs2 = self.bert_model(**inputs2)
            embedding2 = outputs2.last_hidden_state.mean(dim=1).cpu().numpy()

        # 计算余弦相似度
        similarity = cosine_similarity(embedding1, embedding2)[0][0]

        return similarity

    def _tfidf_similarity(self, text1: str, text2: str) -> float:
        """
        使用TF-IDF计算文本相似度

        Args:
            text1: 文本1
            text2: 文本2

        Returns:
            similarity: 相似度
        """
        # 分词
        words1 = ' '.join(jieba.lcut(text1))
        words2 = ' '.join(jieba.lcut(text2))

        # 向量化
        try:
            tfidf_matrix = self.vectorizer.fit_transform([words1, words2])

            # 计算余弦相似度
            similarity = cosine_similarity(tfidf_matrix[0:1], tfidf_matrix[1:2])[0][0]

            return similarity
        except Exception as e:
            logger.error(f"TF-IDF相似度计算失败: {str(e)}")

            # 如果向量化失败，使用简单的词重叠率
            set1 = set(words1.split())
            set2 = set(words2.split())

            if not set1 or not set2:
                return 0.0

            intersection = set1.intersection(set2)
            union = set1.union(set2)

            return len(intersection) / len(union)

    def cluster_news(self, news_list: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        对新闻进行聚类

        Args:
            news_list: 新闻列表

        Returns:
            clustered_news: 聚类后的新闻列表
        """
        if not news_list:
            return []

        logger.info(f"开始聚类，共{len(news_list)}条新闻")

        # 获取参数
        similarity_threshold = self.clustering_params['similarity_threshold']
        min_cluster_size = self.clustering_params['min_cluster_size']
        max_cluster_size = self.clustering_params['max_cluster_size']
        dbscan_eps = self.clustering_params['dbscan_eps']
        dbscan_min_samples = self.clustering_params['dbscan_min_samples']

        # 提取文本
        texts = []
        for news in news_list:
            title = news.get('title', '')
            content = news.get('content', '')
            text = f"{title} {content[:200]}"  # 使用标题和内容前200个字符
            texts.append(text)

        # 计算文本向量
        vectors = self._vectorize_texts(texts)

        # 使用DBSCAN聚类
        clustering = DBSCAN(eps=dbscan_eps, min_samples=dbscan_min_samples, metric='cosine').fit(vectors)

        # 获取聚类标签
        labels = clustering.labels_

        # 创建聚类
        clusters = defaultdict(list)
        for i, label in enumerate(labels):
            if label != -1:  # -1表示噪声点
                clusters[label].append(i)

        # 过滤聚类
        valid_clusters = {}
        for label, indices in clusters.items():
            if min_cluster_size <= len(indices) <= max_cluster_size:
                valid_clusters[label] = indices

        # 更新聚类结果
        self.clusters = []
        for label, indices in valid_clusters.items():
            cluster_news = [news_list[i] for i in indices]

            # 提取关键词
            cluster_text = ' '.join([texts[i] for i in indices])
            keywords = jieba.analyse.extract_tags(cluster_text, topK=5)

            # 创建聚类
            cluster = {
                'id': f"cluster_{label}_{datetime.now().strftime('%Y%m%d%H%M%S')}",
                'size': len(indices),
                'keywords': keywords,
                'news': cluster_news,
                'created_at': datetime.now().isoformat()
            }

            self.clusters.append(cluster)

        # 添加聚类信息到新闻
        clustered_news = []
        for i, news in enumerate(news_list):
            news_copy = news.copy()

            # 查找新闻所属的聚类
            for j, cluster in enumerate(self.clusters):
                if any(news_list.index(n) == i for n in cluster['news']):
                    news_copy['cluster_id'] = cluster['id']
                    news_copy['cluster_keywords'] = cluster['keywords']
                    news_copy['cluster_size'] = cluster['size']
                    break

            clustered_news.append(news_copy)

        logger.info(f"聚类完成，共{len(self.clusters)}个聚类")

        return clustered_news

    def _vectorize_texts(self, texts: List[str]) -> np.ndarray:
        """
        将文本向量化

        Args:
            texts: 文本列表

        Returns:
            vectors: 向量矩阵
        """
        # 如果有BERT模型，使用BERT向量化
        if NLP_AVAILABLE and self.bert_model and self.tokenizer:
            try:
                return self._bert_vectorize(texts)
            except Exception as e:
                logger.error(f"BERT向量化失败: {str(e)}")

        # 使用TF-IDF向量化
        return self._tfidf_vectorize(texts)

    def _bert_vectorize(self, texts: List[str]) -> np.ndarray:
        """
        使用BERT向量化文本

        Args:
            texts: 文本列表

        Returns:
            vectors: 向量矩阵
        """
        embeddings = []

        with torch.no_grad():
            for text in texts:
                # 编码文本
                inputs = self.tokenizer(text, return_tensors='pt', truncation=True, max_length=128)
                if torch.cuda.is_available():
                    inputs = {k: v.cuda() for k, v in inputs.items()}

                # 获取BERT输出
                outputs = self.bert_model(**inputs)

                # 使用最后一层的平均值作为文本表示
                embedding = outputs.last_hidden_state.mean(dim=1).cpu().numpy()
                embeddings.append(embedding[0])

        return np.array(embeddings)

    def _tfidf_vectorize(self, texts: List[str]) -> np.ndarray:
        """
        使用TF-IDF向量化文本

        Args:
            texts: 文本列表

        Returns:
            vectors: 向量矩阵
        """
        # 分词
        processed_texts = []
        for text in texts:
            words = jieba.lcut(text)
            words = [w for w in words if w not in self.stopwords]
            processed_texts.append(' '.join(words))

        # 向量化
        try:
            tfidf_matrix = self.vectorizer.fit_transform(processed_texts)
            return tfidf_matrix.toarray()
        except Exception as e:
            logger.error(f"TF-IDF向量化失败: {str(e)}")

            # 如果向量化失败，使用简单的词袋模型
            all_words = set()
            for text in processed_texts:
                all_words.update(text.split())

            word_to_idx = {word: i for i, word in enumerate(all_words)}

            vectors = np.zeros((len(processed_texts), len(word_to_idx)))
            for i, text in enumerate(processed_texts):
                for word in text.split():
                    if word in word_to_idx:
                        vectors[i, word_to_idx[word]] = 1

            return vectors

    def clean_expired_fingerprints(self, **kwargs):
        """清理过期的新闻指纹"""
        try:
            logger.info("开始清理过期的新闻指纹...")

            # 当前时间
            current_time = datetime.now()

            # 过期时间（7天）
            expiry_time = current_time - timedelta(days=7)

            # 清理过期指纹
            expired_count = 0
            new_fingerprints = {}

            for fingerprint, data in self.news_fingerprints.items():
                fp_time = datetime.fromisoformat(data['timestamp'])
                if fp_time > expiry_time:
                    new_fingerprints[fingerprint] = data
                else:
                    expired_count += 1

            # 更新指纹
            self.news_fingerprints = new_fingerprints

            # 保存指纹
            self.data_storage.save('news_clustering', 'news_fingerprints', self.news_fingerprints, StorageLevel.WARM)

            # 更新时间
            self.last_update_time = current_time

            logger.info(f"清理完成，共清理{expired_count}个过期指纹，剩余{len(self.news_fingerprints)}个")

            return {
                'status': 'success',
                'message': f'清理完成，共清理{expired_count}个过期指纹，剩余{len(self.news_fingerprints)}个',
                'expired_count': expired_count,
                'remaining_count': len(self.news_fingerprints)
            }

        except Exception as e:
            logger.error(f"清理过期指纹失败: {str(e)}")
            return {
                'status': 'error',
                'message': f'清理过期指纹失败: {str(e)}',
                'error': str(e)
            }

    def get_clusters(self, keywords: List[str] = None, min_size: int = None, max_size: int = None) -> List[Dict[str, Any]]:
        """
        获取新闻聚类

        Args:
            keywords: 关键词列表，用于过滤聚类
            min_size: 最小聚类大小
            max_size: 最大聚类大小

        Returns:
            clusters: 聚类列表
        """
        # 过滤聚类
        filtered_clusters = self.clusters

        # 按关键词过滤
        if keywords:
            filtered_clusters = []
            for cluster in self.clusters:
                # 检查关键词是否在聚类关键词中
                if any(kw in cluster['keywords'] for kw in keywords):
                    filtered_clusters.append(cluster)

        # 按大小过滤
        if min_size is not None:
            filtered_clusters = [c for c in filtered_clusters if c['size'] >= min_size]

        if max_size is not None:
            filtered_clusters = [c for c in filtered_clusters if c['size'] <= max_size]

        # 按大小排序
        sorted_clusters = sorted(filtered_clusters, key=lambda x: x['size'], reverse=True)

        return sorted_clusters
