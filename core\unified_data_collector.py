"""
统一数据收集器
负责调用所有相关API获取并存储数据
"""

import os
import json
import asyncio
import aiohttp
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import logging
import akshare as ak
import pandas as pd

from utils.logger import logger
from core.data_storage import DataStorage
from core.enhanced_keyword_extractor import EnhancedKeywordExtractor

class UnifiedDataCollector:
    """统一数据收集器类"""
    
    def __init__(self):
        """初始化数据收集器"""
        self.data_storage = DataStorage()
        self.keyword_extractor = EnhancedKeywordExtractor()
        
        # 数据源配置
        self.data_sources = {
            "新闻数据": {
                "stock_news_em": "东方财富财经新闻",
                "news_cctv": "央视财经新闻", 
                "stock_news_sina": "新浪财经新闻"
            },
            "资金流数据": {
                "stock_hsgt_fund_flow_summary_em": "北向资金流向",
                "stock_sector_fund_flow_rank_em": "行业资金流向",
                "stock_individual_fund_flow_rank_em": "个股资金流向"
            },
            "市场数据": {
                "stock_zh_a_spot_em": "A股实时行情",
                "stock_zh_index_daily": "指数日线数据",
                "stock_board_industry_name_em": "行业板块数据"
            },
            "政策数据": {
                "web_scraping": "政府网站政策抓取"
            }
        }
        
        logger.info("统一数据收集器初始化完成")
    
    async def collect_all_data(self):
        """收集所有数据"""
        try:
            logger.info("开始收集所有数据...")
            
            # 并行收集不同类型的数据
            tasks = [
                self.collect_news_data(),
                self.collect_fund_flow_data(),
                self.collect_market_data(),
                self.collect_policy_data()
            ]
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 处理结果
            success_count = 0
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    logger.error(f"数据收集任务{i}失败: {str(result)}")
                else:
                    success_count += 1
            
            logger.info(f"数据收集完成，成功{success_count}/{len(tasks)}个任务")
            return success_count == len(tasks)
            
        except Exception as e:
            logger.error(f"数据收集失败: {str(e)}")
            return False
    
    async def collect_news_data(self):
        """收集新闻数据"""
        try:
            logger.info("开始收集新闻数据...")
            
            news_data = []
            
            # 1. 东方财富财经新闻
            try:
                em_news = ak.stock_news_em()
                if not em_news.empty:
                    for _, row in em_news.head(50).iterrows():  # 取前50条
                        news_item = {
                            'source': '东方财富',
                            'title': row.get('新闻标题', ''),
                            'content': row.get('新闻内容', ''),
                            'publish_time': row.get('发布时间', ''),
                            'url': row.get('新闻链接', ''),
                            'collect_time': datetime.now().isoformat()
                        }
                        
                        # 提取关键词
                        keywords = self.keyword_extractor.extract_keywords(
                            news_item['content'], 
                            news_item['title'],
                            news_item['publish_time']
                        )
                        news_item['keywords'] = keywords
                        news_item['keyword_summary'] = self.keyword_extractor.get_keyword_summary(keywords)
                        
                        news_data.append(news_item)
                        
                logger.info(f"东方财富新闻收集完成: {len(news_data)}条")
            except Exception as e:
                logger.warning(f"东方财富新闻收集失败: {str(e)}")
            
            # 2. 央视财经新闻
            try:
                cctv_news = ak.news_cctv()
                if not cctv_news.empty:
                    for _, row in cctv_news.head(30).iterrows():  # 取前30条
                        news_item = {
                            'source': '央视财经',
                            'title': row.get('title', ''),
                            'content': row.get('content', ''),
                            'publish_time': row.get('time', ''),
                            'url': row.get('link', ''),
                            'collect_time': datetime.now().isoformat()
                        }
                        
                        # 提取关键词
                        keywords = self.keyword_extractor.extract_keywords(
                            news_item['content'], 
                            news_item['title'],
                            news_item['publish_time']
                        )
                        news_item['keywords'] = keywords
                        news_item['keyword_summary'] = self.keyword_extractor.get_keyword_summary(keywords)
                        
                        news_data.append(news_item)
                        
                logger.info(f"央视财经新闻收集完成: 总计{len(news_data)}条")
            except Exception as e:
                logger.warning(f"央视财经新闻收集失败: {str(e)}")
            
            # 存储新闻数据
            if news_data:
                for news_item in news_data:
                    self.data_storage.store_data('news', news_item)
                logger.info(f"新闻数据存储完成: {len(news_data)}条")
            
            return news_data
            
        except Exception as e:
            logger.error(f"新闻数据收集失败: {str(e)}")
            return []
    
    async def collect_fund_flow_data(self):
        """收集资金流数据"""
        try:
            logger.info("开始收集资金流数据...")
            
            fund_flow_data = {}
            
            # 1. 北向资金流向
            try:
                hsgt_flow = ak.stock_hsgt_fund_flow_summary_em()
                if not hsgt_flow.empty:
                    latest_flow = hsgt_flow.iloc[-1]
                    fund_flow_data['northbound_flow'] = {
                        'date': latest_flow.get('日期', ''),
                        'net_flow': latest_flow.get('当日净流入', 0),
                        'total_flow': latest_flow.get('当日成交净买额', 0),
                        'collect_time': datetime.now().isoformat()
                    }
                logger.info("北向资金数据收集完成")
            except Exception as e:
                logger.warning(f"北向资金数据收集失败: {str(e)}")
            
            # 2. 行业资金流向
            try:
                sector_flow = ak.stock_sector_fund_flow_rank_em(indicator="今日")
                if not sector_flow.empty:
                    sector_data = []
                    for _, row in sector_flow.head(20).iterrows():
                        sector_data.append({
                            'sector_name': row.get('名称', ''),
                            'net_flow': row.get('今日净流入', 0),
                            'net_flow_rate': row.get('今日净流入率', 0),
                            'main_net_flow': row.get('今日主力净流入', 0)
                        })
                    fund_flow_data['sector_flows'] = sector_data
                logger.info("行业资金流数据收集完成")
            except Exception as e:
                logger.warning(f"行业资金流数据收集失败: {str(e)}")
            
            # 3. 个股资金流向（前50只）
            try:
                stock_flow = ak.stock_individual_fund_flow_rank_em(indicator="今日")
                if not stock_flow.empty:
                    stock_data = []
                    for _, row in stock_flow.head(50).iterrows():
                        stock_data.append({
                            'stock_code': row.get('代码', ''),
                            'stock_name': row.get('名称', ''),
                            'net_flow': row.get('今日净流入', 0),
                            'net_flow_rate': row.get('今日净流入率', 0),
                            'main_net_flow': row.get('今日主力净流入', 0),
                            'price': row.get('收盘价', 0),
                            'change_pct': row.get('涨跌幅', 0)
                        })
                    fund_flow_data['stock_flows'] = stock_data
                logger.info("个股资金流数据收集完成")
            except Exception as e:
                logger.warning(f"个股资金流数据收集失败: {str(e)}")
            
            # 存储资金流数据
            if fund_flow_data:
                fund_flow_data['collect_time'] = datetime.now().isoformat()
                self.data_storage.store_data('fund_flow', fund_flow_data)
                logger.info("资金流数据存储完成")
            
            return fund_flow_data
            
        except Exception as e:
            logger.error(f"资金流数据收集失败: {str(e)}")
            return {}
    
    async def collect_market_data(self):
        """收集市场数据"""
        try:
            logger.info("开始收集市场数据...")
            
            market_data = {}
            
            # 1. A股实时行情（主要指数）
            try:
                indices = ['sh000001', 'sz399001', 'sz399006']  # 上证指数、深证成指、创业板指
                index_data = []
                
                for index_code in indices:
                    try:
                        index_daily = ak.stock_zh_index_daily(symbol=index_code)
                        if not index_daily.empty:
                            latest = index_daily.iloc[-1]
                            index_data.append({
                                'index_code': index_code,
                                'date': latest.get('date', ''),
                                'open': latest.get('open', 0),
                                'close': latest.get('close', 0),
                                'high': latest.get('high', 0),
                                'low': latest.get('low', 0),
                                'volume': latest.get('volume', 0)
                            })
                    except Exception as e:
                        logger.warning(f"指数{index_code}数据获取失败: {str(e)}")
                
                market_data['indices'] = index_data
                logger.info(f"指数数据收集完成: {len(index_data)}个指数")
            except Exception as e:
                logger.warning(f"指数数据收集失败: {str(e)}")
            
            # 2. 行业板块数据
            try:
                industry_board = ak.stock_board_industry_name_em()
                if not industry_board.empty:
                    industry_data = []
                    for _, row in industry_board.head(30).iterrows():
                        industry_data.append({
                            'industry_name': row.get('板块名称', ''),
                            'industry_code': row.get('板块代码', ''),
                            'stock_count': row.get('包含股票数', 0),
                            'avg_price': row.get('平均价格', 0),
                            'change_pct': row.get('涨跌幅', 0)
                        })
                    market_data['industries'] = industry_data
                logger.info("行业板块数据收集完成")
            except Exception as e:
                logger.warning(f"行业板块数据收集失败: {str(e)}")
            
            # 存储市场数据
            if market_data:
                market_data['collect_time'] = datetime.now().isoformat()
                self.data_storage.store_data('market', market_data)
                logger.info("市场数据存储完成")
            
            return market_data
            
        except Exception as e:
            logger.error(f"市场数据收集失败: {str(e)}")
            return {}
    
    async def collect_policy_data(self):
        """收集政策数据"""
        try:
            logger.info("开始收集政策数据...")
            
            # 这里应该实现政策网站的爬虫
            # 由于网站爬虫比较复杂，暂时返回模拟数据
            policy_data = {
                'source': '政府网站',
                'policies': [],
                'collect_time': datetime.now().isoformat()
            }
            
            # TODO: 实现政策网站爬虫
            logger.info("政策数据收集完成（暂时为空）")
            
            return policy_data
            
        except Exception as e:
            logger.error(f"政策数据收集失败: {str(e)}")
            return {}
    
    def get_data_collection_status(self) -> Dict[str, Any]:
        """获取数据收集状态"""
        try:
            status = {
                'last_collection_time': datetime.now().isoformat(),
                'data_sources': self.data_sources,
                'storage_status': self.data_storage.get_storage_status(),
                'collection_summary': {
                    'news_count': len(self.data_storage.get_latest_data('news', limit=100)),
                    'fund_flow_records': len(self.data_storage.get_latest_data('fund_flow', limit=10)),
                    'market_records': len(self.data_storage.get_latest_data('market', limit=10))
                }
            }
            return status
        except Exception as e:
            logger.error(f"获取数据收集状态失败: {str(e)}")
            return {}
    
    def run_collection_sync(self):
        """同步运行数据收集"""
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            result = loop.run_until_complete(self.collect_all_data())
            loop.close()
            return result
        except Exception as e:
            logger.error(f"同步数据收集失败: {str(e)}")
            return False
