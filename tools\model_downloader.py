"""
模型下载工具
从HuggingFace镜像下载FinBERT等模型到本地
"""

import os
import sys
import requests
import json
from pathlib import Path
from typing import Dict, List, Any
import logging

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.logger import logger

class ModelDownloader:
    """模型下载器"""
    
    def __init__(self, base_url: str = "https://hf-mirror.com"):
        self.base_url = base_url
        self.models_dir = Path("models")
        self.models_dir.mkdir(exist_ok=True)
        
        # 配置日志
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
    def download_model(self, model_name: str, local_path: str) -> bool:
        """
        下载模型到本地
        
        Args:
            model_name: 模型名称，如 "ProsusAI/finbert"
            local_path: 本地保存路径
            
        Returns:
            是否下载成功
        """
        try:
            self.logger.info(f"开始下载模型: {model_name}")
            
            # 创建本地目录
            local_dir = Path(local_path)
            local_dir.mkdir(parents=True, exist_ok=True)
            
            # 需要下载的文件列表
            files_to_download = [
                "config.json",
                "pytorch_model.bin",
                "tokenizer.json", 
                "tokenizer_config.json",
                "vocab.txt",
                "special_tokens_map.json"
            ]
            
            # 下载每个文件
            success_count = 0
            for filename in files_to_download:
                file_url = f"{self.base_url}/{model_name}/resolve/main/{filename}"
                local_file_path = local_dir / filename
                
                if self.download_file(file_url, local_file_path):
                    success_count += 1
                    self.logger.info(f"✅ 下载成功: {filename}")
                else:
                    self.logger.warning(f"⚠️ 下载失败: {filename}")
            
            # 检查是否下载了关键文件
            required_files = ["config.json", "pytorch_model.bin", "tokenizer.json"]
            required_success = all((local_dir / f).exists() for f in required_files)
            
            if required_success:
                self.logger.info(f"🎉 模型下载完成: {model_name} -> {local_path}")
                return True
            else:
                self.logger.error(f"❌ 模型下载不完整: {model_name}")
                return False
                
        except Exception as e:
            self.logger.error(f"模型下载失败: {model_name}, 错误: {str(e)}")
            return False
    
    def download_file(self, url: str, local_path: Path, chunk_size: int = 8192) -> bool:
        """
        下载单个文件
        
        Args:
            url: 文件URL
            local_path: 本地保存路径
            chunk_size: 下载块大小
            
        Returns:
            是否下载成功
        """
        try:
            response = requests.get(url, stream=True, timeout=30)
            response.raise_for_status()
            
            total_size = int(response.headers.get('content-length', 0))
            downloaded_size = 0
            
            with open(local_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=chunk_size):
                    if chunk:
                        f.write(chunk)
                        downloaded_size += len(chunk)
                        
                        # 显示下载进度
                        if total_size > 0:
                            progress = (downloaded_size / total_size) * 100
                            print(f"\r  下载进度: {progress:.1f}% ({downloaded_size}/{total_size} bytes)", end='')
            
            print()  # 换行
            return True
            
        except requests.exceptions.RequestException as e:
            self.logger.error(f"下载文件失败: {url}, 错误: {str(e)}")
            return False
        except Exception as e:
            self.logger.error(f"保存文件失败: {local_path}, 错误: {str(e)}")
            return False
    
    def download_finbert_models(self) -> Dict[str, bool]:
        """下载FinBERT相关模型"""
        models_to_download = {
            "ProsusAI/finbert": "models/finbert",
            "mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis": "models/financial-sentiment"
        }
        
        results = {}
        
        for model_name, local_path in models_to_download.items():
            self.logger.info(f"\n{'='*60}")
            self.logger.info(f"下载模型: {model_name}")
            self.logger.info(f"保存路径: {local_path}")
            self.logger.info(f"{'='*60}")
            
            success = self.download_model(model_name, local_path)
            results[model_name] = success
            
            if success:
                self.logger.info(f"✅ {model_name} 下载成功")
            else:
                self.logger.error(f"❌ {model_name} 下载失败")
        
        return results
    
    def verify_models(self) -> Dict[str, bool]:
        """验证已下载的模型"""
        models_to_verify = {
            "finbert": "models/finbert",
            "financial-sentiment": "models/financial-sentiment"
        }
        
        verification_results = {}
        
        self.logger.info("\n🔍 验证已下载的模型...")
        
        for model_name, model_path in models_to_verify.items():
            model_dir = Path(model_path)
            
            if not model_dir.exists():
                verification_results[model_name] = False
                self.logger.error(f"❌ {model_name}: 目录不存在 - {model_path}")
                continue
            
            # 检查必要文件
            required_files = ["config.json", "pytorch_model.bin", "tokenizer.json"]
            missing_files = []
            
            for file_name in required_files:
                file_path = model_dir / file_name
                if not file_path.exists():
                    missing_files.append(file_name)
                else:
                    file_size = file_path.stat().st_size
                    self.logger.info(f"  ✅ {file_name}: {file_size/1024/1024:.1f}MB")
            
            if missing_files:
                verification_results[model_name] = False
                self.logger.error(f"❌ {model_name}: 缺少文件 - {missing_files}")
            else:
                verification_results[model_name] = True
                self.logger.info(f"✅ {model_name}: 验证通过")
        
        return verification_results
    
    def test_model_loading(self) -> Dict[str, bool]:
        """测试模型加载"""
        test_results = {}
        
        self.logger.info("\n🧪 测试模型加载...")
        
        try:
            from transformers import AutoTokenizer, AutoModelForSequenceClassification
            
            models_to_test = {
                "finbert": "models/finbert",
                "financial-sentiment": "models/financial-sentiment"
            }
            
            for model_name, model_path in models_to_test.items():
                try:
                    self.logger.info(f"测试加载: {model_name}")
                    
                    # 加载tokenizer
                    tokenizer = AutoTokenizer.from_pretrained(model_path)
                    self.logger.info(f"  ✅ Tokenizer加载成功")
                    
                    # 加载模型
                    model = AutoModelForSequenceClassification.from_pretrained(model_path)
                    self.logger.info(f"  ✅ Model加载成功")
                    
                    # 测试推理
                    test_text = "The company reported strong quarterly earnings."
                    inputs = tokenizer(test_text, return_tensors="pt", truncation=True, padding=True)
                    outputs = model(**inputs)
                    self.logger.info(f"  ✅ 推理测试成功")
                    
                    test_results[model_name] = True
                    self.logger.info(f"✅ {model_name}: 加载测试通过")
                    
                except Exception as e:
                    test_results[model_name] = False
                    self.logger.error(f"❌ {model_name}: 加载测试失败 - {str(e)}")
            
        except ImportError as e:
            self.logger.error(f"❌ 无法导入transformers库: {str(e)}")
            self.logger.info("请安装: pip install transformers torch")
            
        return test_results

def main():
    """主函数"""
    print("🤖 FinBERT模型下载工具")
    print("=" * 60)
    
    downloader = ModelDownloader()
    
    # 1. 下载模型
    print("\n📥 开始下载模型...")
    download_results = downloader.download_finbert_models()
    
    # 2. 验证模型
    print("\n🔍 验证模型完整性...")
    verification_results = downloader.verify_models()
    
    # 3. 测试模型加载
    print("\n🧪 测试模型加载...")
    test_results = downloader.test_model_loading()
    
    # 4. 生成报告
    print("\n" + "=" * 60)
    print("📊 下载和验证报告")
    print("=" * 60)
    
    all_success = True
    
    for model_name in ["ProsusAI/finbert", "mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis"]:
        download_ok = download_results.get(model_name, False)
        
        # 获取简化的模型名称用于验证和测试
        simple_name = "finbert" if "finbert" in model_name else "financial-sentiment"
        verify_ok = verification_results.get(simple_name, False)
        test_ok = test_results.get(simple_name, False)
        
        status = "✅ 成功" if (download_ok and verify_ok and test_ok) else "❌ 失败"
        print(f"{model_name}: {status}")
        print(f"  下载: {'✅' if download_ok else '❌'}")
        print(f"  验证: {'✅' if verify_ok else '❌'}")
        print(f"  测试: {'✅' if test_ok else '❌'}")
        print()
        
        if not (download_ok and verify_ok and test_ok):
            all_success = False
    
    if all_success:
        print("🎉 所有模型下载和验证成功！")
        print("💡 现在可以使用FinBERT引擎进行情感分析了")
    else:
        print("⚠️ 部分模型下载或验证失败")
        print("💡 请检查网络连接或重新运行下载")
    
    print("=" * 60)
    
    return all_success

if __name__ == "__main__":
    main()
