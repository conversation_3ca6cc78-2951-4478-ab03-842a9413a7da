"""
新闻监控模块测试

测试新闻监控模块的基本功能
"""

import os
import sys
import unittest
import json
import logging
from datetime import datetime
import pandas as pd

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('test_news_monitor')

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入被测试的模块
from data_sources.unified_news_monitor import UnifiedNewsMonitor
from core.data_storage import DataStorage, StorageLevel

class TestNewsMonitor(unittest.TestCase):
    """新闻监控模块测试类"""
    
    @classmethod
    def setUpClass(cls):
        """测试前的准备工作"""
        logger.info("初始化新闻监控模块测试...")
        
        # 创建测试目录
        cls.test_dir = os.path.join('data', 'test_news_monitor')
        os.makedirs(cls.test_dir, exist_ok=True)
        
        # 创建配置目录
        os.makedirs('config', exist_ok=True)
        
        # 创建测试配置文件
        cls.config_path = os.path.join('config', 'news_monitor_config.json')
        with open(cls.config_path, 'w', encoding='utf-8') as f:
            json.dump({
                'module_name': 'news_monitor',
                'enabled': True,
                'log_level': 'INFO',
                'data_sources': {
                    'financial_breakfast': {
                        'enabled': True,
                        'max_items': 20
                    },
                    'global_news': {
                        'enabled': True,
                        'max_items': 20
                    },
                    'sina_finance': {
                        'enabled': True,
                        'max_items': 20
                    },
                    'eastmoney_news': {
                        'enabled': True,
                        'max_items': 20
                    }
                },
                'news_processing': {
                    'deduplication': {
                        'enabled': True,
                        'similarity_threshold': 0.8
                    },
                    'clustering': {
                        'enabled': True,
                        'max_clusters': 10,
                        'similarity_threshold': 0.7
                    }
                },
                'sentiment_analysis': {
                    'model': 'finbert',
                    'threshold': 0.6
                },
                'monitoring': {
                    'interval_minutes': 5,
                    'hot_topic_threshold': 3
                }
            }, f, indent=4)
        
        # 创建测试数据存储
        cls.data_storage = DataStorage()
        
        # 创建测试新闻数据
        cls.test_news = pd.DataFrame({
            'title': [
                '美联储宣布维持利率不变',
                '中国央行下调存款准备金率0.5个百分点',
                '科技股大幅上涨，纳斯达克指数创新高',
                '欧洲央行暗示可能在年内降息',
                '国内油价迎来年内第三次上调'
            ],
            'source': ['东方财富', '新浪财经', '东方财富', '财联社', '新浪财经'],
            'publish_time': [
                '2025-05-15 08:30:00',
                '2025-05-15 09:15:00',
                '2025-05-15 10:00:00',
                '2025-05-15 11:30:00',
                '2025-05-15 14:00:00'
            ],
            'content': [
                '美联储在最新货币政策会议上宣布维持联邦基金利率目标区间在5.25%-5.50%不变，符合市场预期。美联储主席鲍威尔表示，通胀正在降温，但仍需要更多证据确认通胀持续回落至2%的目标。',
                '中国人民银行宣布，决定于2025年5月20日下调金融机构存款准备金率0.5个百分点（不含已执行5%存款准备金率的金融机构）。此次降准是全面降准，共计释放长期资金约1.2万亿元。',
                '周三美股收盘，科技股领涨，纳斯达克指数上涨1.5%，创历史新高。苹果、微软、英伟达等科技巨头股价均创新高，带动大盘走强。分析师认为，AI热潮和企业业绩超预期是推动科技股上涨的主要因素。',
                '欧洲央行行长拉加德在最新讲话中暗示，如果通胀继续降温，欧洲央行可能在年内开始降息周期。市场预期首次降息可能在今年9月。欧元区通胀已从高点显著回落，但仍高于2%的目标水平。',
                '国内成品油价格迎来年内第三次上调。根据国家发改委消息，自2025年5月15日24时起，国内汽油价格上调320元/吨，柴油价格上调310元/吨。折合升价，92号汽油上调0.25元，95号汽油上调0.27元。'
            ],
            'url': [
                'https://finance.eastmoney.com/news/1345,202505151234567890.html',
                'https://finance.sina.com.cn/china/gncj/2025-05-15/doc-imyzkvft1234567.shtml',
                'https://finance.eastmoney.com/news/1345,202505151234567891.html',
                'https://www.cls.cn/detail/1234567',
                'https://finance.sina.com.cn/china/gncj/2025-05-15/doc-imyzkvft1234568.shtml'
            ]
        })
        
        # 保存测试新闻数据
        cls.data_storage.save('news_data', 'test_news', cls.test_news.to_dict('records'), StorageLevel.WARM)
        
        logger.info("新闻监控模块测试初始化完成")
    
    @classmethod
    def tearDownClass(cls):
        """测试后的清理工作"""
        logger.info("清理新闻监控模块测试...")
        
        # 删除测试配置文件
        if os.path.exists(cls.config_path):
            os.remove(cls.config_path)
        
        # 删除测试目录
        import shutil
        if os.path.exists(cls.test_dir):
            shutil.rmtree(cls.test_dir)
        
        logger.info("新闻监控模块测试清理完成")
    
    def test_init(self):
        """测试初始化"""
        logger.info("测试新闻监控模块初始化...")
        
        # 创建新闻监控模块
        news_monitor = UnifiedNewsMonitor(config_path=self.config_path, data_storage=self.data_storage)
        
        # 验证配置
        self.assertIsNotNone(news_monitor.config)
        self.assertEqual(news_monitor.config.get('module_name'), 'news_monitor')
        self.assertEqual(news_monitor.module_name, 'news_monitor')
        
        logger.info("新闻监控模块初始化测试通过")
    
    def test_process_news(self):
        """测试处理新闻"""
        logger.info("测试处理新闻...")
        
        # 创建新闻监控模块
        news_monitor = UnifiedNewsMonitor(config_path=self.config_path, data_storage=self.data_storage)
        
        # 获取测试新闻数据
        test_news = self.data_storage.load('news_data', 'test_news', level=StorageLevel.WARM)
        
        # 处理新闻
        processed_news = news_monitor.process_news(test_news)
        
        # 验证处理结果
        self.assertIsNotNone(processed_news)
        self.assertTrue(len(processed_news) > 0)
        
        # 验证处理后的新闻包含必要字段
        for news in processed_news:
            self.assertIn('title', news)
            self.assertIn('source', news)
            self.assertIn('publish_time', news)
            self.assertIn('content', news)
            self.assertIn('url', news)
            self.assertIn('processed_at', news)
            self.assertIn('sentiment', news)
            
        logger.info("处理新闻测试通过")
    
    def test_analyze_news_sentiment(self):
        """测试分析新闻情感"""
        logger.info("测试分析新闻情感...")
        
        # 创建新闻监控模块
        news_monitor = UnifiedNewsMonitor(config_path=self.config_path, data_storage=self.data_storage)
        
        # 测试新闻
        news = {
            'title': '中国央行下调存款准备金率0.5个百分点',
            'content': '中国人民银行宣布，决定于2025年5月20日下调金融机构存款准备金率0.5个百分点（不含已执行5%存款准备金率的金融机构）。此次降准是全面降准，共计释放长期资金约1.2万亿元。'
        }
        
        # 分析新闻情感
        result = news_monitor._analyze_sentiment(news)
        
        # 验证分析结果
        self.assertIsNotNone(result)
        self.assertIn('score', result)
        self.assertIn('label', result)
        self.assertTrue(0 <= result['score'] <= 1)
        self.assertIn(result['label'], ['positive', 'negative', 'neutral'])
        
        logger.info("分析新闻情感测试通过")
    
    def test_extract_keywords(self):
        """测试提取关键词"""
        logger.info("测试提取关键词...")
        
        # 创建新闻监控模块
        news_monitor = UnifiedNewsMonitor(config_path=self.config_path, data_storage=self.data_storage)
        
        # 测试新闻
        news = {
            'title': '中国央行下调存款准备金率0.5个百分点',
            'content': '中国人民银行宣布，决定于2025年5月20日下调金融机构存款准备金率0.5个百分点（不含已执行5%存款准备金率的金融机构）。此次降准是全面降准，共计释放长期资金约1.2万亿元。'
        }
        
        # 提取关键词
        keywords = news_monitor._extract_keywords(news)
        
        # 验证提取结果
        self.assertIsNotNone(keywords)
        self.assertTrue(len(keywords) > 0)
        
        # 检查是否包含相关关键词
        keyword_list = [k['word'] for k in keywords]
        self.assertTrue(any('央行' in kw for kw in keyword_list) or 
                       any('人民银行' in kw for kw in keyword_list))
        self.assertTrue(any('存款准备金' in kw for kw in keyword_list) or 
                       any('降准' in kw for kw in keyword_list))
        
        logger.info("提取关键词测试通过")
    
    def test_detect_hot_topics(self):
        """测试检测热点话题"""
        logger.info("测试检测热点话题...")
        
        # 创建新闻监控模块
        news_monitor = UnifiedNewsMonitor(config_path=self.config_path, data_storage=self.data_storage)
        
        # 获取测试新闻数据
        test_news = self.data_storage.load('news_data', 'test_news', level=StorageLevel.WARM)
        
        # 处理新闻
        processed_news = news_monitor.process_news(test_news)
        
        # 保存处理后的新闻
        news_monitor.processed_news = processed_news
        
        # 检测热点话题
        hot_topics = news_monitor._detect_hot_topics()
        
        # 验证检测结果
        self.assertIsNotNone(hot_topics)
        self.assertTrue(len(hot_topics) > 0)
        
        # 验证热点话题包含必要字段
        for topic in hot_topics:
            self.assertIn('topic', topic)
            self.assertIn('keywords', topic)
            self.assertIn('news_count', topic)
            self.assertIn('sentiment', topic)
            self.assertIn('detected_at', topic)
        
        logger.info("检测热点话题测试通过")
    
    def test_fetch_news(self):
        """测试获取新闻"""
        logger.info("测试获取新闻...")
        
        # 创建新闻监控模块
        news_monitor = UnifiedNewsMonitor(config_path=self.config_path, data_storage=self.data_storage)
        
        # 重写_fetch_news_from_all_sources方法，使用测试数据
        def mock_fetch_news_from_all_sources(self):
            return self.data_storage.load('news_data', 'test_news', level=StorageLevel.WARM)
        
        # 保存原始方法
        original_fetch_news = UnifiedNewsMonitor._fetch_news_from_all_sources
        
        # 替换为模拟方法
        UnifiedNewsMonitor._fetch_news_from_all_sources = mock_fetch_news_from_all_sources
        
        try:
            # 获取新闻
            result = news_monitor.fetch_news()
            
            # 验证结果
            self.assertIsNotNone(result)
            self.assertEqual(result['status'], 'success')
            self.assertTrue(result['count'] > 0)
            
            # 验证已处理的新闻
            self.assertTrue(len(news_monitor.processed_news) > 0)
            
        finally:
            # 恢复原始方法
            UnifiedNewsMonitor._fetch_news_from_all_sources = original_fetch_news
        
        logger.info("获取新闻测试通过")

if __name__ == '__main__':
    unittest.main()
