"""
数据存储示例

展示如何使用多级数据存储系统
"""

import os
import sys
import time
import json
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入数据存储
from core.data_storage import DataStorage, StorageLevel

def example_basic_usage():
    """基本使用示例"""
    print("\n=== 基本使用示例 ===")
    
    # 创建数据存储
    storage = DataStorage()
    
    # 保存字典数据
    data = {
        "name": "测试数据",
        "value": 123,
        "timestamp": datetime.now().isoformat(),
        "items": [1, 2, 3, 4, 5]
    }
    
    print(f"保存字典数据: {data}")
    success = storage.save("example", "test_dict", data)
    print(f"保存结果: {'成功' if success else '失败'}")
    
    # 加载字典数据
    loaded_data = storage.load("example", "test_dict")
    print(f"加载字典数据: {loaded_data}")
    
    # 保存DataFrame数据
    df = pd.DataFrame({
        "id": range(1, 6),
        "name": ["A", "B", "C", "D", "E"],
        "value": np.random.rand(5)
    })
    
    print(f"\n保存DataFrame数据:\n{df}")
    success = storage.save("example", "test_df", df)
    print(f"保存结果: {'成功' if success else '失败'}")
    
    # 加载DataFrame数据
    loaded_df = storage.load("example", "test_df")
    print(f"\n加载DataFrame数据:\n{loaded_df}")
    
    # 检查数据是否存在
    exists = storage.exists("example", "test_dict")
    print(f"\n数据是否存在: {'是' if exists else '否'}")
    
    # 删除数据
    success = storage.delete("example", "test_dict")
    print(f"删除数据结果: {'成功' if success else '失败'}")
    
    # 再次检查数据是否存在
    exists = storage.exists("example", "test_dict")
    print(f"删除后数据是否存在: {'是' if exists else '否'}")
    
    print("基本使用示例完成")

def example_storage_levels():
    """存储级别示例"""
    print("\n=== 存储级别示例 ===")
    
    # 创建数据存储
    storage = DataStorage()
    
    # 创建测试数据
    hot_data = {"level": "hot", "timestamp": datetime.now().isoformat()}
    warm_data = {"level": "warm", "timestamp": datetime.now().isoformat()}
    cold_data = {"level": "cold", "timestamp": datetime.now().isoformat()}
    
    # 保存到不同级别
    print("保存数据到不同存储级别...")
    storage.save("example", "hot_data", hot_data, StorageLevel.HOT)
    storage.save("example", "warm_data", warm_data, StorageLevel.WARM)
    storage.save("example", "cold_data", cold_data, StorageLevel.COLD)
    
    # 加载数据
    print("\n加载不同级别的数据:")
    loaded_hot = storage.load("example", "hot_data")
    loaded_warm = storage.load("example", "warm_data")
    loaded_cold = storage.load("example", "cold_data")
    
    print(f"热数据: {loaded_hot}")
    print(f"温数据: {loaded_warm}")
    print(f"冷数据: {loaded_cold}")
    
    # 检查文件位置
    print("\n检查文件位置:")
    hot_path = os.path.join("data", "hot", "example", "hot_data.json")
    warm_path = os.path.join("data", "warm", "example", "warm_data.json")
    cold_path = os.path.join("data", "cold", "example", "cold_data.json")
    
    print(f"热数据文件存在: {'是' if os.path.exists(hot_path) else '否'}")
    print(f"温数据文件存在: {'是' if os.path.exists(warm_path) else '否'}")
    print(f"冷数据文件存在: {'是' if os.path.exists(cold_path) else '否'}")
    
    print("存储级别示例完成")

def example_large_data():
    """大数据示例"""
    print("\n=== 大数据示例 ===")
    
    # 创建数据存储
    storage = DataStorage()
    
    # 创建大型DataFrame
    print("创建大型DataFrame...")
    rows = 10000
    df = pd.DataFrame({
        "id": range(1, rows + 1),
        "name": [f"Item-{i}" for i in range(1, rows + 1)],
        "value1": np.random.rand(rows),
        "value2": np.random.rand(rows),
        "value3": np.random.rand(rows),
        "category": np.random.choice(["A", "B", "C", "D", "E"], rows)
    })
    
    print(f"DataFrame大小: {df.shape}")
    
    # 计时保存
    print("\n保存大型DataFrame...")
    start_time = time.time()
    success = storage.save("example", "large_df", df)
    end_time = time.time()
    
    print(f"保存结果: {'成功' if success else '失败'}")
    print(f"保存耗时: {end_time - start_time:.2f}秒")
    
    # 计时加载
    print("\n加载大型DataFrame...")
    start_time = time.time()
    loaded_df = storage.load("example", "large_df")
    end_time = time.time()
    
    print(f"加载结果: {'成功' if loaded_df is not None else '失败'}")
    print(f"加载耗时: {end_time - start_time:.2f}秒")
    print(f"加载后DataFrame大小: {loaded_df.shape if loaded_df is not None else 'N/A'}")
    
    # 再次加载（应该从内存缓存加载）
    print("\n再次加载大型DataFrame（从内存缓存）...")
    start_time = time.time()
    loaded_df = storage.load("example", "large_df")
    end_time = time.time()
    
    print(f"加载结果: {'成功' if loaded_df is not None else '失败'}")
    print(f"加载耗时: {end_time - start_time:.2f}秒")
    
    print("大数据示例完成")

def example_backup_restore():
    """备份和恢复示例"""
    print("\n=== 备份和恢复示例 ===")
    
    # 创建数据存储
    storage = DataStorage()
    
    # 创建测试数据
    data = {
        "name": "备份测试数据",
        "value": 456,
        "timestamp": datetime.now().isoformat()
    }
    
    # 保存数据
    print(f"保存数据: {data}")
    storage.save("example", "backup_test", data)
    
    # 备份数据
    print("\n备份数据...")
    success = storage.backup("example")
    print(f"备份结果: {'成功' if success else '失败'}")
    
    # 检查备份目录
    backup_dir = "data/backup"
    backups = [d for d in os.listdir(backup_dir) if os.path.isdir(os.path.join(backup_dir, d))]
    backups.sort(reverse=True)
    
    if backups:
        latest_backup = backups[0]
        print(f"最新备份: {latest_backup}")
        
        # 检查备份文件
        backup_file = os.path.join(backup_dir, latest_backup, "hot", "example", "backup_test.json")
        print(f"备份文件存在: {'是' if os.path.exists(backup_file) else '否'}")
        
        if os.path.exists(backup_file):
            # 读取备份文件
            with open(backup_file, 'r', encoding='utf-8') as f:
                backup_data = json.load(f)
            
            print(f"备份数据: {backup_data}")
    else:
        print("未找到备份")
    
    print("备份和恢复示例完成")

def example_cleanup():
    """清理示例"""
    print("\n=== 清理示例 ===")
    
    # 创建数据存储
    storage = DataStorage()
    
    # 手动触发清理
    print("手动触发清理...")
    storage._cleanup_storage()
    
    # 检查清理结果
    print("\n清理后的内存缓存大小:")
    with storage.memory_cache_lock:
        cache_size = sum(size for _, _, size in storage.memory_cache.values())
        cache_count = len(storage.memory_cache)
    
    print(f"缓存项数: {cache_count}")
    print(f"缓存大小: {cache_size/1024/1024:.2f}MB")
    
    print("清理示例完成")

def main():
    """主函数"""
    print("=== 数据存储示例 ===")
    
    # 创建必要的目录
    os.makedirs('data/hot', exist_ok=True)
    os.makedirs('data/warm', exist_ok=True)
    os.makedirs('data/cold', exist_ok=True)
    os.makedirs('data/backup', exist_ok=True)
    
    # 询问用户选择示例
    print("\n请选择要运行的示例:")
    print("1. 基本使用示例")
    print("2. 存储级别示例")
    print("3. 大数据示例")
    print("4. 备份和恢复示例")
    print("5. 清理示例")
    print("6. 运行所有示例")
    print("0. 退出")
    
    choice = input("\n请输入选项（0-6）: ")
    
    if choice == '1':
        example_basic_usage()
    elif choice == '2':
        example_storage_levels()
    elif choice == '3':
        example_large_data()
    elif choice == '4':
        example_backup_restore()
    elif choice == '5':
        example_cleanup()
    elif choice == '6':
        example_basic_usage()
        example_storage_levels()
        example_large_data()
        example_backup_restore()
        example_cleanup()
    elif choice == '0':
        print("退出")
    else:
        print("无效选项")

if __name__ == '__main__':
    main()
