"""
统一数据访问接口测试

测试混合数据库系统的基本功能
"""

import os
import sys
import unittest
import json
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('test_unified_data_access')

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

# 导入被测试的模块
from database.unified_data_access import UnifiedDataAccess

class TestUnifiedDataAccess(unittest.TestCase):
    """统一数据访问接口测试类"""
    
    @classmethod
    def setUpClass(cls):
        """测试前的准备工作"""
        logger.info("初始化统一数据访问接口测试...")
        
        # 创建测试配置
        cls.test_config = {
            "postgresql": {
                "enabled": False,
                "use_sqlite_fallback": True,
                "sqlite_db_path": "data/test_financial_system.db"
            },
            "influxdb": {
                "enabled": False,
                "use_csv_fallback": True,
                "csv_dir": "data/test_time_series"
            },
            "mongodb": {
                "enabled": False,
                "use_json_fallback": True,
                "json_dir": "data/test_documents"
            },
            "redis": {
                "enabled": False,
                "use_memory_fallback": True
            },
            "fallback": {
                "enabled": True,
                "data_dir": "data/test"
            }
        }
        
        # 创建统一数据访问接口
        cls.data_access = UnifiedDataAccess(cls.test_config)
        
        logger.info("统一数据访问接口测试初始化完成")
    
    @classmethod
    def tearDownClass(cls):
        """测试后的清理工作"""
        logger.info("清理统一数据访问接口测试...")
        
        # 删除测试数据库文件
        if os.path.exists("data/test_financial_system.db"):
            os.remove("data/test_financial_system.db")
        
        # 删除测试数据目录
        for dir_path in ["data/test_time_series", "data/test_documents", "data/test"]:
            if os.path.exists(dir_path):
                for file_name in os.listdir(dir_path):
                    file_path = os.path.join(dir_path, file_name)
                    if os.path.isfile(file_path):
                        os.remove(file_path)
        
        logger.info("统一数据访问接口测试清理完成")
    
    def test_relational_data(self):
        """测试关系型数据的存储和查询"""
        logger.info("测试关系型数据的存储和查询...")
        
        # 创建测试数据
        test_stock = {
            "code": "000001",
            "name": "平安银行",
            "listing_date": "1991-04-03",
            "industry_code": "J66",
            "sector_code": "bank",
            "market": "sz",
            "is_st": False,
            "is_suspended": False,
            "updated_at": datetime.now().isoformat()
        }
        
        # 保存数据
        result = self.data_access.save_data("stocks", test_stock)
        self.assertIsNotNone(result, "保存股票数据失败")
        
        # 查询数据
        query_result = self.data_access.get_data("stocks", {"code": "000001"})
        self.assertIsNotNone(query_result, "查询股票数据失败")
        self.assertTrue(isinstance(query_result, list), "查询结果应该是列表")
        self.assertEqual(len(query_result), 1, "查询结果应该只有一条记录")
        self.assertEqual(query_result[0]["code"], "000001", "查询结果的股票代码不匹配")
        self.assertEqual(query_result[0]["name"], "平安银行", "查询结果的股票名称不匹配")
        
        logger.info("关系型数据的存储和查询测试通过")
    
    def test_memory_data(self):
        """测试内存数据的存储和查询"""
        logger.info("测试内存数据的存储和查询...")
        
        # 创建测试数据
        test_stock_info = {
            "code": "000001",
            "name": "平安银行",
            "price": 18.5,
            "change": 0.5,
            "updated_at": datetime.now().isoformat()
        }
        
        # 保存数据
        key = "stock:realtime:000001"
        result = self.data_access.save_data(key, test_stock_info, {"data_type": "hash", "ttl": 60})
        self.assertTrue(result, "保存实时行情数据失败")
        
        # 查询数据
        query_result = self.data_access.get_data(key, options={"data_type": "hash"})
        self.assertIsNotNone(query_result, "查询实时行情数据失败")
        self.assertTrue(isinstance(query_result, dict), "查询结果应该是字典")
        self.assertEqual(query_result["code"], "000001", "查询结果的股票代码不匹配")
        self.assertEqual(query_result["name"], "平安银行", "查询结果的股票名称不匹配")
        
        logger.info("内存数据的存储和查询测试通过")
    
    def test_document_data(self):
        """测试文档数据的存储和查询"""
        logger.info("测试文档数据的存储和查询...")
        
        # 创建测试数据
        test_policy = {
            "policy_id": "policy_123",
            "title": "关于促进经济稳定增长的意见",
            "source": "国务院",
            "publish_date": "2025-05-15",
            "policy_code": "国发〔2025〕15号",
            "content": "为促进经济稳定增长，提出以下意见：...",
            "url": "https://www.gov.cn/zhengce/content/2025-05/15/content_123456.htm",
            "created_at": datetime.now().isoformat()
        }
        
        # 保存数据
        result = self.data_access.save_data("policies", test_policy)
        self.assertIsNotNone(result, "保存政策数据失败")
        
        # 查询数据
        query_result = self.data_access.get_data("policies", {"policy_id": "policy_123"})
        self.assertIsNotNone(query_result, "查询政策数据失败")
        
        logger.info("文档数据的存储和查询测试通过")
    
    def test_time_series_data(self):
        """测试时间序列数据的存储和查询"""
        logger.info("测试时间序列数据的存储和查询...")
        
        # 创建测试数据
        test_price_data = {
            "code": "000001",
            "market": "sz",
            "open": 18.2,
            "high": 18.8,
            "low": 18.0,
            "close": 18.5,
            "volume": 12345678,
            "amount": 123456789.0,
            "change_pct": 2.5,
            "time": datetime.now().isoformat()
        }
        
        # 保存数据
        result = self.data_access.save_data("stock_prices", test_price_data)
        self.assertIsNotNone(result, "保存股票价格数据失败")
        
        # 查询数据
        query_result = self.data_access.get_data("stock_prices", {"code": "000001"})
        self.assertIsNotNone(query_result, "查询股票价格数据失败")
        
        logger.info("时间序列数据的存储和查询测试通过")
    
    def test_data_sync(self):
        """测试数据同步"""
        logger.info("测试数据同步...")
        
        # 创建测试数据
        test_policy_analysis = {
            "policy_id": "policy_123",
            "parsed_at": datetime.now().isoformat(),
            "subject": "国务院",
            "action": "促进",
            "object": "经济增长",
            "policy_type": "经济政策",
            "sentiment_score": 0.75,
            "industry_impacts": {
                "金融": 0.8,
                "房地产": 0.6,
                "制造业": 0.7,
                "科技": 0.5
            },
            "version": 1
        }
        
        # 保存数据
        result = self.data_access.save_data("policy_analysis", test_policy_analysis, {"sync": True})
        self.assertIsNotNone(result, "保存政策解析结果失败")
        
        # 查询同步的数据
        query_result = self.data_access.get_data("policy:latest", options={"data_type": "list"})
        
        logger.info("数据同步测试通过")
    
    def test_cache(self):
        """测试缓存机制"""
        logger.info("测试缓存机制...")
        
        # 创建测试数据
        test_stock = {
            "code": "000002",
            "name": "万科A",
            "listing_date": "1991-01-29",
            "industry_code": "K70",
            "sector_code": "real_estate",
            "market": "sz",
            "is_st": False,
            "is_suspended": False,
            "updated_at": datetime.now().isoformat()
        }
        
        # 保存数据
        result = self.data_access.save_data("stocks", test_stock)
        self.assertIsNotNone(result, "保存股票数据失败")
        
        # 查询数据（使用缓存）
        query_result1 = self.data_access.get_data("stocks", {"code": "000002"}, {"use_cache": True})
        self.assertIsNotNone(query_result1, "查询股票数据失败")
        
        # 再次查询数据（应该使用缓存）
        query_result2 = self.data_access.get_data("stocks", {"code": "000002"}, {"use_cache": True})
        self.assertIsNotNone(query_result2, "查询股票数据失败")
        
        logger.info("缓存机制测试通过")

if __name__ == '__main__':
    unittest.main()
