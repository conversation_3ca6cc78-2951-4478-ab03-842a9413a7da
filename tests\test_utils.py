"""
Tests for utility functions.
"""

import os
import sys
import unittest
from datetime import datetime, timedelta

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.config_loader import ConfigLoader
from utils.data_utils import convert_amount, get_latest_trading_date
from utils.cache_utils import Cache

class TestConfigLoader(unittest.TestCase):
    """
    Tests for ConfigLoader.
    """
    
    def setUp(self):
        """
        Set up test environment.
        """
        self.config_loader = ConfigLoader()
    
    def test_get_config(self):
        """
        Test getting configuration values.
        """
        # Test getting existing value
        lookback_days = self.config_loader.get('engines.news_policy.lookback_days')
        self.assertIsNotNone(lookback_days)
        
        # Test getting non-existing value with default
        non_existing = self.config_loader.get('non.existing.key', 'default_value')
        self.assertEqual(non_existing, 'default_value')

class TestDataUtils(unittest.TestCase):
    """
    Tests for data utility functions.
    """
    
    def test_convert_amount(self):
        """
        Test convert_amount function.
        """
        # Test with different units
        self.assertEqual(convert_amount('1.2亿'), 120000000)
        self.assertEqual(convert_amount('3456万'), 34560000)
        self.assertEqual(convert_amount('789.1千'), 789100)
        
        # Test with numeric value
        self.assertEqual(convert_amount('123.45'), 123.45)
        
        # Test with None or empty string
        self.assertEqual(convert_amount(None), 0.0)
        self.assertEqual(convert_amount(''), 0.0)
    
    def test_get_latest_trading_date(self):
        """
        Test get_latest_trading_date function.
        """
        # This test might fail if the market is closed for an extended period
        latest_date = get_latest_trading_date()
        self.assertIsInstance(latest_date, datetime.date)
        
        # Latest trading date should not be in the future
        self.assertLessEqual(latest_date, datetime.now().date())

class TestCacheUtils(unittest.TestCase):
    """
    Tests for cache utility functions.
    """
    
    def setUp(self):
        """
        Set up test environment.
        """
        self.cache = Cache(cache_dir="tests/test_cache")
    
    def tearDown(self):
        """
        Clean up after tests.
        """
        self.cache.clear()
        
        # Remove test cache directory
        import shutil
        if os.path.exists("tests/test_cache"):
            shutil.rmtree("tests/test_cache")
    
    def test_cache_set_get(self):
        """
        Test setting and getting cache values.
        """
        # Set a value
        self.cache.set("test_key", "test_value")
        
        # Get the value
        value = self.cache.get("test_key")
        self.assertEqual(value, "test_value")
        
        # Get a non-existing value
        non_existing = self.cache.get("non_existing_key", "default_value")
        self.assertEqual(non_existing, "default_value")
    
    def test_cache_delete(self):
        """
        Test deleting cache values.
        """
        # Set a value
        self.cache.set("test_key", "test_value")
        
        # Delete the value
        self.cache.delete("test_key")
        
        # Get the value (should return None)
        value = self.cache.get("test_key")
        self.assertIsNone(value)

if __name__ == '__main__':
    unittest.main()
