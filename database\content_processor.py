"""
新闻和政策内容处理器

负责处理新闻和政策原始数据，提取特征，生成摘要，并存储到混合数据库系统中
"""

import os
import json
import logging
import hashlib
import re
from datetime import datetime
from typing import Dict, Any, List, Optional, Union, Tuple

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('content_processor')

class ContentProcessor:
    """新闻和政策内容处理器"""

    def __init__(self, data_access, file_storage, nlp_processor=None):
        """
        初始化内容处理器

        Args:
            data_access: 统一数据访问接口
            file_storage: 文件存储管理器
            nlp_processor: NLP处理器，如果为None则使用简单处理
        """
        self.data_access = data_access
        self.file_storage = file_storage
        self.nlp_processor = nlp_processor
        logger.info("内容处理器初始化完成")

    def process_news(self, news_data: Dict[str, Any], save_content: bool = True) -> Dict[str, Any]:
        """
        处理新闻数据

        Args:
            news_data: 新闻数据，包含标题、内容、来源、发布时间等
            save_content: 是否保存原始内容

        Returns:
            处理结果，包含元数据和分析结果
        """
        try:
            # 1. 生成唯一ID
            news_id = self._generate_id(news_data.get('title', ''), news_data.get('publish_date', ''))

            # 2. 提取元数据
            metadata = self._extract_news_metadata(news_id, news_data)

            # 3. 保存原始内容（如果需要）
            content_path = None
            if save_content and 'content' in news_data:
                content_path = self.file_storage.save_news_content(
                    news_id=news_id,
                    content=news_data.get('content', ''),
                    source=news_data.get('source', 'unknown'),
                    publish_date=news_data.get('publish_date', datetime.now().strftime('%Y-%m-%d'))
                )
                metadata['content_storage_path'] = content_path

            # 4. 分析内容
            analysis_result = self._analyze_news_content(news_id, news_data)

            # 5. 保存元数据和分析结果
            self.data_access.save_data('news_metadata', metadata)
            self.data_access.save_data('news_analysis', analysis_result)

            # 6. 返回处理结果
            return {
                'metadata': metadata,
                'analysis': analysis_result
            }

        except Exception as e:
            logger.error(f"处理新闻数据失败: {str(e)}")
            raise

    def process_policy(self, policy_data: Dict[str, Any], save_content: bool = True) -> Dict[str, Any]:
        """
        处理政策数据

        Args:
            policy_data: 政策数据，包含标题、内容、来源、发布时间等
            save_content: 是否保存原始内容

        Returns:
            处理结果，包含元数据和分析结果
        """
        try:
            # 1. 生成唯一ID
            policy_id = self._generate_id(policy_data.get('title', ''), policy_data.get('publish_date', ''))

            # 2. 提取元数据
            metadata = self._extract_policy_metadata(policy_id, policy_data)

            # 3. 保存原始内容（如果需要）
            content_path = None
            if save_content and 'content' in policy_data:
                content_path = self.file_storage.save_policy_content(
                    policy_id=policy_id,
                    content=policy_data.get('content', ''),
                    source=policy_data.get('source', 'unknown'),
                    publish_date=policy_data.get('publish_date', datetime.now().strftime('%Y-%m-%d'))
                )
                metadata['content_storage_path'] = content_path

            # 4. 分析内容
            analysis_result = self._analyze_policy_content(policy_id, policy_data)

            # 5. 保存元数据和分析结果
            self.data_access.save_data('policy_metadata', metadata)
            self.data_access.save_data('policy_analysis', analysis_result)

            # 6. 返回处理结果
            return {
                'metadata': metadata,
                'analysis': analysis_result
            }

        except Exception as e:
            logger.error(f"处理政策数据失败: {str(e)}")
            raise

    def _generate_id(self, title: str, date_str: str) -> str:
        """
        生成唯一ID

        Args:
            title: 标题
            date_str: 日期字符串

        Returns:
            唯一ID
        """
        # 使用标题和日期的组合生成哈希
        hash_input = f"{title}_{date_str}_{datetime.now().timestamp()}"
        return hashlib.md5(hash_input.encode('utf-8')).hexdigest()[:16]

    def _extract_news_metadata(self, news_id: str, news_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        提取新闻元数据

        Args:
            news_id: 新闻ID
            news_data: 新闻数据

        Returns:
            新闻元数据
        """
        return {
            'news_id': news_id,
            'title': news_data.get('title', ''),
            'source': news_data.get('source', ''),
            'publish_date': news_data.get('publish_date', ''),
            'url': news_data.get('url', ''),
            'importance_score': self._calculate_importance_score(news_data),
            'is_processed': True,
            'created_at': datetime.now().isoformat(),
            'updated_at': datetime.now().isoformat()
        }

    def _extract_policy_metadata(self, policy_id: str, policy_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        提取政策元数据

        Args:
            policy_id: 政策ID
            policy_data: 政策数据

        Returns:
            政策元数据
        """
        return {
            'policy_id': policy_id,
            'title': policy_data.get('title', ''),
            'source': policy_data.get('source', ''),
            'publish_date': policy_data.get('publish_date', ''),
            'policy_code': policy_data.get('policy_code', ''),
            'policy_type': policy_data.get('policy_type', ''),
            'url': policy_data.get('url', ''),
            'importance_score': self._calculate_importance_score(policy_data),
            'is_parsed': True,
            'created_at': datetime.now().isoformat(),
            'updated_at': datetime.now().isoformat()
        }

    def _calculate_importance_score(self, data: Dict[str, Any]) -> float:
        """
        计算重要性评分

        Args:
            data: 数据

        Returns:
            重要性评分
        """
        # 如果已有重要性评分，则直接使用
        if 'importance_score' in data:
            return float(data['importance_score'])

        # 否则根据标题和来源计算
        score = 0.5  # 默认分数

        # 根据标题关键词调整分数
        title = data.get('title', '')
        important_keywords = ['重要', '关键', '重大', '紧急', '突发', '重磅', '突破']
        for keyword in important_keywords:
            if keyword in title:
                score += 0.1

        # 根据来源调整分数
        source = data.get('source', '')
        important_sources = ['国务院', '中国人民银行', '证监会', '发改委', '财政部']
        for s in important_sources:
            if s in source:
                score += 0.1

        return min(score, 1.0)  # 确保分数不超过1.0

    def _analyze_news_content(self, news_id: str, news_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        分析新闻内容

        Args:
            news_id: 新闻ID
            news_data: 新闻数据

        Returns:
            分析结果
        """
        content = news_data.get('content', '')
        title = news_data.get('title', '')

        # 使用NLP处理器（如果有）
        if self.nlp_processor:
            sentiment = self.nlp_processor.analyze_sentiment(content)
            keywords = self.nlp_processor.extract_keywords(content)
            entities = self.nlp_processor.extract_entities(content)
            summary = self.nlp_processor.generate_summary(content)
            key_points = self.nlp_processor.extract_key_points(content)
            related_stocks = self.nlp_processor.extract_stocks(content)
            related_industries = self.nlp_processor.extract_industries(content)
        else:
            # 简单处理
            sentiment = self._simple_sentiment_analysis(content)
            keywords = self._simple_keyword_extraction(title, content)
            entities = []
            summary = self._simple_summary_generation(content)
            key_points = self._simple_key_points_extraction(content)
            related_stocks = []
            related_industries = []

        # 构建分析结果
        return {
            'news_id': news_id,
            'processed_at': datetime.now().isoformat(),
            'sentiment': sentiment,
            'keywords': keywords,
            'related_stocks': related_stocks,
            'related_industries': related_industries,
            'summary': summary,
            'key_points': key_points,
            'cluster_id': '',  # 需要后续聚类处理
            'topic_tags': [],  # 需要后续主题分析
            'version': 1
        }

    def _analyze_policy_content(self, policy_id: str, policy_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        分析政策内容

        Args:
            policy_id: 政策ID
            policy_data: 政策数据

        Returns:
            分析结果
        """
        content = policy_data.get('content', '')
        title = policy_data.get('title', '')

        # 使用NLP处理器（如果有）
        if self.nlp_processor:
            sentiment = self.nlp_processor.analyze_sentiment(content)
            keywords = self.nlp_processor.extract_keywords(content)
            entities = self.nlp_processor.extract_entities(content)
            summary = self.nlp_processor.generate_summary(content)
            key_points = self.nlp_processor.extract_key_points(content)
            industry_impacts = self.nlp_processor.analyze_industry_impacts(content)
            stock_impacts = self.nlp_processor.analyze_stock_impacts(content)
            subject, action, obj = self.nlp_processor.extract_sao(content)
        else:
            # 简单处理
            sentiment = self._simple_sentiment_analysis(content)
            keywords = self._simple_keyword_extraction(title, content)
            entities = []
            summary = self._simple_summary_generation(content)
            key_points = self._simple_key_points_extraction(content)
            industry_impacts = {}
            stock_impacts = []
            subject, action, obj = self._simple_sao_extraction(title, content)

        # 构建分析结果
        return {
            'policy_id': policy_id,
            'parsed_at': datetime.now().isoformat(),
            'subject': subject,
            'action': action,
            'object': obj,
            'policy_type': policy_data.get('policy_type', ''),
            'sentiment': sentiment,
            'keywords': keywords,
            'industry_impacts': industry_impacts,
            'stock_impacts': stock_impacts,
            'summary': summary,
            'key_points': key_points,
            'decay_factor': 0.05,  # 默认衰减因子
            'version': 1
        }

    def _simple_sentiment_analysis(self, text: str) -> Dict[str, Any]:
        """
        简单情感分析

        Args:
            text: 文本内容

        Returns:
            情感分析结果
        """
        # 积极词汇
        positive_words = ['利好', '增长', '上涨', '提高', '促进', '改善', '优化', '支持', '鼓励', '发展']
        # 消极词汇
        negative_words = ['利空', '下跌', '降低', '减少', '限制', '下滑', '收紧', '风险', '问题', '困难']

        # 计算情感分数
        positive_count = sum(text.count(word) for word in positive_words)
        negative_count = sum(text.count(word) for word in negative_words)
        total_count = positive_count + negative_count

        if total_count == 0:
            score = 0.5  # 中性
            label = 'neutral'
        else:
            score = positive_count / total_count
            if score > 0.6:
                label = 'positive'
            elif score < 0.4:
                label = 'negative'
            else:
                label = 'neutral'

        return {
            'score': score,
            'label': label,
            'confidence': 0.6  # 简单方法的置信度较低
        }

    def _simple_keyword_extraction(self, title: str, content: str) -> List[Dict[str, Any]]:
        """
        简单关键词提取

        Args:
            title: 标题
            content: 内容

        Returns:
            关键词列表
        """
        # 合并标题和内容
        text = title + ' ' + content

        # 预定义的行业和实体关键词
        industry_keywords = ['金融', '银行', '保险', '证券', '房地产', '制造业', '科技', '医药', '能源', '消费']
        entity_keywords = ['国务院', '央行', '证监会', '发改委', '财政部', '银保监会', '商务部']

        # 提取关键词
        keywords = []

        # 从标题中提取关键词（权重更高）
        for word in industry_keywords + entity_keywords:
            if word in title:
                is_entity = word in entity_keywords
                entity_type = 'organization' if is_entity else 'industry'
                keywords.append({
                    'word': word,
                    'weight': 0.9,
                    'is_entity': is_entity,
                    'entity_type': entity_type
                })

        # 从内容中提取关键词
        for word in industry_keywords + entity_keywords:
            if word in content and not any(k['word'] == word for k in keywords):
                is_entity = word in entity_keywords
                entity_type = 'organization' if is_entity else 'industry'
                keywords.append({
                    'word': word,
                    'weight': 0.7,
                    'is_entity': is_entity,
                    'entity_type': entity_type
                })

        # 提取其他可能的关键词（根据词频）
        words = re.findall(r'\w+', text)
        word_freq = {}
        for word in words:
            if len(word) >= 2:  # 只考虑长度大于等于2的词
                word_freq[word] = word_freq.get(word, 0) + 1

        # 按词频排序
        sorted_words = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)

        # 添加高频词作为关键词
        for word, freq in sorted_words[:10]:  # 取前10个高频词
            if not any(k['word'] == word for k in keywords):
                keywords.append({
                    'word': word,
                    'weight': min(0.5 + freq / 100, 0.8),  # 根据词频计算权重
                    'is_entity': False,
                    'entity_type': ''
                })

        return keywords

    def _simple_summary_generation(self, content: str) -> str:
        """
        简单摘要生成

        Args:
            content: 内容

        Returns:
            摘要
        """
        # 如果内容较短，直接返回
        if len(content) <= 200:
            return content

        # 按句子分割
        sentences = re.split(r'[。！？.!?]', content)
        sentences = [s.strip() for s in sentences if s.strip()]

        # 如果句子数量少于3，返回前两句
        if len(sentences) <= 3:
            return '。'.join(sentences[:2]) + '。'

        # 选择重要句子
        important_sentences = []

        # 第一句通常包含重要信息
        if sentences:
            important_sentences.append(sentences[0])

        # 查找包含关键词的句子
        keywords = ['重要', '关键', '主要', '核心', '显著', '明显', '特别']
        for sentence in sentences[1:]:
            if any(keyword in sentence for keyword in keywords):
                important_sentences.append(sentence)
                if len(important_sentences) >= 3:
                    break

        # 如果没有找到足够的重要句子，添加更多句子
        if len(important_sentences) < 3 and len(sentences) > 1:
            for sentence in sentences[1:]:
                if sentence not in important_sentences:
                    important_sentences.append(sentence)
                    if len(important_sentences) >= 3:
                        break

        # 生成摘要
        summary = '。'.join(important_sentences) + '。'

        # 限制摘要长度
        if len(summary) > 300:
            summary = summary[:297] + '...'

        return summary

    def _simple_key_points_extraction(self, content: str) -> List[str]:
        """
        简单要点提取

        Args:
            content: 内容

        Returns:
            要点列表
        """
        # 查找包含数字编号的句子（如"1. xxx"，"（一）xxx"等）
        numbered_points = re.findall(r'[（(]?[一二三四五六七八九十\d][)）、. ]+[^。！？.!?]+', content)

        # 查找包含关键标记的句子
        key_markers = ['重点是', '主要内容', '核心是', '关键在于', '要点包括']
        marked_points = []
        for marker in key_markers:
            if marker in content:
                idx = content.find(marker) + len(marker)
                sentence = content[idx:].split('。')[0]
                if sentence:
                    marked_points.append(sentence + '。')

        # 合并要点
        key_points = numbered_points + marked_points

        # 如果没有找到要点，尝试使用句子作为要点
        if not key_points:
            sentences = re.split(r'[。！？.!?]', content)
            sentences = [s.strip() for s in sentences if s.strip()]

            # 选择包含关键词的句子
            keywords = ['重要', '关键', '主要', '核心', '显著', '明显', '特别']
            for sentence in sentences:
                if any(keyword in sentence for keyword in keywords):
                    key_points.append(sentence + '。')
                    if len(key_points) >= 3:
                        break

        # 限制要点数量和长度
        key_points = key_points[:5]  # 最多5个要点
        key_points = [point[:100] + '...' if len(point) > 100 else point for point in key_points]  # 限制长度

        return key_points

    def _simple_sao_extraction(self, title: str, content: str) -> Tuple[str, str, str]:
        """
        简单主谓宾提取

        Args:
            title: 标题
            content: 内容

        Returns:
            主体、动作、对象
        """
        # 预定义的主体列表
        subjects = ['国务院', '央行', '证监会', '发改委', '财政部', '银保监会', '商务部', '国家', '政府']

        # 预定义的动作列表
        actions = ['发布', '宣布', '推出', '实施', '出台', '颁布', '制定', '发行', '提出', '要求']

        # 预定义的对象列表
        objects = ['政策', '措施', '规定', '办法', '条例', '通知', '意见', '计划', '方案']

        # 从标题中提取
        subject = ''
        action = ''
        obj = ''

        # 提取主体
        for s in subjects:
            if s in title or s in content[:100]:
                subject = s
                break

        # 如果没有找到主体，使用默认值
        if not subject:
            subject = '政府'

        # 提取动作
        for a in actions:
            if a in title:
                action = a
                break

        # 如果没有找到动作，尝试从内容开头提取
        if not action:
            for a in actions:
                if a in content[:200]:
                    action = a
                    break

        # 如果仍然没有找到动作，使用默认值
        if not action:
            action = '发布'

        # 提取对象
        for o in objects:
            if o in title:
                obj = o
                break

        # 如果没有找到对象，尝试从内容开头提取
        if not obj:
            for o in objects:
                if o in content[:200]:
                    obj = o
                    break

        # 如果仍然没有找到对象，使用默认值
        if not obj:
            obj = '政策'

        return subject, action, obj
