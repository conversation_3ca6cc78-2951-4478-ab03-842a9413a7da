"""
Cache utilities for policy_liquidity_volatility_arbitrage.
"""

import os
import json
import pickle
import hashlib
import functools
import time
from datetime import datetime, timedelta
from utils.logger import logger

class Cache:
    """
    Cache class for storing and retrieving data.
    Supports memory and file-based caching.
    """
    
    def __init__(self, cache_dir="data/cache", default_expiry=86400):
        """
        Initialize the Cache.
        
        Args:
            cache_dir (str): Directory to store cache files.
            default_expiry (int): Default cache expiry time in seconds (1 day).
        """
        self.cache_dir = cache_dir
        self.default_expiry = default_expiry
        self.memory_cache = {}
        
        # Create cache directory if it doesn't exist
        if not os.path.exists(cache_dir):
            os.makedirs(cache_dir)
    
    def _get_cache_key(self, *args, **kwargs):
        """
        Generate a cache key from function arguments.
        
        Args:
            *args: Function positional arguments.
            **kwargs: Function keyword arguments.
            
        Returns:
            str: Cache key.
        """
        # Convert args and kwargs to a string representation
        key_parts = [str(arg) for arg in args]
        key_parts.extend([f"{k}={v}" for k, v in sorted(kwargs.items())])
        key_str = ":".join(key_parts)
        
        # Generate MD5 hash
        return hashlib.md5(key_str.encode()).hexdigest()
    
    def _get_cache_path(self, key):
        """
        Get the file path for a cache key.
        
        Args:
            key (str): Cache key.
            
        Returns:
            str: Cache file path.
        """
        return os.path.join(self.cache_dir, f"{key}.pkl")
    
    def get(self, key, default=None):
        """
        Get a value from the cache.
        
        Args:
            key (str): Cache key.
            default: Default value to return if key is not found.
            
        Returns:
            Cached value or default.
        """
        # Check memory cache first
        if key in self.memory_cache:
            entry = self.memory_cache[key]
            if entry['expiry'] > time.time():
                logger.debug(f"Cache hit (memory): {key}")
                return entry['value']
            else:
                # Remove expired entry
                del self.memory_cache[key]
        
        # Check file cache
        cache_path = self._get_cache_path(key)
        if os.path.exists(cache_path):
            try:
                with open(cache_path, 'rb') as f:
                    entry = pickle.load(f)
                
                if entry['expiry'] > time.time():
                    # Add to memory cache for faster access next time
                    self.memory_cache[key] = entry
                    logger.debug(f"Cache hit (file): {key}")
                    return entry['value']
                else:
                    # Remove expired cache file
                    os.remove(cache_path)
            except Exception as e:
                logger.error(f"Error reading cache file {cache_path}: {str(e)}")
        
        logger.debug(f"Cache miss: {key}")
        return default
    
    def set(self, key, value, expiry=None):
        """
        Set a value in the cache.
        
        Args:
            key (str): Cache key.
            value: Value to cache.
            expiry (int, optional): Cache expiry time in seconds. Defaults to default_expiry.
            
        Returns:
            bool: True if successful, False otherwise.
        """
        if expiry is None:
            expiry = self.default_expiry
        
        expiry_time = time.time() + expiry
        entry = {'value': value, 'expiry': expiry_time}
        
        # Set in memory cache
        self.memory_cache[key] = entry
        
        # Set in file cache
        cache_path = self._get_cache_path(key)
        try:
            with open(cache_path, 'wb') as f:
                pickle.dump(entry, f)
            logger.debug(f"Cache set: {key}")
            return True
        except Exception as e:
            logger.error(f"Error writing cache file {cache_path}: {str(e)}")
            return False
    
    def delete(self, key):
        """
        Delete a value from the cache.
        
        Args:
            key (str): Cache key.
            
        Returns:
            bool: True if successful, False otherwise.
        """
        # Remove from memory cache
        if key in self.memory_cache:
            del self.memory_cache[key]
        
        # Remove from file cache
        cache_path = self._get_cache_path(key)
        if os.path.exists(cache_path):
            try:
                os.remove(cache_path)
                logger.debug(f"Cache deleted: {key}")
                return True
            except Exception as e:
                logger.error(f"Error deleting cache file {cache_path}: {str(e)}")
                return False
        
        return True
    
    def clear(self):
        """
        Clear all cache entries.
        
        Returns:
            bool: True if successful, False otherwise.
        """
        # Clear memory cache
        self.memory_cache = {}
        
        # Clear file cache
        try:
            for filename in os.listdir(self.cache_dir):
                if filename.endswith('.pkl'):
                    os.remove(os.path.join(self.cache_dir, filename))
            logger.info("Cache cleared")
            return True
        except Exception as e:
            logger.error(f"Error clearing cache: {str(e)}")
            return False

def cached(expiry=None, cache_instance=None):
    """
    Decorator to cache function results.
    
    Args:
        expiry (int, optional): Cache expiry time in seconds. Defaults to cache's default_expiry.
        cache_instance (Cache, optional): Cache instance to use. Defaults to a new Cache instance.
        
    Returns:
        function: Decorated function.
    """
    if cache_instance is None:
        cache_instance = Cache()
    
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # Generate cache key
            cache_key = cache_instance._get_cache_key(func.__name__, *args, **kwargs)
            
            # Try to get from cache
            cached_result = cache_instance.get(cache_key)
            if cached_result is not None:
                return cached_result
            
            # Call function and cache result
            result = func(*args, **kwargs)
            cache_instance.set(cache_key, result, expiry)
            
            return result
        return wrapper
    return decorator

# Default cache instance
default_cache = Cache()
