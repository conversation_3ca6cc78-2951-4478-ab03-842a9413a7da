<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>政策-流动性-波动率套利系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.95);
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .header h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 10px;
        }
        
        .status-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 10px;
        }
        
        .status-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .status-dot {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background: #27ae60;
        }
        
        .dashboard {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .card h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            border-bottom: 2px solid #3498db;
            padding-bottom: 5px;
        }
        
        .metric {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #ecf0f1;
        }
        
        .metric:last-child {
            border-bottom: none;
        }
        
        .metric-value {
            font-weight: bold;
            color: #27ae60;
        }
        
        .btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.3s;
        }
        
        .btn:hover {
            background: #2980b9;
        }
        
        .btn:disabled {
            background: #bdc3c7;
            cursor: not-allowed;
        }
        
        .news-item {
            padding: 10px;
            border-left: 3px solid #3498db;
            margin-bottom: 10px;
            background: #f8f9fa;
            border-radius: 0 5px 5px 0;
        }
        
        .news-title {
            font-weight: bold;
            margin-bottom: 5px;
            color: #2c3e50;
        }
        
        .news-meta {
            font-size: 12px;
            color: #7f8c8d;
        }
        
        .keyword-tag {
            display: inline-block;
            background: #e74c3c;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            margin: 2px;
        }
        
        .anomaly-alert {
            background: #e74c3c;
            color: white;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 10px;
        }
        
        .loading {
            text-align: center;
            color: #7f8c8d;
            padding: 20px;
        }
        
        .controls {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
            flex-wrap: wrap;
        }
        
        @media (max-width: 768px) {
            .dashboard {
                grid-template-columns: 1fr;
            }
            
            .status-bar {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .controls {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>政策-流动性-波动率套利系统</h1>
            <div class="status-bar">
                <div class="status-item">
                    <div class="status-dot" id="systemStatus"></div>
                    <span>系统状态: <span id="systemStatusText">正常</span></span>
                </div>
                <div class="status-item">
                    <span>最后更新: <span id="lastUpdate">--</span></span>
                </div>
                <div class="status-item">
                    <span>连接状态: <span id="connectionStatus">连接中...</span></span>
                </div>
            </div>
        </div>
        
        <div class="dashboard">
            <!-- 系统控制面板 -->
            <div class="card">
                <h3>系统控制</h3>
                <div class="controls">
                    <button class="btn" onclick="collectData()">收集数据</button>
                    <button class="btn" onclick="detectAnomalies()">检测异动</button>
                    <button class="btn" onclick="refreshData()">刷新数据</button>
                </div>
                <div id="controlStatus"></div>
            </div>
            
            <!-- 数据概览 -->
            <div class="card">
                <h3>数据概览</h3>
                <div id="dataOverview">
                    <div class="loading">加载中...</div>
                </div>
            </div>
            
            <!-- 资金流向 -->
            <div class="card">
                <h3>资金流向</h3>
                <div id="fundFlow">
                    <div class="loading">加载中...</div>
                </div>
            </div>
            
            <!-- 异动提示 -->
            <div class="card">
                <h3>异动提示</h3>
                <div id="anomalies">
                    <div class="loading">暂无异动</div>
                </div>
            </div>
        </div>
        
        <div class="dashboard">
            <!-- 最新新闻 -->
            <div class="card" style="grid-column: 1 / -1;">
                <h3>最新新闻</h3>
                <div id="latestNews">
                    <div class="loading">加载中...</div>
                </div>
            </div>
            
            <!-- 关键词分析 -->
            <div class="card" style="grid-column: 1 / -1;">
                <h3>关键词分析</h3>
                <div id="keywordAnalysis">
                    <div class="loading">加载中...</div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
    <script>
        // WebSocket连接
        const socket = io();
        
        socket.on('connect', function() {
            document.getElementById('connectionStatus').textContent = '已连接';
            console.log('已连接到服务器');
        });
        
        socket.on('disconnect', function() {
            document.getElementById('connectionStatus').textContent = '连接断开';
            console.log('与服务器连接断开');
        });
        
        socket.on('status_update', function(data) {
            updateSystemStatus(data);
        });
        
        socket.on('data_collection_complete', function(data) {
            const status = document.getElementById('controlStatus');
            if (data.success) {
                status.innerHTML = '<div style="color: green;">数据收集完成</div>';
            } else {
                status.innerHTML = '<div style="color: orange;">数据收集部分失败</div>';
            }
            refreshData();
        });
        
        // 更新系统状态
        function updateSystemStatus(data) {
            document.getElementById('lastUpdate').textContent = 
                new Date(data.timestamp).toLocaleString();
        }
        
        // 收集数据
        async function collectData() {
            const btn = event.target;
            btn.disabled = true;
            btn.textContent = '收集中...';
            
            try {
                const response = await fetch('/api/data/collect', {
                    method: 'POST'
                });
                const result = await response.json();
                
                const status = document.getElementById('controlStatus');
                status.innerHTML = '<div style="color: blue;">数据收集已启动...</div>';
                
            } catch (error) {
                console.error('数据收集失败:', error);
                const status = document.getElementById('controlStatus');
                status.innerHTML = '<div style="color: red;">数据收集失败</div>';
            } finally {
                btn.disabled = false;
                btn.textContent = '收集数据';
            }
        }
        
        // 检测异动
        async function detectAnomalies() {
            const btn = event.target;
            btn.disabled = true;
            btn.textContent = '检测中...';
            
            try {
                const response = await fetch('/api/anomaly/detect', {
                    method: 'POST'
                });
                const result = await response.json();
                
                displayAnomalies(result);
                
            } catch (error) {
                console.error('异动检测失败:', error);
            } finally {
                btn.disabled = false;
                btn.textContent = '检测异动';
            }
        }
        
        // 刷新数据
        async function refreshData() {
            await Promise.all([
                loadLatestData(),
                loadKeywordAnalysis()
            ]);
        }
        
        // 加载最新数据
        async function loadLatestData() {
            try {
                const response = await fetch('/api/data/latest');
                const data = await response.json();
                
                // 更新数据概览
                updateDataOverview(data);
                
                // 更新资金流向
                updateFundFlow(data.fund_flow);
                
                // 更新最新新闻
                updateLatestNews(data.news);
                
            } catch (error) {
                console.error('加载数据失败:', error);
            }
        }
        
        // 更新数据概览
        function updateDataOverview(data) {
            const overview = document.getElementById('dataOverview');
            overview.innerHTML = `
                <div class="metric">
                    <span>新闻数量</span>
                    <span class="metric-value">${data.news ? data.news.length : 0}</span>
                </div>
                <div class="metric">
                    <span>资金流数据</span>
                    <span class="metric-value">${data.fund_flow && Object.keys(data.fund_flow).length > 0 ? '有' : '无'}</span>
                </div>
                <div class="metric">
                    <span>市场数据</span>
                    <span class="metric-value">${data.market && Object.keys(data.market).length > 0 ? '有' : '无'}</span>
                </div>
            `;
        }
        
        // 更新资金流向
        function updateFundFlow(fundFlow) {
            const container = document.getElementById('fundFlow');
            
            if (!fundFlow || Object.keys(fundFlow).length === 0) {
                container.innerHTML = '<div class="loading">暂无数据</div>';
                return;
            }
            
            let html = '';
            
            if (fundFlow.northbound_flow) {
                html += `
                    <div class="metric">
                        <span>北向资金净流入</span>
                        <span class="metric-value">${fundFlow.northbound_flow.net_flow || 0}亿</span>
                    </div>
                `;
            }
            
            if (fundFlow.sector_flows && fundFlow.sector_flows.length > 0) {
                html += '<div style="margin-top: 10px;"><strong>行业资金流向:</strong></div>';
                fundFlow.sector_flows.slice(0, 5).forEach(sector => {
                    html += `
                        <div class="metric">
                            <span>${sector.sector_name}</span>
                            <span class="metric-value">${sector.net_flow || 0}亿</span>
                        </div>
                    `;
                });
            }
            
            container.innerHTML = html || '<div class="loading">暂无数据</div>';
        }
        
        // 更新最新新闻
        function updateLatestNews(news) {
            const container = document.getElementById('latestNews');
            
            if (!news || news.length === 0) {
                container.innerHTML = '<div class="loading">暂无新闻</div>';
                return;
            }
            
            let html = '';
            news.slice(0, 10).forEach(item => {
                const keywords = item.keywords ? item.keywords.slice(0, 5) : [];
                const keywordTags = keywords.map(kw => 
                    `<span class="keyword-tag">${kw.word}</span>`
                ).join('');
                
                html += `
                    <div class="news-item">
                        <div class="news-title">${item.title || '无标题'}</div>
                        <div class="news-meta">
                            来源: ${item.source || '未知'} | 
                            时间: ${item.publish_time || '未知'}
                        </div>
                        <div style="margin-top: 5px;">${keywordTags}</div>
                    </div>
                `;
            });
            
            container.innerHTML = html;
        }
        
        // 显示异动
        function displayAnomalies(result) {
            const container = document.getElementById('anomalies');
            
            if (!result.has_anomaly || !result.anomalies || result.anomalies.length === 0) {
                container.innerHTML = '<div class="loading">暂无异动</div>';
                return;
            }
            
            let html = '';
            result.anomalies.forEach(anomaly => {
                html += `
                    <div class="anomaly-alert">
                        <strong>${anomaly.type || '未知异动'}</strong><br>
                        ${anomaly.description || '无描述'}
                    </div>
                `;
            });
            
            container.innerHTML = html;
        }
        
        // 加载关键词分析
        async function loadKeywordAnalysis() {
            try {
                const response = await fetch('/api/keywords/summary');
                const data = await response.json();
                
                const container = document.getElementById('keywordAnalysis');
                
                if (!data.top_keywords || data.top_keywords.length === 0) {
                    container.innerHTML = '<div class="loading">暂无关键词数据</div>';
                    return;
                }
                
                let html = '<div><strong>热门关键词:</strong></div>';
                data.top_keywords.slice(0, 20).forEach(item => {
                    html += `<span class="keyword-tag">${item.word} (${item.count})</span>`;
                });
                
                container.innerHTML = html;
                
            } catch (error) {
                console.error('加载关键词分析失败:', error);
            }
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            refreshData();
            
            // 每30秒自动刷新数据
            setInterval(refreshData, 30000);
        });
    </script>
</body>
</html>
