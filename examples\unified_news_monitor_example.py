"""
统一新闻监控示例

展示如何使用统一新闻监控模块
"""

import os
import sys
import time
import json
from datetime import datetime, timedelta

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入统一新闻监控器
from data_sources.unified_news_monitor import UnifiedNewsMonitor

# 导入中央调度器
from core.scheduler import CentralScheduler, TaskPriority

def example_basic_usage():
    """基本使用示例"""
    print("\n=== 基本使用示例 ===")
    
    # 创建调度器
    scheduler = CentralScheduler()
    
    # 创建统一新闻监控器
    monitor = UnifiedNewsMonitor()
    
    # 设置调度器
    monitor.set_scheduler(scheduler)
    
    # 启动调度器
    scheduler.start()
    
    # 初始化模块
    monitor.initialize()
    
    # 等待初始化完成
    print("等待初始化完成...")
    time.sleep(3)
    
    # 获取模块状态
    status = monitor.get_status()
    print(f"模块状态: {json.dumps(status, indent=2)}")
    
    # 停止调度器
    scheduler.stop()
    
    print("基本使用示例完成")

def example_fetch_news():
    """获取新闻示例"""
    print("\n=== 获取新闻示例 ===")
    
    # 创建调度器
    scheduler = CentralScheduler()
    
    # 创建统一新闻监控器
    monitor = UnifiedNewsMonitor()
    
    # 设置调度器
    monitor.set_scheduler(scheduler)
    
    # 启动调度器
    scheduler.start()
    
    # 手动获取新闻
    print("开始获取新闻...")
    result = monitor.fetch_news()
    
    print(f"获取结果: {json.dumps(result, indent=2)}")
    
    # 停止调度器
    scheduler.stop()
    
    print("获取新闻示例完成")

def example_analyze_hot_topics():
    """分析热点话题示例"""
    print("\n=== 分析热点话题示例 ===")
    
    # 创建调度器
    scheduler = CentralScheduler()
    
    # 创建统一新闻监控器
    monitor = UnifiedNewsMonitor()
    
    # 设置调度器
    monitor.set_scheduler(scheduler)
    
    # 启动调度器
    scheduler.start()
    
    # 先获取新闻
    print("先获取新闻...")
    monitor.fetch_news()
    
    # 分析热点话题
    print("\n开始分析热点话题...")
    result = monitor.analyze_hot_topics()
    
    print(f"分析结果状态: {result['status']}")
    print(f"分析结果消息: {result['message']}")
    
    # 显示热点板块
    if result['hot_sectors']:
        print("\n热点板块:")
        hot_sectors = sorted(result['hot_sectors'].items(), key=lambda x: x[1], reverse=True)
        for sector, score in hot_sectors[:5]:
            print(f"- {sector}: {score:.2f}")
    
    # 显示热点个股
    if result['hot_stocks']:
        print("\n热点个股:")
        hot_stocks = sorted(result['hot_stocks'].items(), key=lambda x: x[1], reverse=True)
        for stock, score in hot_stocks[:5]:
            print(f"- {stock}: {score:.2f}")
    
    # 停止调度器
    scheduler.stop()
    
    print("分析热点话题示例完成")

def example_generate_daily_report():
    """生成每日报告示例"""
    print("\n=== 生成每日报告示例 ===")
    
    # 创建调度器
    scheduler = CentralScheduler()
    
    # 创建统一新闻监控器
    monitor = UnifiedNewsMonitor()
    
    # 设置调度器
    monitor.set_scheduler(scheduler)
    
    # 启动调度器
    scheduler.start()
    
    # 先获取新闻
    print("先获取新闻...")
    monitor.fetch_news()
    
    # 生成每日报告
    print("\n开始生成每日报告...")
    result = monitor.generate_daily_report()
    
    print(f"生成结果状态: {result['status']}")
    print(f"生成结果消息: {result['message']}")
    
    # 显示报告文件
    if result['report_file']:
        print(f"\n报告文件: {result['report_file']}")
        
        # 显示报告内容摘要
        content = result['report_content']
        lines = content.split('\n')
        preview = '\n'.join(lines[:20]) + '\n...'
        print(f"\n报告内容摘要:\n{preview}")
    
    # 停止调度器
    scheduler.stop()
    
    print("生成每日报告示例完成")

def example_scheduled_tasks():
    """定时任务示例"""
    print("\n=== 定时任务示例 ===")
    
    # 创建调度器
    scheduler = CentralScheduler()
    
    # 创建统一新闻监控器
    monitor = UnifiedNewsMonitor()
    
    # 设置调度器
    monitor.set_scheduler(scheduler)
    
    # 启动调度器
    scheduler.start()
    
    # 初始化模块
    monitor.initialize()
    
    # 等待初始化完成
    print("等待初始化完成...")
    time.sleep(3)
    
    # 查看调度器中的任务
    print("\n调度器中的任务:")
    for task_id, task in scheduler.tasks.items():
        print(f"- {task_id}: {task.function} (状态: {task.status.name})")
    
    # 手动调度任务
    print("\n手动调度任务...")
    task_id = monitor.schedule_task(
        function='fetch_news',
        params={},
        priority='high',
        schedule_time=datetime.now() + timedelta(seconds=5)
    )
    
    print(f"任务ID: {task_id}")
    
    # 等待任务执行
    print("等待任务执行...")
    time.sleep(10)
    
    # 查看任务状态
    status = scheduler.get_task_status(task_id)
    print(f"任务状态: {status.name if status else 'Unknown'}")
    
    # 获取任务结果
    result = scheduler.get_task_result(task_id)
    if result:
        print(f"任务结果: {json.dumps(result, indent=2)}")
    
    # 停止调度器
    scheduler.stop()
    
    print("定时任务示例完成")

def example_long_running():
    """长时间运行示例"""
    print("\n=== 长时间运行示例 ===")
    
    # 创建调度器
    scheduler = CentralScheduler()
    
    # 创建统一新闻监控器
    monitor = UnifiedNewsMonitor()
    
    # 设置调度器
    monitor.set_scheduler(scheduler)
    
    # 启动调度器
    scheduler.start()
    
    # 初始化模块
    monitor.initialize()
    
    # 运行一段时间
    print("系统将运行60秒...")
    try:
        for i in range(60):
            time.sleep(1)
            
            # 每10秒显示一次状态
            if i % 10 == 0:
                status = monitor.get_status()
                health = monitor.health_check()
                print(f"[{i}s] 状态: {status['is_running']}, 健康状态: {health['status']}")
    
    except KeyboardInterrupt:
        print("接收到中断信号")
    
    finally:
        # 停止调度器
        scheduler.stop()
    
    print("长时间运行示例完成")

def main():
    """主函数"""
    print("=== 统一新闻监控示例 ===")
    
    # 创建必要的目录
    os.makedirs('logs', exist_ok=True)
    os.makedirs('data', exist_ok=True)
    os.makedirs('config', exist_ok=True)
    
    # 询问用户选择示例
    print("\n请选择要运行的示例:")
    print("1. 基本使用示例")
    print("2. 获取新闻示例")
    print("3. 分析热点话题示例")
    print("4. 生成每日报告示例")
    print("5. 定时任务示例")
    print("6. 长时间运行示例")
    print("0. 退出")
    
    choice = input("\n请输入选项（0-6）: ")
    
    if choice == '1':
        example_basic_usage()
    elif choice == '2':
        example_fetch_news()
    elif choice == '3':
        example_analyze_hot_topics()
    elif choice == '4':
        example_generate_daily_report()
    elif choice == '5':
        example_scheduled_tasks()
    elif choice == '6':
        example_long_running()
    elif choice == '0':
        print("退出")
    else:
        print("无效选项")

if __name__ == '__main__':
    main()
