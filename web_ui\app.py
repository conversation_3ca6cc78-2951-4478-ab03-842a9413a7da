"""
简单的Web可视化界面
使用Flask提供基础的系统监控和数据展示
"""

import os
import sys
import json
from datetime import datetime, timedelta
from flask import Flask, render_template, jsonify, request
from flask_socketio import SocketIO, emit
import threading
import time

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.data_storage import DataStorage
from core.unified_data_collector import UnifiedDataCollector
from core.anomaly_detector import AnomalyDetector
from utils.logger import logger

app = Flask(__name__)
app.config['SECRET_KEY'] = 'trading_system_secret_key'
socketio = SocketIO(app, cors_allowed_origins="*")

# 初始化组件
data_storage = DataStorage()
data_collector = UnifiedDataCollector()
anomaly_detector = AnomalyDetector()

@app.route('/')
def index():
    """主页"""
    return render_template('index.html')

@app.route('/api/system/status')
def get_system_status():
    """获取系统状态"""
    try:
        status = {
            'timestamp': datetime.now().isoformat(),
            'system_health': 'healthy',
            'data_collection': {
                'last_update': datetime.now().isoformat(),
                'status': 'running'
            },
            'monitoring': {
                'anomaly_detection': 'active',
                'alert_system': 'active'
            }
        }
        return jsonify(status)
    except Exception as e:
        logger.error(f"获取系统状态失败: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/data/latest')
def get_latest_data():
    """获取最新数据"""
    try:
        # 获取最新新闻
        latest_news = data_storage.get_latest_data('news', limit=10)
        
        # 获取最新资金流
        latest_fund_flow = data_storage.get_latest_data('fund_flow', limit=1)
        
        # 获取最新市场数据
        latest_market = data_storage.get_latest_data('market', limit=1)
        
        data = {
            'news': latest_news,
            'fund_flow': latest_fund_flow[0] if latest_fund_flow else {},
            'market': latest_market[0] if latest_market else {},
            'timestamp': datetime.now().isoformat()
        }
        
        return jsonify(data)
    except Exception as e:
        logger.error(f"获取最新数据失败: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/data/collect', methods=['POST'])
def trigger_data_collection():
    """触发数据收集"""
    try:
        # 在后台线程中运行数据收集
        def collect_data():
            success = data_collector.run_collection_sync()
            socketio.emit('data_collection_complete', {'success': success})
        
        thread = threading.Thread(target=collect_data)
        thread.start()
        
        return jsonify({'message': '数据收集已启动', 'status': 'started'})
    except Exception as e:
        logger.error(f"触发数据收集失败: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/anomaly/detect', methods=['POST'])
def trigger_anomaly_detection():
    """触发异动检测"""
    try:
        # 获取最新数据进行异动检测
        latest_data = {}
        
        # 获取最新新闻数据
        news_data = data_storage.get_latest_data('news', limit=10)
        if news_data:
            sentiment_scores = [item.get('keyword_summary', {}).get('avg_score', 0) for item in news_data]
            avg_sentiment = sum(sentiment_scores) / len(sentiment_scores) if sentiment_scores else 0
            latest_data['news_data'] = {
                'sentiment_score': avg_sentiment,
                'news_count': len(news_data)
            }
        
        # 获取最新资金流数据
        fund_flow_data = data_storage.get_latest_data('fund_flow', limit=1)
        if fund_flow_data:
            latest_data['fund_flow_data'] = fund_flow_data[0]
        
        # 运行异动检测
        anomalies = []
        
        if 'news_data' in latest_data:
            news_anomaly = anomaly_detector.detect_news_anomaly(latest_data['news_data'])
            if news_anomaly.get('has_anomaly'):
                anomalies.extend(news_anomaly.get('anomalies', []))
        
        if 'fund_flow_data' in latest_data:
            fund_anomaly = anomaly_detector.detect_fund_flow_anomaly(latest_data['fund_flow_data'])
            if fund_anomaly.get('has_anomaly'):
                anomalies.extend(fund_anomaly.get('anomalies', []))
        
        return jsonify({
            'anomalies': anomalies,
            'detection_time': datetime.now().isoformat(),
            'has_anomaly': len(anomalies) > 0
        })
        
    except Exception as e:
        logger.error(f"异动检测失败: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/keywords/summary')
def get_keywords_summary():
    """获取关键词摘要"""
    try:
        # 获取最近的新闻数据
        recent_news = data_storage.get_latest_data('news', limit=50)
        
        # 统计关键词
        keyword_stats = {}
        category_stats = {}
        
        for news in recent_news:
            keywords = news.get('keywords', [])
            for kw in keywords:
                word = kw.get('word', '')
                category = kw.get('category', '其他')
                score = kw.get('final_score', 0)
                
                if word not in keyword_stats:
                    keyword_stats[word] = {'count': 0, 'total_score': 0, 'category': category}
                keyword_stats[word]['count'] += 1
                keyword_stats[word]['total_score'] += score
                
                if category not in category_stats:
                    category_stats[category] = {'count': 0, 'total_score': 0}
                category_stats[category]['count'] += 1
                category_stats[category]['total_score'] += score
        
        # 计算平均分数并排序
        for word, stats in keyword_stats.items():
            stats['avg_score'] = stats['total_score'] / stats['count']
        
        top_keywords = sorted(keyword_stats.items(), 
                            key=lambda x: x[1]['avg_score'], reverse=True)[:20]
        
        return jsonify({
            'top_keywords': [{'word': k, **v} for k, v in top_keywords],
            'category_stats': category_stats,
            'total_news': len(recent_news),
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        logger.error(f"获取关键词摘要失败: {str(e)}")
        return jsonify({'error': str(e)}), 500

@socketio.on('connect')
def handle_connect():
    """WebSocket连接处理"""
    logger.info('客户端已连接')
    emit('connected', {'message': '已连接到系统'})

@socketio.on('disconnect')
def handle_disconnect():
    """WebSocket断开处理"""
    logger.info('客户端已断开连接')

def background_monitoring():
    """后台监控任务"""
    while True:
        try:
            # 每30秒发送一次系统状态更新
            status = {
                'timestamp': datetime.now().isoformat(),
                'system_health': 'healthy',
                'active_connections': len(socketio.server.manager.rooms.get('/', {}))
            }
            socketio.emit('status_update', status)
            
            # 检查是否有新的异动
            # 这里可以添加实时异动检测逻辑
            
            time.sleep(30)
        except Exception as e:
            logger.error(f"后台监控任务错误: {str(e)}")
            time.sleep(60)

if __name__ == '__main__':
    # 启动后台监控线程
    monitor_thread = threading.Thread(target=background_monitoring, daemon=True)
    monitor_thread.start()
    
    logger.info("Web UI服务器启动中...")
    socketio.run(app, host='0.0.0.0', port=5000, debug=True)
