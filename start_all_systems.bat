@echo off
echo ========================================
echo    政策-流动性-波动率套利系统
echo         启动所有系统
echo ========================================
echo.

cd /d "%~dp0"

echo 正在启动所有系统组件...
echo.

echo [1/2] 启动24小时监控系统...
start "监控系统" cmd /k "python monitor_system.py start"
timeout /t 3 /nobreak >nul

echo [2/2] 启动Web可视化界面...
start "Web界面" cmd /k "python web_ui/app.py"
timeout /t 3 /nobreak >nul

echo.
echo ========================================
echo 所有系统已启动完成！
echo.
echo 已启动的服务：
echo - 24小时监控系统 (后台运行)
echo - Web可视化界面 (http://127.0.0.1:5000)
echo.
echo 使用说明：
echo 1. 访问 http://127.0.0.1:5000 查看系统状态
echo 2. 运行 start_main_system.bat 获取股票推荐
echo 3. 运行 stop_all_systems.bat 停止所有服务
echo ========================================
echo.
pause
