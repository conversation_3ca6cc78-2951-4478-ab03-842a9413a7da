"""
政策分析模块测试

测试政策分析模块的基本功能
"""

import os
import sys
import unittest
import json
import logging
from datetime import datetime
import pandas as pd

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('test_policy_analyzer')

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入被测试的模块
from data_sources.policy_analyzer import PolicyAnalyzer
from core.data_storage import DataStorage, StorageLevel

class TestPolicyAnalyzer(unittest.TestCase):
    """政策分析模块测试类"""
    
    @classmethod
    def setUpClass(cls):
        """测试前的准备工作"""
        logger.info("初始化政策分析模块测试...")
        
        # 创建测试目录
        cls.test_dir = os.path.join('data', 'test_policy_analyzer')
        os.makedirs(cls.test_dir, exist_ok=True)
        
        # 创建配置目录
        os.makedirs('config', exist_ok=True)
        
        # 创建测试配置文件
        cls.config_path = os.path.join('config', 'policy_analyzer_config.json')
        with open(cls.config_path, 'w', encoding='utf-8') as f:
            json.dump({
                'module_name': 'policy_analyzer',
                'enabled': True,
                'log_level': 'INFO',
                'data_sources': {
                    'gov_policy': {
                        'enabled': True,
                        'url': 'https://www.gov.cn/zhengce/zhengcewenjianku/index.htm',
                        'max_pages': 3
                    },
                    'ndrc_policy': {
                        'enabled': True,
                        'url': 'https://www.ndrc.gov.cn/xxgk/',
                        'max_pages': 3
                    }
                },
                'nlp': {
                    'sentiment_model': 'finbert',
                    'keyword_extraction': {
                        'max_keywords': 10,
                        'min_frequency': 2
                    }
                },
                'industry_impact': {
                    'decay_factor': 0.05,
                    'threshold': 0.3
                }
            }, f, indent=4)
        
        # 创建测试数据存储
        cls.data_storage = DataStorage()
        
        # 创建测试政策数据
        cls.test_policies = pd.DataFrame({
            'title': [
                '关于促进经济稳定增长的意见',
                '关于加强金融支持实体经济的通知',
                '关于推动制造业高质量发展的指导意见'
            ],
            'source': ['国务院', '央行', '发改委'],
            'publish_date': ['2025-05-15', '2025-05-14', '2025-05-13'],
            'content': [
                '为促进经济稳定增长，提出以下意见：一、加大宏观政策调节力度。二、着力扩大内需。三、大力支持实体经济发展。',
                '为加强金融支持实体经济，现就有关事项通知如下：一、降低企业融资成本。二、增加信贷投放。三、优化金融服务。',
                '为推动制造业高质量发展，提出以下指导意见：一、加强创新驱动。二、推动数字化转型。三、提升产业基础能力。'
            ],
            'url': [
                'https://www.gov.cn/zhengce/content/2025-05/15/content_123456.htm',
                'http://www.pbc.gov.cn/goutongjiaoliu/113456/113469/123456/index.html',
                'https://www.ndrc.gov.cn/xxgk/zcfb/tz/202505/t20250513_123456.html'
            ]
        })
        
        # 保存测试政策数据
        cls.data_storage.save('policy_data', 'gov_policies', cls.test_policies.to_dict('records'), StorageLevel.WARM)
        
        logger.info("政策分析模块测试初始化完成")
    
    @classmethod
    def tearDownClass(cls):
        """测试后的清理工作"""
        logger.info("清理政策分析模块测试...")
        
        # 删除测试配置文件
        if os.path.exists(cls.config_path):
            os.remove(cls.config_path)
        
        # 删除测试目录
        import shutil
        if os.path.exists(cls.test_dir):
            shutil.rmtree(cls.test_dir)
        
        logger.info("政策分析模块测试清理完成")
    
    def test_init(self):
        """测试初始化"""
        logger.info("测试政策分析模块初始化...")
        
        # 创建政策分析模块
        policy_analyzer = PolicyAnalyzer(config_path=self.config_path, data_storage=self.data_storage)
        
        # 验证配置
        self.assertIsNotNone(policy_analyzer.config)
        self.assertEqual(policy_analyzer.config.get('module_name'), 'policy_analyzer')
        self.assertEqual(policy_analyzer.module_name, 'policy_analyzer')
        
        logger.info("政策分析模块初始化测试通过")
    
    def test_parse_policy_title(self):
        """测试解析政策标题"""
        logger.info("测试解析政策标题...")
        
        # 创建政策分析模块
        policy_analyzer = PolicyAnalyzer(config_path=self.config_path, data_storage=self.data_storage)
        
        # 测试政策
        policy = {
            'title': '关于促进经济稳定增长的意见',
            'source': '国务院',
            'publish_date': '2025-05-15',
            'content': '为促进经济稳定增长，提出以下意见：一、加大宏观政策调节力度。二、着力扩大内需。三、大力支持实体经济发展。'
        }
        
        # 解析政策标题
        result = policy_analyzer._parse_policy_title(policy)
        
        # 验证解析结果
        self.assertIsNotNone(result)
        self.assertIn('subject', result)
        self.assertIn('action', result)
        self.assertIn('object', result)
        self.assertEqual(result['subject'], '国务院')
        self.assertIn(result['action'], ['促进', '发布'])
        self.assertIn('经济稳定增长', result['object'])
        
        logger.info("解析政策标题测试通过")
    
    def test_parse_policy_content(self):
        """测试解析政策内容"""
        logger.info("测试解析政策内容...")
        
        # 创建政策分析模块
        policy_analyzer = PolicyAnalyzer(config_path=self.config_path, data_storage=self.data_storage)
        
        # 测试政策
        policy = {
            'title': '关于促进经济稳定增长的意见',
            'source': '国务院',
            'publish_date': '2025-05-15',
            'content': '为促进经济稳定增长，提出以下意见：一、加大宏观政策调节力度。二、着力扩大内需。三、大力支持实体经济发展。'
        }
        
        # 解析政策内容
        result = policy_analyzer._parse_policy_content(policy)
        
        # 验证解析结果
        self.assertIsNotNone(result)
        self.assertIn('keywords', result)
        self.assertIn('summary', result)
        self.assertIn('key_points', result)
        self.assertTrue(len(result['keywords']) > 0)
        self.assertTrue(len(result['summary']) > 0)
        self.assertTrue(len(result['key_points']) > 0)
        
        logger.info("解析政策内容测试通过")
    
    def test_analyze_policy_sentiment(self):
        """测试分析政策情感"""
        logger.info("测试分析政策情感...")
        
        # 创建政策分析模块
        policy_analyzer = PolicyAnalyzer(config_path=self.config_path, data_storage=self.data_storage)
        
        # 测试政策
        policy = {
            'title': '关于促进经济稳定增长的意见',
            'source': '国务院',
            'publish_date': '2025-05-15',
            'content': '为促进经济稳定增长，提出以下意见：一、加大宏观政策调节力度。二、着力扩大内需。三、大力支持实体经济发展。'
        }
        
        # 分析政策情感
        result = policy_analyzer._analyze_policy_sentiment(policy)
        
        # 验证分析结果
        self.assertIsNotNone(result)
        self.assertIn('score', result)
        self.assertIn('label', result)
        self.assertTrue(0 <= result['score'] <= 1)
        self.assertIn(result['label'], ['positive', 'negative', 'neutral'])
        
        logger.info("分析政策情感测试通过")
    
    def test_calculate_industry_impacts(self):
        """测试计算行业影响"""
        logger.info("测试计算行业影响...")
        
        # 创建政策分析模块
        policy_analyzer = PolicyAnalyzer(config_path=self.config_path, data_storage=self.data_storage)
        
        # 测试政策
        policy = {
            'title': '关于促进经济稳定增长的意见',
            'source': '国务院',
            'publish_date': '2025-05-15',
            'content': '为促进经济稳定增长，提出以下意见：一、加大宏观政策调节力度。二、着力扩大内需。三、大力支持实体经济发展。四、鼓励金融机构增加信贷投放，支持制造业和科技创新。'
        }
        
        # 计算行业影响
        result = policy_analyzer._calculate_industry_impacts(policy)
        
        # 验证计算结果
        self.assertIsNotNone(result)
        self.assertTrue(len(result) > 0)
        
        # 检查是否包含相关行业
        industries = list(result.keys())
        self.assertTrue(any('金融' in industry for industry in industries) or 
                       any('银行' in industry for industry in industries))
        self.assertTrue(any('制造' in industry for industry in industries))
        
        logger.info("计算行业影响测试通过")
    
    def test_fetch_and_parse_policies(self):
        """测试获取并解析政策"""
        logger.info("测试获取并解析政策...")
        
        # 创建政策分析模块
        policy_analyzer = PolicyAnalyzer(config_path=self.config_path, data_storage=self.data_storage)
        
        # 重写_fetch_policies方法，使用测试数据
        def mock_fetch_policies(self):
            return self.data_storage.load('policy_data', 'gov_policies', level=StorageLevel.WARM)
        
        # 保存原始方法
        original_fetch_policies = PolicyAnalyzer._fetch_policies
        
        # 替换为模拟方法
        PolicyAnalyzer._fetch_policies = mock_fetch_policies
        
        try:
            # 获取并解析政策
            result = policy_analyzer.fetch_and_parse_policies()
            
            # 验证结果
            self.assertIsNotNone(result)
            self.assertEqual(result['status'], 'success')
            self.assertTrue(result['count'] > 0)
            
            # 验证已解析的政策
            self.assertTrue(len(policy_analyzer.parsed_policies) > 0)
            
            # 验证解析结果
            parsed_policy = policy_analyzer.parsed_policies[0]
            self.assertIn('title', parsed_policy)
            self.assertIn('source', parsed_policy)
            self.assertIn('publish_date', parsed_policy)
            self.assertIn('content', parsed_policy)
            self.assertIn('parsed_title', parsed_policy)
            self.assertIn('parsed_content', parsed_policy)
            self.assertIn('sentiment', parsed_policy)
            self.assertIn('industry_impacts', parsed_policy)
            
        finally:
            # 恢复原始方法
            PolicyAnalyzer._fetch_policies = original_fetch_policies
        
        logger.info("获取并解析政策测试通过")

if __name__ == '__main__':
    unittest.main()
