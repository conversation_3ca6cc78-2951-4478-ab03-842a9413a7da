"""
数据类型定义

定义系统中的数据类型及其对应的存储位置
"""

# 关系型数据库存储的数据类型
RELATIONAL_DATA_TYPES = {
    # 股票基本信息
    'stocks': {
        'table': 'stocks',
        'primary_key': 'code',
        'description': '股票基本信息'
    },
    # 行业分类
    'industries': {
        'table': 'industries',
        'primary_key': 'code',
        'description': '行业分类信息'
    },
    # 交易日历
    'trading_calendar': {
        'table': 'trading_calendar',
        'primary_key': 'date',
        'description': '交易日历信息'
    },
    # 元数据索引
    'metadata_index': {
        'table': 'metadata_index',
        'primary_key': 'id',
        'description': '元数据索引'
    },
    # 数据源配置
    'data_sources': {
        'table': 'data_sources',
        'primary_key': 'id',
        'description': '数据源配置'
    },
    # 系统参数
    'system_params': {
        'table': 'system_params',
        'primary_key': 'param_key',
        'description': '系统参数'
    },
    # 用户设置
    'user_settings': {
        'table': 'user_settings',
        'primary_key': 'id',
        'description': '用户设置'
    }
}

# 时序数据库存储的数据类型
TIME_SERIES_DATA_TYPES = {
    # 股票价格数据
    'stock_prices': {
        'measurement': 'stock_prices',
        'tags': ['code', 'market'],
        'fields': ['open', 'high', 'low', 'close', 'volume', 'amount', 'change_pct'],
        'description': '股票价格数据'
    },
    # 指数数据
    'index_data': {
        'measurement': 'index_data',
        'tags': ['code', 'market'],
        'fields': ['open', 'high', 'low', 'close', 'volume', 'amount', 'change_pct'],
        'description': '指数数据'
    },
    # 北向南向资金数据
    'cross_border_flow': {
        'measurement': 'fund_flows',
        'tags': ['type', 'direction'],
        'fields': ['net_flow', 'inflow', 'outflow', 'accumulated'],
        'description': '北向南向资金数据'
    },
    # 行业资金流数据
    'sector_fund_flow': {
        'measurement': 'fund_flows',
        'tags': ['type', 'code'],
        'fields': ['net_flow', 'inflow', 'outflow', 'net_flow_ratio'],
        'description': '行业资金流数据'
    },
    # 个股资金流数据
    'stock_fund_flow': {
        'measurement': 'fund_flows',
        'tags': ['type', 'code'],
        'fields': ['net_flow', 'super_large_flow', 'large_flow', 'medium_flow', 'small_flow'],
        'description': '个股资金流数据'
    },
    # 波动率数据
    'volatility': {
        'measurement': 'volatility',
        'tags': ['type', 'code', 'window'],
        'fields': ['value', 'realized', 'implied', 'skew'],
        'description': '波动率数据'
    },
    # 情绪指标数据
    'sentiment_index': {
        'measurement': 'sentiment_index',
        'tags': ['type', 'source'],
        'fields': ['value', 'label', 'count'],
        'description': '情绪指标数据'
    }
}

# 文档数据库存储的数据类型
DOCUMENT_DATA_TYPES = {
    # 政策文档
    'policies': {
        'collection': 'policies',
        'index_fields': ['title', 'source', 'publish_date', 'policy_code'],
        'description': '政策文档'
    },
    # 政策解析结果
    'policy_analysis': {
        'collection': 'policy_analysis',
        'index_fields': ['policy_id', 'parsed_at', 'policy_type'],
        'description': '政策解析结果'
    },
    # 新闻文档
    'news': {
        'collection': 'news',
        'index_fields': ['title', 'source', 'publish_date'],
        'description': '新闻文档'
    },
    # 新闻分析结果
    'news_analysis': {
        'collection': 'news_analysis',
        'index_fields': ['news_id', 'processed_at'],
        'description': '新闻分析结果'
    },
    # 热点话题分析
    'hot_topics': {
        'collection': 'hot_topics',
        'index_fields': ['date', 'topic_type'],
        'description': '热点话题分析'
    },
    # 情绪共振分析报告
    'sentiment_resonance': {
        'collection': 'sentiment_resonance',
        'index_fields': ['date', 'is_turning_point'],
        'description': '情绪共振分析报告'
    },
    # 波动率分析报告
    'volatility_report': {
        'collection': 'volatility_report',
        'index_fields': ['date', 'report_type'],
        'description': '波动率分析报告'
    }
}

# 内存数据库存储的数据类型
MEMORY_DATA_TYPES = {
    # 实时行情缓存
    'realtime_quotes': {
        'key_pattern': 'stock:realtime:{code}',
        'data_type': 'hash',
        'expiry': 60,  # 秒
        'description': '实时行情缓存'
    },
    # 股票信息缓存
    'stock_info': {
        'key_pattern': 'stock:info:{code}',
        'data_type': 'hash',
        'expiry': 86400,  # 1天
        'description': '股票信息缓存'
    },
    # 热点话题缓存
    'hot_topics': {
        'key_pattern': 'hot_topics:{date}',
        'data_type': 'zset',  # 有序集合
        'expiry': 86400,  # 1天
        'description': '热点话题缓存'
    },
    # 最新政策缓存
    'latest_policies': {
        'key_pattern': 'policy:latest',
        'data_type': 'list',
        'expiry': 3600,  # 1小时
        'description': '最新政策缓存'
    },
    # 最新新闻缓存
    'latest_news': {
        'key_pattern': 'news:latest',
        'data_type': 'list',
        'expiry': 1800,  # 30分钟
        'description': '最新新闻缓存'
    },
    # 计算结果缓存
    'calculation_results': {
        'key_pattern': 'calc:{module}:{function}:{params_hash}',
        'data_type': 'string',
        'expiry': 3600,  # 1小时
        'description': '计算结果缓存'
    },
    # 会话数据
    'session_data': {
        'key_pattern': 'session:{session_id}',
        'data_type': 'hash',
        'expiry': 1800,  # 30分钟
        'description': '会话数据'
    },
    # 分布式锁
    'distributed_locks': {
        'key_pattern': 'lock:{resource_name}',
        'data_type': 'string',
        'expiry': 30,  # 30秒
        'description': '分布式锁'
    }
}
