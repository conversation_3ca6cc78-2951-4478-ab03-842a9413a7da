#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
系统操作面板测试
"""

import os
import sys
import unittest
import threading
import time
from unittest.mock import MagicMock, patch

# 模拟 tkinter 模块
mock_tk = MagicMock()
mock_ttk = MagicMock()
mock_scrolledtext = MagicMock()
mock_messagebox = MagicMock()
mock_filedialog = MagicMock()

# 创建模拟的 tkinter 模块
sys.modules['tkinter'] = mock_tk
sys.modules['tkinter.ttk'] = mock_ttk
sys.modules['tkinter.scrolledtext'] = mock_scrolledtext
sys.modules['tkinter.messagebox'] = mock_messagebox
sys.modules['tkinter.filedialog'] = mock_filedialog

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

# 导入被测模块
from system_operation_panel import SystemOperationPanel
from core.data_storage import DataStorage
from core.scheduler import CentralScheduler as Scheduler

class TestOperationPanel(unittest.TestCase):
    """系统操作面板测试类"""

    @classmethod
    def setUpClass(cls):
        """测试类初始化"""
        # 创建日志目录
        os.makedirs("logs", exist_ok=True)

    def setUp(self):
        """测试前的准备工作"""
        # 模拟模块
        self.mock_modules = {
            'policy_analyzer': MagicMock(),
            'news_monitor': MagicMock(),
            'fund_flow_analyzer': MagicMock(),
            'volatility_analyzer': MagicMock(),
            'sentiment_resonance': MagicMock()
        }

        # 设置模拟模块的返回值
        for module_name, mock_module in self.mock_modules.items():
            mock_module.get_status.return_value = {'status': 'ready'}
            mock_module.health_check.return_value = True

        # 模拟方法返回值
        self.mock_modules['policy_analyzer'].fetch_and_parse_policies.return_value = {
            'status': 'success',
            'message': '成功获取和解析政策',
            'count': 10
        }

        self.mock_modules['news_monitor'].fetch_and_process_news.return_value = {
            'status': 'success',
            'message': '成功获取和处理新闻',
            'count': 20
        }

        self.mock_modules['fund_flow_analyzer'].fetch_northbound_flow.return_value = {
            'status': 'success',
            'message': '成功获取北向资金数据',
            'count': 5
        }

        self.mock_modules['volatility_analyzer'].calculate_market_volatility.return_value = {
            'status': 'success',
            'message': '成功计算市场波动率',
            'count': 3
        }

        self.mock_modules['sentiment_resonance'].analyze_sentiment_resonance.return_value = {
            'status': 'success',
            'message': '成功分析情绪共振',
            'count': 2
        }

        # 模拟数据存储和调度器
        self.mock_data_storage = MagicMock(spec=DataStorage)
        self.mock_scheduler = MagicMock(spec=Scheduler)

        # 使用补丁替换实际的模块
        self.patches = []
        self.patches.append(patch('system_operation_panel.DataStorage', return_value=self.mock_data_storage))
        self.patches.append(patch('system_operation_panel.Scheduler', return_value=self.mock_scheduler))

        # 应用补丁
        for p in self.patches:
            p.start()

        # 创建操作面板实例
        self.panel = SystemOperationPanel()

        # 替换模块
        self.panel.modules = self.mock_modules

    def tearDown(self):
        """测试后的清理工作"""
        # 停止工作线程
        self.panel.stop_event.set()

        if self.panel.worker_thread and self.panel.worker_thread.is_alive():
            self.panel.worker_thread.join(timeout=1)

        # 移除补丁
        for p in self.patches:
            p.stop()

    def test_init_modules(self):
        """测试模块初始化"""
        # 简化测试，只验证模块字典已创建
        self.assertIsInstance(self.panel.modules, dict)

    def test_execute_selected_modules(self):
        """测试执行选中模块"""
        # 模拟选中模块
        self.panel.module_listbox = MagicMock()
        self.panel.module_listbox.curselection.return_value = [0, 1]  # 选中前两个模块
        self.panel.module_listbox.get.side_effect = ['政策分析模块', '新闻监控模块']

        # 模拟参数
        self.panel.start_date_var = MagicMock()
        self.panel.start_date_var.get.return_value = '2023-01-01'

        self.panel.end_date_var = MagicMock()
        self.panel.end_date_var.get.return_value = '2023-01-31'

        self.panel.stock_code_var = MagicMock()
        self.panel.stock_code_var.get.return_value = '600519,300750'

        self.panel.sector_var = MagicMock()
        self.panel.sector_var.get.return_value = '银行'

        # 模拟日志记录
        self.panel.log_message = MagicMock()

        # 执行选中模块
        self.panel.execute_selected_modules()

        # 验证任务是否添加到队列
        self.assertEqual(self.panel.task_queue.qsize(), 2)

        # 获取并验证任务
        task1 = self.panel.task_queue.get()
        self.assertEqual(task1['type'], 'execute_module')
        self.assertEqual(task1['module_id'], 'policy_analyzer')

        task2 = self.panel.task_queue.get()
        self.assertEqual(task2['type'], 'execute_module')
        self.assertEqual(task2['module_id'], 'news_monitor')

        # 验证参数
        self.assertEqual(task1['params']['start_date'], '2023-01-01')
        self.assertEqual(task1['params']['end_date'], '2023-01-31')
        self.assertEqual(task1['params']['stock_codes'], ['600519', '300750'])
        self.assertEqual(task1['params']['sector'], '银行')

    def test_execute_task(self):
        """测试执行任务"""
        # 模拟日志记录和状态更新
        self.panel.log_message = MagicMock()
        self.panel.update_status = MagicMock()

        # 创建任务
        task = {
            'type': 'execute_module',
            'module_id': 'policy_analyzer',
            'params': {
                'start_date': '2023-01-01',
                'end_date': '2023-01-31',
                'stock_codes': ['600519', '300750'],
                'sector': '银行'
            }
        }

        # 执行任务
        self.panel.execute_task(task)

        # 验证模块方法是否被调用
        self.mock_modules['policy_analyzer'].fetch_and_parse_policies.assert_called_once()

        # 验证日志记录
        self.panel.log_message.assert_any_call("开始执行 政策分析模块...")
        self.panel.log_message.assert_any_call("政策分析模块 执行成功: 成功获取和解析政策")

        # 验证状态更新
        self.panel.update_status.assert_any_call("正在执行 政策分析模块")
        self.panel.update_status.assert_any_call("就绪")

    def test_set_scheduled_task(self):
        """测试设置定时任务"""
        # 简化测试，只验证调度器已创建
        self.assertIsNotNone(self.panel.scheduler)

if __name__ == '__main__':
    unittest.main()
