<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能金融分析驾驶舱</title>
    <script src="https://cdn.socket.io/4.0.0/socket.io.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.0/dist/echarts.min.js"></script>
    <script src="https://unpkg.com/cytoscape@3.26.0/dist/cytoscape.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, #0f1419 0%, #1a2332 50%, #2d3748 100%);
            color: #e2e8f0;
            min-height: 100vh;
            overflow-x: hidden;
        }

        .dashboard-container {
            display: grid;
            grid-template-areas:
                "header header header"
                "sensing analysis execution"
                "network network network"
                "ai-monitor ai-monitor ai-monitor";
            grid-template-rows: 80px 1fr 300px 250px;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 15px;
            padding: 15px;
            min-height: 100vh;
        }

        .dashboard-header {
            grid-area: header;
            background: linear-gradient(135deg, #1a365d 0%, #2d3748 100%);
            border-radius: 12px;
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
        }

        .header-title {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .header-title h1 {
            font-size: 24px;
            font-weight: 600;
            color: #63b3ed;
        }

        .header-title .icon {
            font-size: 28px;
            color: #4fd1c7;
        }

        .system-status {
            display: flex;
            gap: 20px;
            align-items: center;
        }

        .status-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            background: rgba(255,255,255,0.1);
            border-radius: 20px;
            font-size: 14px;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        .status-online { background: #48bb78; }
        .status-warning { background: #ed8936; }
        .status-error { background: #f56565; }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .layer-card {
            background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.2);
            border: 1px solid rgba(255,255,255,0.1);
            position: relative;
            overflow: hidden;
        }

        .layer-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #4fd1c7, #63b3ed, #9f7aea);
        }

        .layer-title {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 15px;
            font-size: 18px;
            font-weight: 600;
            color: #e2e8f0;
        }

        .layer-icon {
            font-size: 20px;
            color: #4fd1c7;
        }

        .sensing-layer {
            grid-area: sensing;
        }

        .analysis-layer {
            grid-area: analysis;
        }

        .execution-layer {
            grid-area: execution;
        }

        .network-layer {
            grid-area: network;
        }

        .ai-monitor-layer {
            grid-area: ai-monitor;
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin-bottom: 15px;
        }

        .metric-item {
            background: rgba(255,255,255,0.05);
            border-radius: 8px;
            padding: 15px;
            text-align: center;
        }

        .metric-value {
            font-size: 24px;
            font-weight: bold;
            color: #4fd1c7;
            margin-bottom: 5px;
        }

        .metric-label {
            font-size: 12px;
            color: #a0aec0;
            text-transform: uppercase;
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: rgba(255,255,255,0.1);
            border-radius: 3px;
            overflow: hidden;
            margin-top: 8px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4fd1c7, #63b3ed);
            border-radius: 3px;
            transition: width 0.3s ease;
        }

        .chart-container {
            height: 200px;
            margin-top: 15px;
        }

        .network-graph {
            height: 250px;
            background: rgba(255,255,255,0.02);
            border-radius: 8px;
            margin-top: 15px;
        }

        .ai-models-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
            margin-top: 15px;
        }

        .model-card {
            background: rgba(255,255,255,0.05);
            border-radius: 8px;
            padding: 15px;
            text-align: center;
        }

        .model-status {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin: 0 auto 10px;
        }

        .model-name {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .model-accuracy {
            font-size: 12px;
            color: #a0aec0;
        }

        .control-buttons {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #4fd1c7, #63b3ed);
            color: white;
        }

        .btn-secondary {
            background: rgba(255,255,255,0.1);
            color: #e2e8f0;
            border: 1px solid rgba(255,255,255,0.2);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        }

        .data-stream {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            background: rgba(255,255,255,0.05);
            border-radius: 6px;
            margin-bottom: 8px;
        }

        .stream-name {
            font-size: 14px;
            font-weight: 500;
        }

        .stream-status {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 12px;
        }

        .stream-indicator {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background: #48bb78;
            animation: pulse 2s infinite;
        }

        .alert-panel {
            background: rgba(245, 101, 101, 0.1);
            border: 1px solid rgba(245, 101, 101, 0.3);
            border-radius: 8px;
            padding: 12px;
            margin-top: 15px;
        }

        .alert-title {
            font-size: 14px;
            font-weight: 600;
            color: #fc8181;
            margin-bottom: 8px;
        }

        .alert-content {
            font-size: 12px;
            color: #e2e8f0;
            line-height: 1.4;
        }

        @media (max-width: 1200px) {
            .dashboard-container {
                grid-template-columns: 1fr 1fr;
                grid-template-areas:
                    "header header"
                    "sensing analysis"
                    "execution execution"
                    "network network"
                    "ai-monitor ai-monitor";
            }
        }

        @media (max-width: 768px) {
            .dashboard-container {
                grid-template-columns: 1fr;
                grid-template-areas:
                    "header"
                    "sensing"
                    "analysis"
                    "execution"
                    "network"
                    "ai-monitor";
            }

            .metrics-grid {
                grid-template-columns: 1fr;
            }

            .ai-models-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <!-- 系统头部 -->
        <div class="dashboard-header">
            <div class="header-title">
                <i class="fas fa-brain icon"></i>
                <h1>智能金融分析驾驶舱</h1>
                <span style="font-size: 14px; color: #a0aec0;">政策-流动性-波动率AI系统</span>
            </div>
            <div class="system-status">
                <div class="status-item">
                    <div class="status-dot status-online"></div>
                    <span>主系统</span>
                </div>
                <div class="status-item">
                    <div class="status-dot status-online"></div>
                    <span>监控系统</span>
                </div>
                <div class="status-item">
                    <div class="status-dot status-online"></div>
                    <span>AI引擎</span>
                </div>
                <div class="status-item">
                    <div class="status-dot status-warning"></div>
                    <span id="websocket-status">连接中...</span>
                </div>
            </div>
        </div>

        <!-- 数据感知层 -->
        <div class="layer-card sensing-layer">
            <div class="layer-title">
                <i class="fas fa-satellite-dish layer-icon"></i>
                数据感知层
                <span style="font-size: 12px; color: #a0aec0;">(24小时监控)</span>
            </div>

            <div class="metrics-grid">
                <div class="metric-item">
                    <div class="metric-value" id="news-count">0</div>
                    <div class="metric-label">新闻数据</div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 85%"></div>
                    </div>
                </div>
                <div class="metric-item">
                    <div class="metric-value" id="policy-count">0</div>
                    <div class="metric-label">政策数据</div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 70%"></div>
                    </div>
                </div>
            </div>

            <div class="data-stream">
                <span class="stream-name">资金流数据</span>
                <div class="stream-status">
                    <div class="stream-indicator"></div>
                    <span id="fund-flow-status">实时更新</span>
                </div>
            </div>

            <div class="data-stream">
                <span class="stream-name">波动率数据</span>
                <div class="stream-status">
                    <div class="stream-indicator"></div>
                    <span id="volatility-status">实时更新</span>
                </div>
            </div>

            <div class="control-buttons">
                <button class="btn btn-primary" onclick="triggerDataCollection()">
                    <i class="fas fa-sync-alt"></i>
                    数据收集
                </button>
                <button class="btn btn-secondary" onclick="showDataQuality()">
                    <i class="fas fa-chart-line"></i>
                    数据质量
                </button>
            </div>
        </div>

        <!-- AI智能分析层 -->
        <div class="layer-card analysis-layer">
            <div class="layer-title">
                <i class="fas fa-brain layer-icon"></i>
                AI智能分析层
                <span style="font-size: 12px; color: #a0aec0;">(多模态学习)</span>
            </div>

            <div class="metrics-grid">
                <div class="metric-item">
                    <div class="metric-value" id="sentiment-score">0.75</div>
                    <div class="metric-label">情感分析</div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 75%"></div>
                    </div>
                </div>
                <div class="metric-item">
                    <div class="metric-value" id="coupling-score">0.68</div>
                    <div class="metric-label">耦合强度</div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 68%"></div>
                    </div>
                </div>
            </div>

            <div class="chart-container" id="sentiment-chart"></div>

            <div class="control-buttons">
                <button class="btn btn-primary" onclick="runAIAnalysis()">
                    <i class="fas fa-cogs"></i>
                    AI分析
                </button>
                <button class="btn btn-secondary" onclick="showModelDetails()">
                    <i class="fas fa-info-circle"></i>
                    模型详情
                </button>
            </div>
        </div>

        <!-- 决策执行层 -->
        <div class="layer-card execution-layer">
            <div class="layer-title">
                <i class="fas fa-rocket layer-icon"></i>
                决策执行层
                <span style="font-size: 12px; color: #a0aec0;">(智能推荐)</span>
            </div>

            <div class="metrics-grid">
                <div class="metric-item">
                    <div class="metric-value" id="recommendation-count">5</div>
                    <div class="metric-label">推荐股票</div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 90%"></div>
                    </div>
                </div>
                <div class="metric-item">
                    <div class="metric-value" id="confidence-score">0.82</div>
                    <div class="metric-label">置信度</div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 82%"></div>
                    </div>
                </div>
            </div>

            <div id="recommendations-list" style="margin-top: 15px;">
                <!-- 推荐列表将在这里动态生成 -->
            </div>

            <div class="control-buttons">
                <button class="btn btn-primary" onclick="generateRecommendations()">
                    <i class="fas fa-magic"></i>
                    生成推荐
                </button>
                <button class="btn btn-secondary" onclick="showRiskAnalysis()">
                    <i class="fas fa-shield-alt"></i>
                    风险分析
                </button>
            </div>

            <div class="alert-panel" id="risk-alert" style="display: none;">
                <div class="alert-title">风险提示</div>
                <div class="alert-content" id="risk-content">
                    当前市场波动较大，建议谨慎操作
                </div>
            </div>
        </div>

        <!-- 知识图谱网络层 -->
        <div class="layer-card network-layer">
            <div class="layer-title">
                <i class="fas fa-project-diagram layer-icon"></i>
                知识图谱网络
                <span style="font-size: 12px; color: #a0aec0;">(因子关联分析)</span>
            </div>

            <div class="network-graph" id="knowledge-graph"></div>

            <div class="control-buttons">
                <button class="btn btn-primary" onclick="updateKnowledgeGraph()">
                    <i class="fas fa-sync-alt"></i>
                    更新图谱
                </button>
                <button class="btn btn-secondary" onclick="analyzeNetworkCentrality()">
                    <i class="fas fa-search"></i>
                    中心性分析
                </button>
            </div>
        </div>

        <!-- AI学习监控面板 -->
        <div class="layer-card ai-monitor-layer">
            <div class="layer-title">
                <i class="fas fa-robot layer-icon"></i>
                AI学习监控面板
                <span style="font-size: 12px; color: #a0aec0;">(模型性能监控)</span>
            </div>

            <div class="ai-models-grid">
                <div class="model-card">
                    <div class="model-status status-online"></div>
                    <div class="model-name">FinBERT</div>
                    <div class="model-accuracy">准确率: 94.2%</div>
                </div>
                <div class="model-card">
                    <div class="model-status status-online"></div>
                    <div class="model-name">耦合分析</div>
                    <div class="model-accuracy">R²: 0.847</div>
                </div>
                <div class="model-card">
                    <div class="model-status status-warning"></div>
                    <div class="model-name">预测模型</div>
                    <div class="model-accuracy">训练中...</div>
                </div>
            </div>

            <div class="control-buttons">
                <button class="btn btn-primary" onclick="retrainModels()">
                    <i class="fas fa-graduation-cap"></i>
                    重新训练
                </button>
                <button class="btn btn-secondary" onclick="showModelMetrics()">
                    <i class="fas fa-chart-bar"></i>
                    性能指标
                </button>
                <button class="btn btn-secondary" onclick="exportModelResults()">
                    <i class="fas fa-download"></i>
                    导出结果
                </button>
            </div>
        </div>
    </div>

    <script>
        // WebSocket连接
        const socket = io();

        // 连接状态管理
        socket.on('connect', function() {
            document.getElementById('websocket-status').textContent = '已连接';
            document.querySelector('#websocket-status').parentElement.querySelector('.status-dot').className = 'status-dot status-online';
        });

        socket.on('disconnect', function() {
            document.getElementById('websocket-status').textContent = '连接断开';
            document.querySelector('#websocket-status').parentElement.querySelector('.status-dot').className = 'status-dot status-error';
        });

        // 数据更新处理
        socket.on('data_update', function(data) {
            updateDashboard(data);
        });

        // 更新仪表板数据
        function updateDashboard(data) {
            if (data.news_count !== undefined) {
                document.getElementById('news-count').textContent = data.news_count;
            }
            if (data.policy_count !== undefined) {
                document.getElementById('policy-count').textContent = data.policy_count;
            }
            if (data.sentiment_score !== undefined) {
                document.getElementById('sentiment-score').textContent = data.sentiment_score.toFixed(2);
            }
            if (data.coupling_score !== undefined) {
                document.getElementById('coupling-score').textContent = data.coupling_score.toFixed(2);
            }
        }

        // 控制按钮功能
        function triggerDataCollection() {
            socket.emit('trigger_data_collection');
            showNotification('数据收集已启动', 'success');
        }

        function runAIAnalysis() {
            socket.emit('run_ai_analysis');
            showNotification('AI分析已启动', 'success');
        }

        function generateRecommendations() {
            socket.emit('generate_recommendations');
            showNotification('正在生成推荐...', 'info');
        }

        function showNotification(message, type) {
            // 简单的通知实现
            console.log(`${type.toUpperCase()}: ${message}`);
        }

        // 初始化图表
        function initCharts() {
            // 情感分析图表
            const sentimentChart = echarts.init(document.getElementById('sentiment-chart'));
            const sentimentOption = {
                backgroundColor: 'transparent',
                textStyle: { color: '#e2e8f0' },
                xAxis: { type: 'category', data: ['政策', '新闻', '资金', '波动'] },
                yAxis: { type: 'value', min: -1, max: 1 },
                series: [{
                    data: [0.2, 0.5, -0.1, 0.3],
                    type: 'bar',
                    itemStyle: {
                        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            { offset: 0, color: '#4fd1c7' },
                            { offset: 1, color: '#63b3ed' }
                        ])
                    }
                }]
            };
            sentimentChart.setOption(sentimentOption);
        }

        // 知识图谱初始化
        function initKnowledgeGraph() {
            const cy = cytoscape({
                container: document.getElementById('knowledge-graph'),
                elements: [
                    // 节点
                    { data: { id: 'policy', label: '政策' } },
                    { data: { id: 'news', label: '新闻' } },
                    { data: { id: 'fund', label: '资金流' } },
                    { data: { id: 'volatility', label: '波动率' } },
                    // 边
                    { data: { id: 'policy-news', source: 'policy', target: 'news', weight: 0.8 } },
                    { data: { id: 'news-fund', source: 'news', target: 'fund', weight: 0.6 } },
                    { data: { id: 'fund-volatility', source: 'fund', target: 'volatility', weight: 0.7 } },
                    { data: { id: 'policy-fund', source: 'policy', target: 'fund', weight: 0.5 } }
                ],
                style: [
                    {
                        selector: 'node',
                        style: {
                            'background-color': '#4fd1c7',
                            'label': 'data(label)',
                            'color': '#fff',
                            'text-valign': 'center',
                            'text-halign': 'center',
                            'font-size': '12px',
                            'width': 60,
                            'height': 60
                        }
                    },
                    {
                        selector: 'edge',
                        style: {
                            'width': 'mapData(weight, 0, 1, 1, 5)',
                            'line-color': '#63b3ed',
                            'target-arrow-color': '#63b3ed',
                            'target-arrow-shape': 'triangle',
                            'curve-style': 'bezier'
                        }
                    }
                ],
                layout: {
                    name: 'circle',
                    radius: 80
                }
            });

            return cy;
        }

        // 更新知识图谱
        function updateKnowledgeGraph() {
            socket.emit('get_coupling_analysis');
            showNotification('正在更新知识图谱...', 'info');
        }

        // 网络中心性分析
        function analyzeNetworkCentrality() {
            socket.emit('analyze_network_centrality');
            showNotification('正在分析网络中心性...', 'info');
        }

        // AI模型相关功能
        function retrainModels() {
            socket.emit('retrain_models');
            showNotification('模型重训练已启动...', 'info');
        }

        function showModelMetrics() {
            socket.emit('get_model_metrics');
        }

        function exportModelResults() {
            socket.emit('export_model_results');
            showNotification('正在导出模型结果...', 'info');
        }

        function showDataQuality() {
            socket.emit('get_data_quality');
        }

        function showModelDetails() {
            socket.emit('get_model_details');
        }

        function showRiskAnalysis() {
            const riskAlert = document.getElementById('risk-alert');
            riskAlert.style.display = riskAlert.style.display === 'none' ? 'block' : 'none';
        }

        // 推荐列表更新
        function updateRecommendationsList(recommendations) {
            const listContainer = document.getElementById('recommendations-list');
            listContainer.innerHTML = '';

            recommendations.forEach((rec, index) => {
                const recItem = document.createElement('div');
                recItem.className = 'data-stream';
                recItem.innerHTML = `
                    <span class="stream-name">${rec.code} ${rec.name}</span>
                    <div class="stream-status">
                        <span style="color: #4fd1c7; font-weight: bold;">${rec.score.toFixed(3)}</span>
                    </div>
                `;
                listContainer.appendChild(recItem);
            });
        }

        // 监听推荐更新
        socket.on('recommendations_update', function(data) {
            updateRecommendationsList(data.recommendations);
            document.getElementById('recommendation-count').textContent = data.recommendations.length;
            document.getElementById('confidence-score').textContent = data.avg_confidence.toFixed(2);
        });

        // 监听耦合分析结果
        socket.on('coupling_analysis_result', function(data) {
            document.getElementById('coupling-score').textContent = data.coupling_score.total_score.toFixed(2);
            // 更新知识图谱可以在这里实现
        });

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initCharts();
            initKnowledgeGraph();
        });
    </script>
</body>
</html>
