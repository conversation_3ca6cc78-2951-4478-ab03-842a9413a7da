"""
调度器测试

测试任务调度器的基本功能
"""

import os
import sys
import unittest
import json
import logging
import time
from datetime import datetime, timedelta
import threading

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('test_scheduler')

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入被测试的模块
from core.scheduler import CentralScheduler, TaskPriority, TaskStatus
from core.module_interface import ModuleInterface

# 创建测试用的模块接口实现类
class TestModule(ModuleInterface):
    """测试用的模块接口实现类"""
    
    def __init__(self, module_name="test_module", config_path=None):
        """初始化测试模块"""
        super().__init__(module_name, config_path)
        self.initialized = False
        self.test_function_called = False
        self.test_function_params = None
    
    def initialize(self):
        """初始化模块"""
        self.initialized = True
        return True
    
    def get_status(self):
        """获取模块状态"""
        return {
            'module_name': self.module_name,
            'initialized': self.initialized,
            'status': 'running',
            'last_update': datetime.now().isoformat()
        }
    
    def test_function(self, **kwargs):
        """测试函数"""
        self.test_function_called = True
        self.test_function_params = kwargs
        return {
            'status': 'success',
            'message': '测试函数执行成功',
            'params': kwargs
        }
    
    def long_running_function(self, **kwargs):
        """长时间运行的测试函数"""
        time.sleep(2)  # 睡眠2秒
        return {
            'status': 'success',
            'message': '长时间运行的测试函数执行成功',
            'params': kwargs
        }
    
    def failing_function(self, **kwargs):
        """失败的测试函数"""
        raise ValueError("测试函数故意失败")

class TestScheduler(unittest.TestCase):
    """调度器测试类"""
    
    @classmethod
    def setUpClass(cls):
        """测试前的准备工作"""
        logger.info("初始化调度器测试...")
        
        # 创建测试目录
        cls.test_dir = os.path.join('data', 'test_scheduler')
        os.makedirs(cls.test_dir, exist_ok=True)
        
        # 创建配置目录
        os.makedirs('config', exist_ok=True)
        
        # 创建测试配置文件
        cls.config_path = os.path.join('config', 'scheduler_config.json')
        with open(cls.config_path, 'w', encoding='utf-8') as f:
            json.dump({
                'max_workers': 3,
                'task_timeout': 10,
                'retry_count': 3,
                'retry_delay': 1,
                'log_level': 'INFO'
            }, f, indent=4)
        
        logger.info("调度器测试初始化完成")
    
    @classmethod
    def tearDownClass(cls):
        """测试后的清理工作"""
        logger.info("清理调度器测试...")
        
        # 删除测试配置文件
        if os.path.exists(cls.config_path):
            os.remove(cls.config_path)
        
        # 删除测试目录
        import shutil
        if os.path.exists(cls.test_dir):
            shutil.rmtree(cls.test_dir)
        
        logger.info("调度器测试清理完成")
    
    def test_init(self):
        """测试初始化"""
        logger.info("测试调度器初始化...")
        
        # 创建调度器
        scheduler = CentralScheduler(config_path=self.config_path)
        
        # 验证配置
        self.assertIsNotNone(scheduler.config)
        self.assertEqual(scheduler.config.get('max_workers'), 3)
        self.assertEqual(scheduler.config.get('task_timeout'), 10)
        
        logger.info("调度器初始化测试通过")
    
    def test_register_module(self):
        """测试注册模块"""
        logger.info("测试注册模块...")
        
        # 创建调度器
        scheduler = CentralScheduler(config_path=self.config_path)
        
        # 创建测试模块
        module = TestModule()
        
        # 注册模块
        scheduler.register_module(module.module_name, module)
        
        # 验证模块是否已注册
        self.assertIn(module.module_name, scheduler.modules)
        self.assertEqual(scheduler.modules[module.module_name], module)
        
        logger.info("注册模块测试通过")
    
    def test_schedule_task(self):
        """测试调度任务"""
        logger.info("测试调度任务...")
        
        # 创建调度器
        scheduler = CentralScheduler(config_path=self.config_path)
        
        # 创建测试模块
        module = TestModule()
        
        # 注册模块
        scheduler.register_module(module.module_name, module)
        
        # 调度任务
        task_id = scheduler.schedule_task(
            task_type='test',
            module=module.module_name,
            function='test_function',
            params={'param1': 'value1', 'param2': 'value2'},
            priority=TaskPriority.HIGH
        )
        
        # 验证任务是否已添加
        self.assertIn(task_id, scheduler.tasks)
        task = scheduler.tasks[task_id]
        self.assertEqual(task.task_type, 'test')
        self.assertEqual(task.module, module.module_name)
        self.assertEqual(task.function, 'test_function')
        self.assertEqual(task.params, {'param1': 'value1', 'param2': 'value2'})
        self.assertEqual(task.priority, TaskPriority.HIGH)
        self.assertEqual(task.status, TaskStatus.PENDING)
        
        logger.info("调度任务测试通过")
    
    def test_execute_task(self):
        """测试执行任务"""
        logger.info("测试执行任务...")
        
        # 创建调度器
        scheduler = CentralScheduler(config_path=self.config_path)
        
        # 创建测试模块
        module = TestModule()
        
        # 注册模块
        scheduler.register_module(module.module_name, module)
        
        # 调度任务
        task_id = scheduler.schedule_task(
            task_type='test',
            module=module.module_name,
            function='test_function',
            params={'param1': 'value1', 'param2': 'value2'},
            priority=TaskPriority.HIGH
        )
        
        # 执行任务
        result = scheduler.execute_task(task_id)
        
        # 验证任务执行结果
        self.assertTrue(result)
        self.assertTrue(module.test_function_called)
        self.assertEqual(module.test_function_params, {'param1': 'value1', 'param2': 'value2'})
        
        # 验证任务状态
        task = scheduler.tasks[task_id]
        self.assertEqual(task.status, TaskStatus.COMPLETED)
        
        logger.info("执行任务测试通过")
    
    def test_task_timeout(self):
        """测试任务超时"""
        logger.info("测试任务超时...")
        
        # 创建调度器（设置较短的超时时间）
        scheduler = CentralScheduler(config_path=self.config_path)
        scheduler.config['task_timeout'] = 1  # 设置超时时间为1秒
        
        # 创建测试模块
        module = TestModule()
        
        # 注册模块
        scheduler.register_module(module.module_name, module)
        
        # 调度长时间运行的任务
        task_id = scheduler.schedule_task(
            task_type='test',
            module=module.module_name,
            function='long_running_function',
            params={},
            priority=TaskPriority.MEDIUM,
            timeout=1  # 设置超时时间为1秒
        )
        
        # 执行任务
        result = scheduler.execute_task(task_id)
        
        # 验证任务执行结果（应该超时）
        self.assertFalse(result)
        
        # 验证任务状态
        task = scheduler.tasks[task_id]
        self.assertEqual(task.status, TaskStatus.TIMEOUT)
        
        logger.info("任务超时测试通过")
    
    def test_task_failure(self):
        """测试任务失败"""
        logger.info("测试任务失败...")
        
        # 创建调度器
        scheduler = CentralScheduler(config_path=self.config_path)
        
        # 创建测试模块
        module = TestModule()
        
        # 注册模块
        scheduler.register_module(module.module_name, module)
        
        # 调度会失败的任务
        task_id = scheduler.schedule_task(
            task_type='test',
            module=module.module_name,
            function='failing_function',
            params={},
            priority=TaskPriority.LOW
        )
        
        # 执行任务
        result = scheduler.execute_task(task_id)
        
        # 验证任务执行结果（应该失败）
        self.assertFalse(result)
        
        # 验证任务状态
        task = scheduler.tasks[task_id]
        self.assertEqual(task.status, TaskStatus.FAILED)
        
        logger.info("任务失败测试通过")
    
    def test_run_scheduler(self):
        """测试运行调度器"""
        logger.info("测试运行调度器...")
        
        # 创建调度器
        scheduler = CentralScheduler(config_path=self.config_path)
        
        # 创建测试模块
        module = TestModule()
        
        # 注册模块
        scheduler.register_module(module.module_name, module)
        
        # 调度任务
        scheduler.schedule_task(
            task_type='test',
            module=module.module_name,
            function='test_function',
            params={'param1': 'value1'},
            priority=TaskPriority.HIGH
        )
        
        # 启动调度器
        scheduler.start()
        
        # 等待任务执行完成
        time.sleep(2)
        
        # 停止调度器
        scheduler.stop()
        
        # 验证任务是否已执行
        self.assertTrue(module.test_function_called)
        self.assertEqual(module.test_function_params, {'param1': 'value1'})
        
        logger.info("运行调度器测试通过")

if __name__ == '__main__':
    unittest.main()
