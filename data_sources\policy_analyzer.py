"""
政策三层解析框架

实现政策数据的精准解析和影响评估
1. 顶层架构识别：使用BERT+CRF模型对政策文件进行实体识别
2. 行业影响矩阵：构建申万行业-政策敏感度矩阵
3. 时间衰减因子：引入指数衰减模型
"""

import os
import logging
import json
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import time
import re
from typing import Dict, List, Any, Optional, Union, Tuple
import math

# 导入模块接口
from core.module_interface import ModuleInterface

# 导入数据存储
from core.data_storage import DataStorage, StorageLevel

# 导入数据源
from data_sources.policy_data import PolicyDataSource

# 尝试导入NLP相关库
try:
    import torch
    import transformers
    from transformers import AutoTokenizer, AutoModel
    NLP_AVAILABLE = True
except ImportError:
    NLP_AVAILABLE = False
    logging.warning("NLP相关库未安装，将使用简化版政策解析")

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/policy_analyzer.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('policy_analyzer')

class PolicyAnalyzer(ModuleInterface):
    """政策三层解析框架类"""

    def __init__(self):
        """初始化政策解析器"""
        super().__init__(module_name='policy_analyzer')

        # 创建数据源
        self.policy_data = PolicyDataSource()

        # 创建数据存储
        self.data_storage = DataStorage()

        # 上次更新时间
        self.last_update_time = datetime.now()

        # 政策实体识别模型
        self.entity_model = None

        # 行业影响矩阵
        self.industry_impact_matrix = self._load_industry_impact_matrix()

        # 政策类型衰减因子
        self.policy_decay_factors = self._load_policy_decay_factors()

        # 已解析的政策
        self.parsed_policies = self.data_storage.load('policy_analyzer', 'parsed_policies', [])

        # 初始化NLP模型
        if NLP_AVAILABLE:
            self._init_nlp_models()

        logger.info("政策解析器初始化完成")

    def _init_nlp_models(self):
        """初始化NLP模型"""
        try:
            # 使用金融领域预训练模型
            # 优先使用FinBERT，如果失败则尝试DistilRoBERTa-Financial
            try:
                model_name = "ProsusAI/finbert"
                self.tokenizer = AutoTokenizer.from_pretrained(model_name)
                self.bert_model = AutoModel.from_pretrained(model_name)
                logger.info(f"成功加载FinBERT模型")
            except Exception as e:
                logger.warning(f"加载FinBERT模型失败，尝试加载DistilRoBERTa-Financial: {str(e)}")
                model_name = "mrm8488/distilroberta-finetuned-financial-news-sentiment-analysis"
                self.tokenizer = AutoTokenizer.from_pretrained(model_name)
                self.bert_model = AutoModel.from_pretrained(model_name)
                logger.info(f"成功加载DistilRoBERTa-Financial模型")

            # 设置为评估模式
            self.bert_model.eval()

            # 如果有GPU，使用GPU
            if torch.cuda.is_available():
                self.bert_model = self.bert_model.cuda()
                logger.info("使用GPU进行NLP处理")

            logger.info(f"NLP模型初始化成功: {model_name}")

        except Exception as e:
            logger.error(f"NLP模型初始化失败: {str(e)}")
            self.tokenizer = None
            self.bert_model = None

            # 使用简化版处理方式
            logger.info("将使用简化版政策解析方法")

    def _load_industry_impact_matrix(self) -> Dict[str, Dict[str, float]]:
        """
        加载行业影响矩阵

        Returns:
            matrix: 行业影响矩阵
        """
        # 尝试从数据存储加载
        matrix = self.data_storage.load('policy_analyzer', 'industry_impact_matrix')
        if matrix:
            logger.info(f"从数据存储加载了行业影响矩阵")
            return matrix

        # 如果数据存储中没有，尝试从文件加载
        try:
            matrix_file = os.path.join('data', 'policy_analyzer', 'industry_impact_matrix.json')
            if os.path.exists(matrix_file):
                with open(matrix_file, 'r', encoding='utf-8') as f:
                    matrix = json.load(f)
                logger.info(f"从文件加载了行业影响矩阵")

                # 保存到数据存储
                self.data_storage.save('policy_analyzer', 'industry_impact_matrix', matrix)

                return matrix
        except Exception as e:
            logger.error(f"加载行业影响矩阵文件失败: {str(e)}")

        # 如果都没有，创建默认矩阵
        # 这里使用申万一级行业分类
        industries = [
            '农林牧渔', '采掘', '化工', '钢铁', '有色金属',
            '电子', '家用电器', '食品饮料', '纺织服装', '轻工制造',
            '医药生物', '公用事业', '交通运输', '房地产', '商业贸易',
            '休闲服务', '银行', '非银金融', '汽车', '机械设备',
            '建筑材料', '建筑装饰', '电气设备', '国防军工', '计算机',
            '传媒', '通信', '综合'
        ]

        # 政策类型
        policy_types = [
            '货币政策', '财政政策', '产业政策', '监管政策', '外贸政策',
            '科技政策', '环保政策', '医疗政策', '教育政策', '房地产政策',
            '就业政策', '社保政策', '税收政策', '金融政策', '能源政策'
        ]

        # 创建默认矩阵
        matrix = {}
        for policy_type in policy_types:
            matrix[policy_type] = {}
            for industry in industries:
                # 默认影响系数为0.5
                matrix[policy_type][industry] = 0.5

        # 设置一些特定的影响系数
        # 货币政策对金融业的影响较大
        matrix['货币政策']['银行'] = 0.9
        matrix['货币政策']['非银金融'] = 0.8
        matrix['货币政策']['房地产'] = 0.7

        # 财政政策对基建相关行业的影响较大
        matrix['财政政策']['建筑材料'] = 0.8
        matrix['财政政策']['建筑装饰'] = 0.8
        matrix['财政政策']['机械设备'] = 0.7

        # 产业政策对相关行业的影响较大
        matrix['产业政策']['电子'] = 0.8
        matrix['产业政策']['计算机'] = 0.8
        matrix['产业政策']['通信'] = 0.8
        matrix['产业政策']['电气设备'] = 0.7

        # 环保政策对高污染行业的影响较大
        matrix['环保政策']['钢铁'] = 0.9
        matrix['环保政策']['有色金属'] = 0.8
        matrix['环保政策']['化工'] = 0.8
        matrix['环保政策']['采掘'] = 0.8

        # 医疗政策对医药行业的影响较大
        matrix['医疗政策']['医药生物'] = 0.9

        # 房地产政策对相关行业的影响较大
        matrix['房地产政策']['房地产'] = 0.9
        matrix['房地产政策']['建筑材料'] = 0.7
        matrix['房地产政策']['家用电器'] = 0.6

        # 保存到数据存储
        self.data_storage.save('policy_analyzer', 'industry_impact_matrix', matrix)

        logger.info(f"创建默认行业影响矩阵")
        return matrix

    def _load_policy_decay_factors(self) -> Dict[str, float]:
        """
        加载政策衰减因子

        Returns:
            factors: 政策衰减因子
        """
        # 尝试从数据存储加载
        factors = self.data_storage.load('policy_analyzer', 'policy_decay_factors')
        if factors:
            logger.info(f"从数据存储加载了政策衰减因子")
            return factors

        # 如果数据存储中没有，尝试从文件加载
        try:
            factors_file = os.path.join('data', 'policy_analyzer', 'policy_decay_factors.json')
            if os.path.exists(factors_file):
                with open(factors_file, 'r', encoding='utf-8') as f:
                    factors = json.load(f)
                logger.info(f"从文件加载了政策衰减因子")

                # 保存到数据存储
                self.data_storage.save('policy_analyzer', 'policy_decay_factors', factors)

                return factors
        except Exception as e:
            logger.error(f"加载政策衰减因子文件失败: {str(e)}")

        # 如果都没有，创建默认因子
        # 政策类型
        policy_types = [
            '货币政策', '财政政策', '产业政策', '监管政策', '外贸政策',
            '科技政策', '环保政策', '医疗政策', '教育政策', '房地产政策',
            '就业政策', '社保政策', '税收政策', '金融政策', '能源政策'
        ]

        # 创建默认因子
        factors = {}
        for policy_type in policy_types:
            # 默认衰减因子为0.05
            factors[policy_type] = 0.05

        # 设置一些特定的衰减因子
        # 长期规划类政策衰减较慢
        factors['产业政策'] = 0.01
        factors['科技政策'] = 0.01
        factors['教育政策'] = 0.01

        # 短期流动性政策衰减较快
        factors['货币政策'] = 0.1
        factors['财政政策'] = 0.08

        # 保存到数据存储
        self.data_storage.save('policy_analyzer', 'policy_decay_factors', factors)

        logger.info(f"创建默认政策衰减因子")
        return factors

    def initialize(self):
        """初始化模块"""
        logger.info("初始化政策解析器模块...")

        # 注册定时任务
        if self.scheduler:
            # 每天获取一次政策数据
            self.schedule_task(
                function='fetch_and_parse_policies',
                params={},
                priority='medium',
                schedule_time=datetime.now() + timedelta(minutes=5)
            )

            # 每周更新一次行业影响矩阵
            self.schedule_task(
                function='update_industry_impact_matrix',
                params={},
                priority='low',
                schedule_time=datetime.now() + timedelta(days=7)
            )

        logger.info("政策解析器模块初始化完成")

    def get_status(self) -> Dict[str, Any]:
        """获取模块状态"""
        return {
            'module_name': self.module_name,
            'enabled': self.config.get('enabled', True),
            'last_update_time': self.last_update_time.isoformat() if self.last_update_time else None,
            'parsed_policies_count': len(self.parsed_policies),
            'nlp_available': NLP_AVAILABLE
        }

    def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        try:
            # 检查上次更新时间
            time_since_last_update = datetime.now() - self.last_update_time

            if time_since_last_update > timedelta(days=2):
                status = 'warning'
                message = f'上次更新时间超过2天: {self.last_update_time.isoformat()}'
            else:
                status = 'healthy'
                message = f'上次更新时间: {self.last_update_time.isoformat()}'

            return {
                'status': status,
                'message': message,
                'last_update_time': self.last_update_time.isoformat(),
                'time_since_last_update': str(time_since_last_update),
                'parsed_policies_count': len(self.parsed_policies),
                'nlp_available': NLP_AVAILABLE
            }

        except Exception as e:
            logger.error(f"健康检查失败: {str(e)}")
            return {
                'status': 'error',
                'message': f'健康检查失败: {str(e)}',
                'error': str(e)
            }

    def fetch_and_parse_policies(self, **kwargs):
        """获取并解析政策"""
        try:
            logger.info("开始获取并解析政策...")

            # 记录当前时间
            current_time = datetime.now()

            # 获取政策数据
            policies = self._fetch_policies()

            if not policies:
                logger.warning("没有获取到政策")
                return {
                    'status': 'warning',
                    'message': '没有获取到政策',
                    'count': 0
                }

            # 解析政策
            parsed_policies = self._parse_policies(policies)

            # 更新已解析的政策
            self.parsed_policies.extend(parsed_policies)

            # 保存已解析的政策
            self.data_storage.save('policy_analyzer', 'parsed_policies', self.parsed_policies, StorageLevel.WARM)

            # 更新上次更新时间
            self.last_update_time = current_time

            logger.info(f"政策获取和解析成功，共{len(parsed_policies)}条")

            return {
                'status': 'success',
                'message': f'政策获取和解析成功，共{len(parsed_policies)}条',
                'count': len(parsed_policies)
            }

        except Exception as e:
            logger.error(f"获取并解析政策失败: {str(e)}")
            return {
                'status': 'error',
                'message': f'获取并解析政策失败: {str(e)}',
                'error': str(e)
            }

    def _fetch_policies(self) -> List[Dict[str, Any]]:
        """
        获取政策数据

        Returns:
            policies: 政策数据列表
        """
        all_policies = []

        try:
            # 获取国务院政策
            gov_policies = self.policy_data.get_gov_policy(page=1, limit=50)
            if not gov_policies.empty:
                for _, row in gov_policies.iterrows():
                    policy = row.to_dict()
                    policy['source'] = '国务院'
                    all_policies.append(policy)

            # 获取发改委政策
            ndrc_policies = self.policy_data.get_ndrc_policy(page=1, limit=50)
            if not ndrc_policies.empty:
                for _, row in ndrc_policies.iterrows():
                    policy = row.to_dict()
                    policy['source'] = '发改委'
                    all_policies.append(policy)

            logger.info(f"获取到{len(all_policies)}条政策")
            return all_policies

        except Exception as e:
            logger.error(f"获取政策数据失败: {str(e)}")
            return []

    def _parse_policies(self, policies: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        解析政策

        Args:
            policies: 政策数据列表

        Returns:
            parsed_policies: 解析后的政策列表
        """
        parsed_policies = []

        for policy in policies:
            try:
                # 检查是否已经解析过
                if any(p.get('id') == policy.get('id') for p in self.parsed_policies):
                    continue

                # 解析政策
                parsed_policy = self._parse_policy(policy)

                # 添加到解析后的政策列表
                parsed_policies.append(parsed_policy)

            except Exception as e:
                logger.error(f"解析政策失败: {str(e)}, 政策: {policy.get('title', '')}")

        logger.info(f"解析了{len(parsed_policies)}条政策")
        return parsed_policies

    def _parse_policy(self, policy: Dict[str, Any]) -> Dict[str, Any]:
        """
        解析单个政策

        Args:
            policy: 政策数据

        Returns:
            parsed_policy: 解析后的政策
        """
        # 复制原始政策数据
        parsed_policy = policy.copy()

        # 添加解析时间
        parsed_policy['parsed_at'] = datetime.now().isoformat()

        # 提取政策主体、动作和影响对象
        entities = self._extract_policy_entities(policy)
        parsed_policy.update(entities)

        # 计算行业影响
        industry_impacts = self._calculate_industry_impacts(parsed_policy)
        parsed_policy['industry_impacts'] = industry_impacts

        # 计算时间衰减因子
        decay_factor = self._get_decay_factor(parsed_policy)
        parsed_policy['decay_factor'] = decay_factor

        return parsed_policy

    def _extract_policy_entities(self, policy: Dict[str, Any]) -> Dict[str, Any]:
        """
        提取政策实体（主体、动作、影响对象）

        Args:
            policy: 政策数据

        Returns:
            entities: 提取的实体
        """
        entities = {
            'subject': '',  # 政策主体
            'action': '',   # 政策动作
            'object': '',   # 影响对象
            'policy_type': ''  # 政策类型
        }

        title = policy.get('title', '')
        content = policy.get('content', '')
        source = policy.get('source', '')

        # 设置默认政策主体
        entities['subject'] = source

        # 如果有NLP模型，使用模型提取实体
        if NLP_AVAILABLE and self.bert_model and self.tokenizer:
            try:
                # 使用预训练模型提取特征
                # 这里是简化版，实际应该使用更复杂的实体识别模型
                encoded_input = self.tokenizer(title, return_tensors='pt', truncation=True, max_length=512)
                with torch.no_grad():
                    model_output = self.bert_model(**encoded_input)
                    # 获取[CLS]标记的输出作为整个句子的表示
                    sentence_embedding = model_output.last_hidden_state[:, 0, :].cpu().numpy()

                # 使用简单的规则来确定政策类型
                # 实际应用中应该使用更复杂的分类模型
                logger.info(f"成功使用NLP模型处理政策标题: {title[:30]}...")

            except Exception as e:
                logger.error(f"使用NLP模型提取实体失败: {str(e)}")

        # 使用规则匹配提取政策类型
        policy_type = self._identify_policy_type(title, content)
        entities['policy_type'] = policy_type

        # 使用规则匹配提取政策动作
        action = self._identify_policy_action(title, content)
        entities['action'] = action

        # 使用规则匹配提取影响对象
        obj = self._identify_policy_object(title, content)
        entities['object'] = obj

        return entities

    def _identify_policy_type(self, title: str, content: str) -> str:
        """
        识别政策类型

        Args:
            title: 政策标题
            content: 政策内容

        Returns:
            policy_type: 政策类型
        """
        # 政策类型关键词映射
        type_keywords = {
            '货币政策': ['货币', '利率', '降息', '降准', '央行', '流动性', '存款准备金', 'MLF', 'LPR', '公开市场操作'],
            '财政政策': ['财政', '减税', '降费', '补贴', '专项债', '赤字', '预算', '政府支出', '财政支出'],
            '产业政策': ['产业', '行业', '发展规划', '五年规划', '中长期', '战略性', '新兴产业', '转型升级'],
            '监管政策': ['监管', '合规', '整治', '规范', '风险防控', '风险管理', '监督', '检查', '巡查'],
            '外贸政策': ['外贸', '进出口', '关税', '贸易', '外商投资', '自贸区', '跨境', '海关'],
            '科技政策': ['科技', '创新', '研发', '知识产权', '专利', '高新技术', '数字化', '智能化'],
            '环保政策': ['环保', '碳中和', '碳达峰', '减排', '节能', '污染', '生态', '绿色', '可持续'],
            '医疗政策': ['医疗', '卫生', '健康', '医保', '药品', '疫苗', '医院', '诊所'],
            '教育政策': ['教育', '学校', '学生', '教师', '课程', '教学', '培训', '学历'],
            '房地产政策': ['房地产', '楼市', '住房', '地产', '购房', '限购', '房贷', '按揭', '租赁'],
            '就业政策': ['就业', '创业', '人才', '劳动力', '职业', '技能', '培训', '失业'],
            '社保政策': ['社保', '养老', '医保', '失业保险', '工伤', '生育', '社会保障'],
            '税收政策': ['税收', '增值税', '所得税', '企业所得税', '个人所得税', '税率', '税基', '减税'],
            '金融政策': ['金融', '银行', '证券', '保险', '基金', '信托', '理财', '资本市场'],
            '能源政策': ['能源', '电力', '石油', '天然气', '煤炭', '新能源', '可再生能源']
        }

        # 合并标题和内容
        text = f"{title} {content}"

        # 计算每种政策类型的匹配分数
        type_scores = {}
        for policy_type, keywords in type_keywords.items():
            score = 0
            for keyword in keywords:
                if keyword in text:
                    score += 1
            type_scores[policy_type] = score

        # 选择得分最高的政策类型
        if type_scores:
            max_type = max(type_scores.items(), key=lambda x: x[1])
            if max_type[1] > 0:
                return max_type[0]

        # 默认为产业政策
        return '产业政策'

    def _identify_policy_action(self, title: str, content: str) -> str:
        """
        识别政策动作

        Args:
            title: 政策标题
            content: 政策内容

        Returns:
            action: 政策动作
        """
        # 政策动作关键词
        action_keywords = {
            '支持': ['支持', '鼓励', '促进', '推动', '加快', '提升', '增强', '优化'],
            '限制': ['限制', '控制', '约束', '规范', '整治', '整顿', '调控'],
            '禁止': ['禁止', '禁止', '取缔', '打击', '严厉打击', '严禁'],
            '改革': ['改革', '创新', '转型', '升级', '调整', '重组', '优化'],
            '规划': ['规划', '计划', '部署', '安排', '布局', '战略'],
            '监管': ['监管', '监督', '检查', '巡查', '审查', '核查', '评估'],
            '减税降费': ['减税', '降费', '减免', '优惠', '补贴', '奖励', '激励'],
            '投资': ['投资', '建设', '发展', '扩大', '增加', '提高'],
            '保障': ['保障', '保护', '维护', '确保', '稳定', '安全']
        }

        # 合并标题和内容
        text = f"{title} {content}"

        # 计算每种动作的匹配分数
        action_scores = {}
        for action, keywords in action_keywords.items():
            score = 0
            for keyword in keywords:
                if keyword in text:
                    score += 1
            action_scores[action] = score

        # 选择得分最高的动作
        if action_scores:
            max_action = max(action_scores.items(), key=lambda x: x[1])
            if max_action[1] > 0:
                return max_action[0]

        # 从标题中提取动词
        # 这里是简化版，实际应该使用分词和词性标注
        for word in ['推动', '促进', '加强', '实施', '推进', '加快', '深化', '开展', '建设', '发展']:
            if word in title:
                return word

        # 默认为支持
        return '支持'

    def _identify_policy_object(self, title: str, content: str) -> str:
        """
        识别政策影响对象

        Args:
            title: 政策标题
            content: 政策内容

        Returns:
            object: 政策影响对象
        """
        # 行业关键词
        industry_keywords = {
            '农林牧渔': ['农业', '林业', '牧业', '渔业', '种植', '养殖', '农产品', '粮食'],
            '采掘': ['采矿', '矿业', '煤炭', '石油', '天然气', '开采'],
            '化工': ['化工', '化学', '化肥', '农药', '塑料', '橡胶', '涂料'],
            '钢铁': ['钢铁', '钢材', '铁矿', '冶金'],
            '有色金属': ['有色金属', '铜', '铝', '铅', '锌', '金', '银'],
            '电子': ['电子', '半导体', '集成电路', '芯片', '显示', '面板'],
            '家用电器': ['家电', '电器', '冰箱', '洗衣机', '空调', '电视'],
            '食品饮料': ['食品', '饮料', '酒', '乳业', '调味品'],
            '纺织服装': ['纺织', '服装', '服饰', '鞋', '皮革'],
            '医药生物': ['医药', '生物', '药品', '疫苗', '医疗器械'],
            '公用事业': ['公用事业', '水务', '燃气', '电力', '热力'],
            '交通运输': ['交通', '运输', '航空', '航运', '铁路', '公路', '港口'],
            '房地产': ['房地产', '地产', '楼市', '住宅', '商业地产'],
            '银行': ['银行', '存款', '贷款', '理财'],
            '非银金融': ['证券', '保险', '基金', '信托', '期货'],
            '汽车': ['汽车', '乘用车', '商用车', '新能源汽车', '充电桩'],
            '机械设备': ['机械', '设备', '工程机械', '专用设备', '通用设备'],
            '电气设备': ['电气', '电力设备', '输配电', '电网'],
            '国防军工': ['国防', '军工', '航天', '航空', '船舶', '兵器'],
            '计算机': ['计算机', '软件', '信息技术', 'IT', '互联网', '云计算', '大数据'],
            '通信': ['通信', '电信', '移动通信', '5G', '6G', '光通信'],
            '传媒': ['传媒', '媒体', '出版', '影视', '广告', '文化'],
            '社会服务': ['社会服务', '教育', '医疗', '养老', '旅游', '餐饮', '酒店']
        }

        # 合并标题和内容
        text = f"{title} {content}"

        # 计算每个行业的匹配分数
        industry_scores = {}
        for industry, keywords in industry_keywords.items():
            score = 0
            for keyword in keywords:
                if keyword in text:
                    score += 1
            industry_scores[industry] = score

        # 选择得分最高的行业
        if industry_scores:
            max_industry = max(industry_scores.items(), key=lambda x: x[1])
            if max_industry[1] > 0:
                return max_industry[0]

        # 如果没有匹配到行业，尝试从标题中提取名词
        # 这里是简化版，实际应该使用分词和词性标注
        for word in ['经济', '产业', '企业', '市场', '发展', '建设', '改革', '创新']:
            if word in title:
                return word

        # 默认为经济
        return '经济'

    def _calculate_industry_impacts(self, policy: Dict[str, Any]) -> Dict[str, float]:
        """
        计算政策对各行业的影响

        Args:
            policy: 解析后的政策

        Returns:
            impacts: 行业影响字典
        """
        policy_type = policy.get('policy_type', '产业政策')
        policy_object = policy.get('object', '经济')

        # 获取该政策类型的行业影响矩阵
        type_matrix = self.industry_impact_matrix.get(policy_type, {})

        # 计算每个行业的影响分数
        impacts = {}
        for industry, base_impact in type_matrix.items():
            # 基础影响分数
            impact = base_impact

            # 如果政策对象是该行业，增加影响
            if policy_object == industry:
                impact *= 2.0

            # 保存影响分数
            impacts[industry] = impact

        return impacts

    def _get_decay_factor(self, policy: Dict[str, Any]) -> float:
        """
        获取政策的时间衰减因子

        Args:
            policy: 解析后的政策

        Returns:
            decay_factor: 时间衰减因子
        """
        policy_type = policy.get('policy_type', '产业政策')

        # 获取该政策类型的衰减因子
        decay_factor = self.policy_decay_factors.get(policy_type, 0.05)

        return decay_factor

    def update_industry_impact_matrix(self, **kwargs):
        """更新行业影响矩阵"""
        try:
            logger.info("开始更新行业影响矩阵...")

            # 这里应该有更复杂的逻辑来更新矩阵
            # 例如基于历史数据分析政策对行业的实际影响
            # 简化版只是保存当前矩阵

            self.data_storage.save('policy_analyzer', 'industry_impact_matrix', self.industry_impact_matrix, StorageLevel.WARM)

            logger.info("行业影响矩阵更新成功")

            return {
                'status': 'success',
                'message': '行业影响矩阵更新成功'
            }

        except Exception as e:
            logger.error(f"更新行业影响矩阵失败: {str(e)}")
            return {
                'status': 'error',
                'message': f'更新行业影响矩阵失败: {str(e)}',
                'error': str(e)
            }

    def calculate_policy_heat(self, policy_id: str = None, days: int = 30) -> Dict[str, Any]:
        """
        计算政策热度

        Args:
            policy_id: 政策ID，如果为None则计算所有政策
            days: 时间窗口（天）

        Returns:
            heat: 政策热度信息
        """
        try:
            logger.info(f"开始计算政策热度，时间窗口: {days}天")

            # 获取时间窗口内的政策
            cutoff_date = datetime.now() - timedelta(days=days)

            # 过滤政策
            if policy_id:
                policies = [p for p in self.parsed_policies if p.get('id') == policy_id]
            else:
                policies = [p for p in self.parsed_policies if
                           p.get('parsed_at') and
                           datetime.fromisoformat(p['parsed_at']) > cutoff_date]

            if not policies:
                logger.warning("没有找到符合条件的政策")
                return {
                    'status': 'warning',
                    'message': '没有找到符合条件的政策',
                    'heat': {}
                }

            # 计算每个政策的热度
            policy_heats = []
            for policy in policies:
                heat = self._calculate_single_policy_heat(policy)
                policy_heats.append({
                    'id': policy.get('id'),
                    'title': policy.get('title'),
                    'source': policy.get('source'),
                    'policy_type': policy.get('policy_type'),
                    'publish_date': policy.get('publish_date'),
                    'heat': heat
                })

            # 按热度排序
            policy_heats = sorted(policy_heats, key=lambda x: x['heat'], reverse=True)

            # 计算行业热度
            industry_heats = self._calculate_industry_heats(policies)

            logger.info(f"政策热度计算完成，共{len(policy_heats)}条政策")

            return {
                'status': 'success',
                'message': f'政策热度计算完成，共{len(policy_heats)}条政策',
                'policy_heats': policy_heats,
                'industry_heats': industry_heats
            }

        except Exception as e:
            logger.error(f"计算政策热度失败: {str(e)}")
            return {
                'status': 'error',
                'message': f'计算政策热度失败: {str(e)}',
                'error': str(e)
            }

    def _calculate_single_policy_heat(self, policy: Dict[str, Any]) -> float:
        """
        计算单个政策的热度

        Args:
            policy: 政策数据

        Returns:
            heat: 政策热度
        """
        # 基础热度
        base_heat = 1.0

        # 政策来源权重
        source_weights = {
            '国务院': 2.0,
            '发改委': 1.8,
            '证监会': 1.8,
            '央行': 1.8,
            '财政部': 1.8,
            '工信部': 1.6,
            '商务部': 1.6,
            '其他': 1.0
        }

        source = policy.get('source', '其他')
        source_weight = source_weights.get(source, source_weights['其他'])

        # 政策类型权重
        type_weights = {
            '货币政策': 2.0,
            '财政政策': 1.8,
            '产业政策': 1.6,
            '监管政策': 1.5,
            '其他': 1.0
        }

        policy_type = policy.get('policy_type', '其他')
        type_weight = type_weights.get(policy_type, type_weights['其他'])

        # 时间衰减
        publish_date = policy.get('publish_date')
        if publish_date:
            try:
                publish_time = datetime.fromisoformat(publish_date)
                time_diff = (datetime.now() - publish_time).total_seconds() / 86400  # 天数

                # 获取衰减因子
                decay_factor = policy.get('decay_factor', 0.05)

                # 计算时间衰减
                time_decay = math.exp(-decay_factor * time_diff)
            except:
                time_decay = 1.0
        else:
            time_decay = 1.0

        # 计算最终热度
        heat = base_heat * source_weight * type_weight * time_decay

        return heat

    def _calculate_industry_heats(self, policies: List[Dict[str, Any]]) -> Dict[str, float]:
        """
        计算行业热度

        Args:
            policies: 政策列表

        Returns:
            industry_heats: 行业热度字典
        """
        industry_heats = {}

        for policy in policies:
            # 获取政策热度
            policy_heat = self._calculate_single_policy_heat(policy)

            # 获取行业影响
            industry_impacts = policy.get('industry_impacts', {})

            # 累加行业热度
            for industry, impact in industry_impacts.items():
                if industry not in industry_heats:
                    industry_heats[industry] = 0

                industry_heats[industry] += policy_heat * impact

        return industry_heats
