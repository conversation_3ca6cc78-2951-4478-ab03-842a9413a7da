import unittest
from unittest.mock import MagicMock, patch
import sys
import os

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

# 模拟 tkinter 模块
mock_tk = MagicMock()
mock_ttk = MagicMock()
mock_scrolledtext = MagicMock()
mock_messagebox = MagicMock()
mock_filedialog = MagicMock()

# 创建模拟的 tkinter 模块
sys.modules['tkinter'] = mock_tk
sys.modules['tkinter.ttk'] = mock_ttk
sys.modules['tkinter.scrolledtext'] = mock_scrolledtext
sys.modules['tkinter.messagebox'] = mock_messagebox
sys.modules['tkinter.filedialog'] = mock_filedialog

# 导入被测模块
from core.data_storage import DataStorage
from core.scheduler import CentralScheduler as Scheduler
from data_sources.policy_analyzer import PolicyAnalyzer
from data_sources.fund_flow_analyzer import FundFlowAnalyzer
from data_sources.volatility_analyzer import VolatilityAnalyzer

class SimpleSystemTest(unittest.TestCase):
    """简单系统测试"""

    def test_data_storage(self):
        """测试数据存储模块"""
        # 创建数据存储实例
        data_storage = DataStorage()

        # 保存和加载数据
        test_data = {'test_key': 'test_value'}
        data_storage.save('test_module', 'test_data', test_data)
        loaded_data = data_storage.load('test_module', 'test_data')

        # 验证数据
        self.assertEqual(loaded_data, test_data)

    def test_scheduler(self):
        """测试调度器模块"""
        # 创建调度器实例
        scheduler = Scheduler()

        # 验证调度器已创建
        self.assertIsNotNone(scheduler)

        # 验证调度器方法存在 - 使用实际存在的方法
        self.assertTrue(hasattr(scheduler, 'schedule_task'))
        self.assertTrue(hasattr(scheduler, 'cancel_task'))
        self.assertTrue(hasattr(scheduler, 'get_task_status'))  # 使用正确的方法名

    def test_policy_analyzer_init(self):
        """测试政策分析模块初始化"""
        try:
            # 创建政策分析模块实例
            policy_analyzer = PolicyAnalyzer()

            # 验证模块已创建
            self.assertIsNotNone(policy_analyzer)

            # 验证模块方法存在
            self.assertTrue(hasattr(policy_analyzer, 'fetch_and_parse_policies'))
        except Exception as e:
            self.fail(f"PolicyAnalyzer 初始化失败: {str(e)}")

    def test_fund_flow_analyzer_init(self):
        """测试资金流分析模块初始化"""
        try:
            # 创建资金流分析模块实例
            fund_flow_analyzer = FundFlowAnalyzer()

            # 验证模块已创建
            self.assertIsNotNone(fund_flow_analyzer)

            # 验证模块方法存在 - 使用实际存在的方法
            self.assertTrue(hasattr(fund_flow_analyzer, 'fetch_cross_border_flow'))
        except Exception as e:
            self.fail(f"FundFlowAnalyzer 初始化失败: {str(e)}")

    def test_volatility_analyzer_init(self):
        """测试波动率分析模块初始化"""
        try:
            # 创建波动率分析模块实例
            volatility_analyzer = VolatilityAnalyzer()

            # 验证模块已创建
            self.assertIsNotNone(volatility_analyzer)

            # 验证模块方法存在
            self.assertTrue(hasattr(volatility_analyzer, 'calculate_market_volatility'))
        except Exception as e:
            self.fail(f"VolatilityAnalyzer 初始化失败: {str(e)}")

if __name__ == '__main__':
    unittest.main()
