#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
系统初始化脚本

用于初始化系统环境，创建必要的目录和配置文件
"""

import os
import sys
import json
import logging
import argparse
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f"logs/initialize_{datetime.now().strftime('%Y%m%d')}.log", encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('initialize')

def create_directories():
    """创建必要的目录"""
    logger.info("创建必要的目录...")
    
    # 创建数据目录
    os.makedirs("data/hot", exist_ok=True)
    os.makedirs("data/warm", exist_ok=True)
    os.makedirs("data/cold", exist_ok=True)
    
    # 创建日志目录
    os.makedirs("logs", exist_ok=True)
    
    # 创建模型目录
    os.makedirs("models", exist_ok=True)
    
    # 创建配置目录
    os.makedirs("config", exist_ok=True)
    
    logger.info("目录创建完成")

def check_config_files():
    """检查配置文件"""
    logger.info("检查配置文件...")
    
    # 检查系统配置文件
    system_config_path = "config/system_config.json"
    if not os.path.exists(system_config_path):
        logger.warning(f"系统配置文件不存在：{system_config_path}")
        create_default_system_config()
    else:
        logger.info(f"系统配置文件已存在：{system_config_path}")
    
    # 检查模块配置文件
    module_configs = [
        "config/policy_analyzer_config.json",
        "config/news_monitor_config.json",
        "config/fund_flow_analyzer_config.json",
        "config/volatility_analyzer_config.json",
        "config/sentiment_resonance_config.json",
        "config/arbitrage_detector_config.json"
    ]
    
    for config_path in module_configs:
        if not os.path.exists(config_path):
            logger.warning(f"模块配置文件不存在：{config_path}")
            create_default_module_config(config_path)
        else:
            logger.info(f"模块配置文件已存在：{config_path}")
    
    # 检查数据库配置文件
    db_config_path = "config/database_config.json"
    if not os.path.exists(db_config_path):
        logger.warning(f"数据库配置文件不存在：{db_config_path}")
        create_default_database_config()
    else:
        logger.info(f"数据库配置文件已存在：{db_config_path}")
    
    logger.info("配置文件检查完成")

def create_default_system_config():
    """创建默认系统配置文件"""
    logger.info("创建默认系统配置文件...")
    
    config = {
        "system_name": "政策-流动性-波动率套利系统",
        "version": "1.0.0",
        "log_level": "INFO",
        "modules": {
            "policy_analyzer": {"enabled": True},
            "news_monitor": {"enabled": True},
            "fund_flow_analyzer": {"enabled": True},
            "volatility_analyzer": {"enabled": True},
            "sentiment_resonance": {"enabled": True},
            "arbitrage_detector": {"enabled": True}
        },
        "scheduler": {
            "max_workers": 4,
            "task_timeout": 300,
            "retry_count": 3,
            "retry_delay": 5
        },
        "data_storage": {
            "hot_storage_path": "data/hot",
            "warm_storage_path": "data/warm",
            "cold_storage_path": "data/cold",
            "hot_to_warm_days": 7,
            "warm_to_cold_days": 30,
            "compression": True
        },
        "database": {
            "enabled": False,
            "config_path": "config/database_config.json"
        },
        "gui": {
            "enabled": True,
            "theme": "light",
            "port": 8080,
            "refresh_interval": 5
        }
    }
    
    with open("config/system_config.json", 'w', encoding='utf-8') as f:
        json.dump(config, f, indent=4, ensure_ascii=False)
    
    logger.info("默认系统配置文件创建完成")

def create_default_module_config(config_path):
    """创建默认模块配置文件"""
    logger.info(f"创建默认模块配置文件：{config_path}")
    
    module_name = os.path.basename(config_path).replace("_config.json", "")
    
    if module_name == "policy_analyzer":
        config = {
            "module_name": "policy_analyzer",
            "enabled": True,
            "log_level": "INFO",
            "data_sources": {
                "gov_policy": {
                    "enabled": True,
                    "url": "https://www.gov.cn/zhengce/zhengcewenjianku/index.htm",
                    "max_pages": 5
                },
                "ndrc_policy": {
                    "enabled": True,
                    "url": "https://www.ndrc.gov.cn/xxgk/",
                    "max_pages": 5
                }
            },
            "nlp": {
                "sentiment_model": "finbert",
                "model_path": "models/finbert",
                "keyword_extraction": {
                    "max_keywords": 20,
                    "min_frequency": 2
                }
            },
            "industry_impact": {
                "decay_factor": 0.05,
                "threshold": 0.3
            }
        }
    
    elif module_name == "news_monitor":
        config = {
            "module_name": "news_monitor",
            "enabled": True,
            "log_level": "INFO",
            "data_sources": {
                "financial_breakfast": {
                    "enabled": True,
                    "max_items": 50
                },
                "global_news": {
                    "enabled": True,
                    "max_items": 50
                },
                "sina_finance": {
                    "enabled": True,
                    "max_items": 50
                },
                "eastmoney_news": {
                    "enabled": True,
                    "max_items": 50
                },
                "stock_news": {
                    "enabled": True,
                    "max_items": 50
                }
            },
            "news_processing": {
                "deduplication": {
                    "enabled": True,
                    "similarity_threshold": 0.8
                },
                "clustering": {
                    "enabled": True,
                    "max_clusters": 20,
                    "similarity_threshold": 0.7
                }
            },
            "sentiment_analysis": {
                "model": "finbert",
                "model_path": "models/finbert",
                "threshold": 0.6
            },
            "monitoring": {
                "interval_minutes": 5,
                "hot_topic_threshold": 3
            }
        }
    
    elif module_name == "fund_flow_analyzer":
        config = {
            "module_name": "fund_flow_analyzer",
            "enabled": True,
            "log_level": "INFO",
            "data_sources": {
                "north_south_flow": {
                    "enabled": True,
                    "lookback_days": 60
                },
                "sector_flow": {
                    "enabled": True,
                    "lookback_days": 30
                },
                "stock_flow": {
                    "enabled": True,
                    "lookback_days": 30,
                    "top_stocks": 300
                }
            },
            "analysis": {
                "moving_average_windows": [5, 10, 20],
                "trend_threshold": 0.05,
                "correlation_window": 20,
                "significant_flow_threshold": 0.1
            }
        }
    
    elif module_name == "volatility_analyzer":
        config = {
            "module_name": "volatility_analyzer",
            "enabled": True,
            "log_level": "INFO",
            "data_sources": {
                "market_data": {
                    "enabled": True,
                    "lookback_days": 120
                },
                "sector_data": {
                    "enabled": True,
                    "lookback_days": 120
                },
                "stock_data": {
                    "enabled": True,
                    "lookback_days": 120,
                    "top_stocks": 300
                }
            },
            "volatility": {
                "windows": [20, 60],
                "annualization_factor": 252,
                "percentile_threshold": 80,
                "significant_change_threshold": 0.2
            },
            "analysis": {
                "correlation_window": 20,
                "regime_detection": {
                    "high_vol_threshold": 0.3,
                    "low_vol_threshold": 0.15
                }
            }
        }
    
    elif module_name == "sentiment_resonance":
        config = {
            "module_name": "sentiment_resonance",
            "enabled": True,
            "log_level": "INFO",
            "models": {
                "policy_sentiment": {
                    "model": "finbert",
                    "model_path": "models/finbert"
                },
                "news_sentiment": {
                    "model": "finbert",
                    "model_path": "models/finbert"
                }
            },
            "resonance": {
                "window_days": 7,
                "threshold": 0.7,
                "decay_factor": 0.1
            }
        }
    
    elif module_name == "arbitrage_detector":
        config = {
            "module_name": "arbitrage_detector",
            "enabled": True,
            "log_level": "INFO",
            "factors": {
                "policy_weight": 0.3,
                "fund_flow_weight": 0.3,
                "volatility_weight": 0.2,
                "sentiment_weight": 0.2
            },
            "thresholds": {
                "opportunity_threshold": 0.7,
                "risk_threshold": 0.5
            },
            "filters": {
                "min_market_cap": 5000000000,
                "max_market_cap": None,
                "min_turnover_rate": 1.0,
                "exclude_st": True
            }
        }
    
    else:
        config = {
            "module_name": module_name,
            "enabled": True,
            "log_level": "INFO"
        }
    
    with open(config_path, 'w', encoding='utf-8') as f:
        json.dump(config, f, indent=4, ensure_ascii=False)
    
    logger.info(f"默认模块配置文件创建完成：{config_path}")

def create_default_database_config():
    """创建默认数据库配置文件"""
    logger.info("创建默认数据库配置文件...")
    
    config = {
        "enabled": False,
        "type": "sqlite",
        "sqlite": {
            "path": "data/database.db"
        },
        "mysql": {
            "host": "localhost",
            "port": 3306,
            "user": "root",
            "password": "",
            "database": "policy_liquidity_volatility"
        },
        "postgresql": {
            "host": "localhost",
            "port": 5432,
            "user": "postgres",
            "password": "",
            "database": "policy_liquidity_volatility"
        },
        "connection_pool": {
            "pool_size": 5,
            "max_overflow": 10,
            "timeout": 30
        }
    }
    
    with open("config/database_config.json", 'w', encoding='utf-8') as f:
        json.dump(config, f, indent=4, ensure_ascii=False)
    
    logger.info("默认数据库配置文件创建完成")

def check_models():
    """检查模型文件"""
    logger.info("检查模型文件...")
    
    # 检查FinBERT模型
    finbert_dir = "models/finbert"
    if not os.path.exists(finbert_dir) or len(os.listdir(finbert_dir)) == 0:
        logger.warning(f"FinBERT模型不存在或为空：{finbert_dir}")
        logger.info("请手动下载FinBERT模型，或使用以下命令：")
        logger.info("python -c \"from huggingface_hub import snapshot_download; snapshot_download(repo_id='ProsusAI/finbert', local_dir='models/finbert')\"")
    else:
        logger.info(f"FinBERT模型已存在：{finbert_dir}")
    
    logger.info("模型文件检查完成")

def initialize_system():
    """初始化系统"""
    logger.info("开始初始化系统...")
    
    # 创建必要的目录
    create_directories()
    
    # 检查配置文件
    check_config_files()
    
    # 检查模型文件
    check_models()
    
    logger.info("系统初始化完成")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="系统初始化脚本")
    parser.add_argument("--force", action="store_true", help="强制重新初始化，覆盖现有配置")
    args = parser.parse_args()
    
    if args.force:
        logger.info("强制重新初始化系统...")
        initialize_system()
    else:
        logger.info("初始化系统...")
        initialize_system()
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
