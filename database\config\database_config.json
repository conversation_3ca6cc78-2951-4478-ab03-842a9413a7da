{"postgresql": {"enabled": false, "host": "localhost", "port": 5432, "database": "financial_system", "user": "postgres", "password": "postgres", "use_sqlite_fallback": true, "sqlite_db_path": "data/financial_system.db"}, "influxdb": {"enabled": false, "host": "localhost", "port": 8086, "database": "financial_system", "user": "admin", "password": "admin", "use_csv_fallback": true, "csv_dir": "data/time_series"}, "mongodb": {"enabled": false, "host": "localhost", "port": 27017, "database": "financial_system", "user": "admin", "password": "admin", "use_json_fallback": true, "json_dir": "data/documents"}, "redis": {"enabled": false, "host": "localhost", "port": 6379, "db": 0, "password": null, "use_memory_fallback": true}, "fallback": {"enabled": true, "data_dir": "data"}}