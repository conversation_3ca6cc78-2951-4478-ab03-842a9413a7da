"""
情绪共振模型示例

展示如何使用情绪共振模型
"""

import os
import sys
import time
import json
from datetime import datetime, timedelta
import pandas as pd
import matplotlib.pyplot as plt

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入情绪共振模型
from data_sources.sentiment_resonance import SentimentResonance

# 导入中央调度器
from core.scheduler import CentralScheduler, TaskPriority

def example_basic_usage():
    """基本使用示例"""
    print("\n=== 基本使用示例 ===")
    
    # 创建调度器
    scheduler = CentralScheduler()
    
    # 创建情绪共振模型
    resonance = SentimentResonance()
    
    # 设置调度器
    resonance.set_scheduler(scheduler)
    
    # 启动调度器
    scheduler.start()
    
    # 初始化模块
    resonance.initialize()
    
    # 等待初始化完成
    print("等待初始化完成...")
    time.sleep(3)
    
    # 获取模块状态
    status = resonance.get_status()
    print(f"模块状态: {json.dumps(status, indent=2, ensure_ascii=False)}")
    
    # 停止调度器
    scheduler.stop()
    
    print("基本使用示例完成")

def example_analyze_sentiment():
    """分析情绪共振示例"""
    print("\n=== 分析情绪共振示例 ===")
    
    # 创建调度器
    scheduler = CentralScheduler()
    
    # 创建情绪共振模型
    resonance = SentimentResonance()
    
    # 设置调度器
    resonance.set_scheduler(scheduler)
    
    # 启动调度器
    scheduler.start()
    
    # 手动分析情绪共振
    print("开始分析情绪共振...")
    result = resonance.analyze_sentiment_resonance()
    
    print(f"分析结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
    
    # 如果分析成功，显示部分共振点
    if result['status'] == 'success' and result.get('resonance_points_count', 0) > 0:
        print("\n情绪共振点示例:")
        for i, point in enumerate(resonance.resonance_data.get('resonance_points', [])[:3]):  # 只显示前3个
            print(f"\n共振点 {i+1}:")
            print(f"  政策类型: {point.get('policy_type', '')}")
            print(f"  政策情绪: {point.get('policy_sentiment', 0.0):.2f}")
            print(f"  新闻情绪: {point.get('news_sentiment', 0.0):.2f}")
            print(f"  共振强度: {point.get('resonance_strength', 0.0):.2f}")
            print(f"  方向: {point.get('direction', '')}")
            
            # 显示相关政策
            policies = point.get('policies', [])
            if policies:
                print("\n  相关政策:")
                for policy in policies[:2]:  # 只显示前2条
                    print(f"    - {policy.get('title', '')}")
            
            # 显示相关新闻
            news = point.get('news', [])
            if news:
                print("\n  相关新闻:")
                for n in news[:2]:  # 只显示前2条
                    print(f"    - {n.get('title', '')}")
    
    # 停止调度器
    scheduler.stop()
    
    print("分析情绪共振示例完成")

def example_get_resonance_report():
    """获取情绪共振报告示例"""
    print("\n=== 获取情绪共振报告示例 ===")
    
    # 创建调度器
    scheduler = CentralScheduler()
    
    # 创建情绪共振模型
    resonance = SentimentResonance()
    
    # 设置调度器
    resonance.set_scheduler(scheduler)
    
    # 启动调度器
    scheduler.start()
    
    # 先分析情绪共振
    print("先分析情绪共振...")
    resonance.analyze_sentiment_resonance()
    
    # 获取情绪共振报告
    print("\n开始获取情绪共振报告...")
    result = resonance.get_resonance_report(days=7)
    
    print(f"报告结果状态: {result['status']}")
    print(f"报告结果消息: {result['message']}")
    
    # 显示报告内容
    if result['status'] == 'success' and 'report' in result:
        report = result['report']
        print(f"\n报告周期: {report.get('period', '')}")
        
        # 显示情绪趋势
        trend = report.get('sentiment_trend', {})
        print(f"\n情绪趋势: {trend.get('description', '')}")
        print(f"  方向: {trend.get('direction', '')}")
        print(f"  强度: {trend.get('strength', 0.0):.2f}")
        print(f"  起始分数: {trend.get('start_score', 0.0):.2f}")
        print(f"  结束分数: {trend.get('end_score', 0.0):.2f}")
        print(f"  变化: {trend.get('change', 0.0):.2f}")
        
        # 显示行业情绪
        industry_sentiment = report.get('industry_sentiment', {})
        if industry_sentiment:
            print("\n行业情绪排名:")
            # 按情绪分数排序
            sorted_industry = sorted(industry_sentiment.items(), key=lambda x: x[1], reverse=True)
            for i, (industry, score) in enumerate(sorted_industry[:5]):  # 只显示前5个行业
                print(f"{i+1}. {industry}: {score:.2f}")
    
    # 停止调度器
    scheduler.stop()
    
    print("获取情绪共振报告示例完成")

def example_sentiment_dictionary():
    """情感词典示例"""
    print("\n=== 情感词典示例 ===")
    
    # 创建情绪共振模型
    resonance = SentimentResonance()
    
    # 获取情感词典
    sentiment_dict = resonance.sentiment_dict
    
    print(f"情感词典包含{len(sentiment_dict)}个词")
    
    # 显示部分正面词汇
    print("\n正面词汇示例:")
    positive_words = {k: v for k, v in sentiment_dict.items() if v > 0}
    sorted_positive = sorted(positive_words.items(), key=lambda x: x[1], reverse=True)
    for word, score in sorted_positive[:10]:  # 只显示前10个
        print(f"{word}: {score:.2f}")
    
    # 显示部分负面词汇
    print("\n负面词汇示例:")
    negative_words = {k: v for k, v in sentiment_dict.items() if v < 0}
    sorted_negative = sorted(negative_words.items(), key=lambda x: x[1])
    for word, score in sorted_negative[:10]:  # 只显示前10个
        print(f"{word}: {score:.2f}")
    
    # 测试情感分析
    test_texts = [
        "市场行情持续向好，多项利好政策出台，投资者信心大增",
        "经济下行压力加大，企业经营困难，市场悲观情绪蔓延",
        "市场震荡调整，投资者观望情绪浓厚，等待政策明朗"
    ]
    
    print("\n情感分析测试:")
    for text in test_texts:
        score = resonance._dictionary_sentiment_analysis(text)
        label = resonance._get_sentiment_label(score)
        print(f"文本: {text}")
        print(f"情感分数: {score:.2f}, 情感标签: {label}\n")
    
    print("情感词典示例完成")

def example_turning_points():
    """情绪拐点示例"""
    print("\n=== 情绪拐点示例 ===")
    
    # 创建情绪共振模型
    resonance = SentimentResonance()
    
    # 手动创建一些市场情绪数据
    market_sentiment = []
    base_date = datetime.now() - timedelta(days=10)
    
    # 创建一个波动的情绪序列
    scores = [0.1, 0.2, 0.4, 0.6, 0.5, 0.3, 0.1, -0.1, -0.3, -0.2, 0.0]
    
    for i, score in enumerate(scores):
        date = (base_date + timedelta(days=i)).strftime('%Y-%m-%d')
        sentiment = {
            'date': date,
            'combined_sentiment_score': score,
            'news_sentiment_score': score - 0.1,
            'policy_sentiment_score': score + 0.1,
            'sentiment_label': resonance._get_sentiment_label(score)
        }
        market_sentiment.append(sentiment)
    
    # 更新情绪共振数据
    resonance.resonance_data['market_sentiment'] = market_sentiment
    
    # 识别拐点
    turning_points = resonance._identify_turning_points(market_sentiment[-1])
    
    print(f"识别到{len(turning_points)}个情绪拐点")
    
    # 显示拐点
    if turning_points:
        print("\n情绪拐点:")
        for i, point in enumerate(turning_points):
            print(f"\n拐点 {i+1}:")
            print(f"  日期: {point.get('date', '')}")
            print(f"  类型: {point.get('type', '')}")
            print(f"  分数: {point.get('score', 0.0):.2f}")
            print(f"  前一分数: {point.get('prev_score', 0.0):.2f}")
            print(f"  后一分数: {point.get('next_score', 0.0):.2f}")
            print(f"  变化率: {point.get('change_rate', 0.0):.2f}")
    
    # 绘制情绪曲线和拐点
    try:
        plt.figure(figsize=(10, 6))
        
        # 绘制情绪曲线
        dates = [item['date'] for item in market_sentiment]
        scores = [item['combined_sentiment_score'] for item in market_sentiment]
        plt.plot(dates, scores, 'b-', label='情绪分数')
        
        # 绘制拐点
        for point in turning_points:
            idx = dates.index(point['date'])
            if point['type'] == 'peak':
                plt.plot(dates[idx], scores[idx], 'ro', markersize=10, label='峰值' if 'peak' not in plt.gca().get_legend_handles_labels()[1] else '')
            else:
                plt.plot(dates[idx], scores[idx], 'go', markersize=10, label='谷值' if 'valley' not in plt.gca().get_legend_handles_labels()[1] else '')
        
        plt.title('市场情绪曲线与拐点')
        plt.xlabel('日期')
        plt.ylabel('情绪分数')
        plt.grid(True)
        plt.legend()
        plt.xticks(rotation=45)
        plt.tight_layout()
        
        # 保存图表
        os.makedirs('output', exist_ok=True)
        plt.savefig('output/sentiment_turning_points.png')
        print("\n情绪曲线图已保存到 output/sentiment_turning_points.png")
        
    except Exception as e:
        print(f"绘制图表失败: {str(e)}")
    
    print("情绪拐点示例完成")

def main():
    """主函数"""
    print("=== 情绪共振模型示例 ===")
    
    # 创建必要的目录
    os.makedirs('logs', exist_ok=True)
    os.makedirs('data', exist_ok=True)
    os.makedirs('config', exist_ok=True)
    
    # 询问用户选择示例
    print("\n请选择要运行的示例:")
    print("1. 基本使用示例")
    print("2. 分析情绪共振示例")
    print("3. 获取情绪共振报告示例")
    print("4. 情感词典示例")
    print("5. 情绪拐点示例")
    print("6. 运行所有示例")
    print("0. 退出")
    
    choice = input("\n请输入选项（0-6）: ")
    
    if choice == '1':
        example_basic_usage()
    elif choice == '2':
        example_analyze_sentiment()
    elif choice == '3':
        example_get_resonance_report()
    elif choice == '4':
        example_sentiment_dictionary()
    elif choice == '5':
        example_turning_points()
    elif choice == '6':
        example_basic_usage()
        example_analyze_sentiment()
        example_get_resonance_report()
        example_sentiment_dictionary()
        example_turning_points()
    elif choice == '0':
        print("退出")
    else:
        print("无效选项")

if __name__ == '__main__':
    main()
