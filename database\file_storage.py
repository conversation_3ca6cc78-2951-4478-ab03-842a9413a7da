"""
文件存储管理器

负责管理新闻和政策的原始内容文件
"""

import os
import json
import logging
import shutil
import gzip
import hashlib
from datetime import datetime
from typing import Dict, Any, List, Optional, Union, BinaryIO

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('file_storage')

class FileStorage:
    """文件存储管理器"""
    
    def __init__(self, base_dir: str = 'data'):
        """
        初始化文件存储管理器
        
        Args:
            base_dir: 基础目录
        """
        self.base_dir = base_dir
        self._ensure_directories()
        logger.info(f"文件存储管理器初始化完成，基础目录: {base_dir}")
    
    def _ensure_directories(self):
        """确保必要的目录存在"""
        # 创建基础目录
        os.makedirs(self.base_dir, exist_ok=True)
        
        # 创建新闻和政策目录
        os.makedirs(os.path.join(self.base_dir, 'news'), exist_ok=True)
        os.makedirs(os.path.join(self.base_dir, 'policies'), exist_ok=True)
    
    def save_news_content(self, news_id: str, content: str, source: str, 
                          publish_date: str, compress: bool = False) -> str:
        """
        保存新闻内容
        
        Args:
            news_id: 新闻ID
            content: 新闻内容
            source: 新闻来源
            publish_date: 发布日期 (YYYY-MM-DD)
            compress: 是否压缩
        
        Returns:
            文件存储路径
        """
        # 解析日期
        try:
            date_obj = datetime.strptime(publish_date, '%Y-%m-%d')
            year_month = date_obj.strftime('%Y-%m')
            date_str = date_obj.strftime('%Y%m%d')
        except ValueError:
            # 如果日期格式不正确，使用当前日期
            date_obj = datetime.now()
            year_month = date_obj.strftime('%Y-%m')
            date_str = date_obj.strftime('%Y%m%d')
            logger.warning(f"新闻日期格式不正确: {publish_date}，使用当前日期")
        
        # 构建目录路径
        dir_path = os.path.join(self.base_dir, 'news', year_month, source)
        os.makedirs(dir_path, exist_ok=True)
        
        # 构建文件名
        file_name = f"news_{date_str}_{news_id}"
        
        # 保存内容
        return self._save_content(dir_path, file_name, content, compress)
    
    def save_policy_content(self, policy_id: str, content: str, source: str, 
                            publish_date: str, compress: bool = False) -> str:
        """
        保存政策内容
        
        Args:
            policy_id: 政策ID
            content: 政策内容
            source: 发布机构
            publish_date: 发布日期 (YYYY-MM-DD)
            compress: 是否压缩
        
        Returns:
            文件存储路径
        """
        # 解析日期
        try:
            date_obj = datetime.strptime(publish_date, '%Y-%m-%d')
            year_month = date_obj.strftime('%Y-%m')
            date_str = date_obj.strftime('%Y%m%d')
        except ValueError:
            # 如果日期格式不正确，使用当前日期
            date_obj = datetime.now()
            year_month = date_obj.strftime('%Y-%m')
            date_str = date_obj.strftime('%Y%m%d')
            logger.warning(f"政策日期格式不正确: {publish_date}，使用当前日期")
        
        # 构建目录路径
        dir_path = os.path.join(self.base_dir, 'policies', year_month, source)
        os.makedirs(dir_path, exist_ok=True)
        
        # 构建文件名
        file_name = f"policy_{date_str}_{policy_id}"
        
        # 保存内容
        return self._save_content(dir_path, file_name, content, compress)
    
    def _save_content(self, dir_path: str, file_name: str, content: str, compress: bool) -> str:
        """
        保存内容到文件
        
        Args:
            dir_path: 目录路径
            file_name: 文件名（不含扩展名）
            content: 内容
            compress: 是否压缩
        
        Returns:
            文件存储路径
        """
        if compress:
            file_path = os.path.join(dir_path, f"{file_name}.json.gz")
            with gzip.open(file_path, 'wt', encoding='utf-8') as f:
                json.dump({"content": content}, f, ensure_ascii=False, indent=2)
        else:
            file_path = os.path.join(dir_path, f"{file_name}.json")
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump({"content": content}, f, ensure_ascii=False, indent=2)
        
        # 返回相对于基础目录的路径
        rel_path = os.path.relpath(file_path, self.base_dir)
        logger.debug(f"内容已保存到: {rel_path}")
        return rel_path
    
    def load_content(self, file_path: str) -> Optional[str]:
        """
        加载内容
        
        Args:
            file_path: 文件路径（相对于基础目录）
        
        Returns:
            内容，如果文件不存在则返回None
        """
        # 构建完整路径
        full_path = os.path.join(self.base_dir, file_path)
        
        # 检查文件是否存在
        if not os.path.exists(full_path):
            logger.warning(f"文件不存在: {full_path}")
            return None
        
        try:
            # 根据文件扩展名判断是否为压缩文件
            if full_path.endswith('.gz'):
                with gzip.open(full_path, 'rt', encoding='utf-8') as f:
                    data = json.load(f)
            else:
                with open(full_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
            
            return data.get("content")
        
        except Exception as e:
            logger.error(f"加载内容失败: {full_path}, {str(e)}")
            return None
    
    def delete_content(self, file_path: str) -> bool:
        """
        删除内容
        
        Args:
            file_path: 文件路径（相对于基础目录）
        
        Returns:
            删除结果
        """
        # 构建完整路径
        full_path = os.path.join(self.base_dir, file_path)
        
        # 检查文件是否存在
        if not os.path.exists(full_path):
            logger.warning(f"文件不存在: {full_path}")
            return False
        
        try:
            # 删除文件
            os.remove(full_path)
            logger.debug(f"文件已删除: {file_path}")
            return True
        
        except Exception as e:
            logger.error(f"删除文件失败: {full_path}, {str(e)}")
            return False
    
    def compress_old_files(self, days: int = 30) -> int:
        """
        压缩旧文件
        
        Args:
            days: 超过多少天的文件将被压缩
        
        Returns:
            压缩的文件数量
        """
        # 计算截止日期
        cutoff_date = datetime.now().timestamp() - days * 86400
        
        # 压缩计数
        compressed_count = 0
        
        # 遍历所有文件
        for root, dirs, files in os.walk(self.base_dir):
            for file in files:
                if file.endswith('.json') and not file.endswith('.json.gz'):
                    file_path = os.path.join(root, file)
                    
                    # 检查文件修改时间
                    if os.path.getmtime(file_path) < cutoff_date:
                        try:
                            # 读取内容
                            with open(file_path, 'r', encoding='utf-8') as f:
                                content = f.read()
                            
                            # 压缩并保存
                            with gzip.open(f"{file_path}.gz", 'wt', encoding='utf-8') as f:
                                f.write(content)
                            
                            # 删除原文件
                            os.remove(file_path)
                            
                            compressed_count += 1
                            logger.debug(f"文件已压缩: {file_path}")
                        
                        except Exception as e:
                            logger.error(f"压缩文件失败: {file_path}, {str(e)}")
        
        logger.info(f"共压缩 {compressed_count} 个文件")
        return compressed_count
    
    def archive_old_files(self, months: int = 12, archive_dir: Optional[str] = None) -> int:
        """
        归档旧文件
        
        Args:
            months: 超过多少月的文件将被归档
            archive_dir: 归档目录，如果为None则使用基础目录下的archive目录
        
        Returns:
            归档的文件数量
        """
        # 设置归档目录
        if archive_dir is None:
            archive_dir = os.path.join(self.base_dir, 'archive')
        
        # 确保归档目录存在
        os.makedirs(archive_dir, exist_ok=True)
        
        # 计算截止日期
        cutoff_date = datetime.now()
        cutoff_year_month = f"{cutoff_date.year - months // 12}-{cutoff_date.month - months % 12:02d}"
        if cutoff_date.month - months % 12 <= 0:
            cutoff_year_month = f"{cutoff_date.year - months // 12 - 1}-{cutoff_date.month - months % 12 + 12:02d}"
        
        # 归档计数
        archived_count = 0
        
        # 遍历新闻和政策目录
        for content_type in ['news', 'policies']:
            content_dir = os.path.join(self.base_dir, content_type)
            if not os.path.exists(content_dir):
                continue
            
            # 遍历年月目录
            for year_month in os.listdir(content_dir):
                if not os.path.isdir(os.path.join(content_dir, year_month)):
                    continue
                
                # 检查是否为旧目录
                if year_month < cutoff_year_month:
                    src_dir = os.path.join(content_dir, year_month)
                    dst_dir = os.path.join(archive_dir, content_type, year_month)
                    
                    try:
                        # 确保目标目录存在
                        os.makedirs(os.path.dirname(dst_dir), exist_ok=True)
                        
                        # 移动目录
                        shutil.move(src_dir, dst_dir)
                        
                        # 统计文件数量
                        file_count = sum(len(files) for _, _, files in os.walk(dst_dir))
                        archived_count += file_count
                        
                        logger.info(f"已归档目录: {src_dir} -> {dst_dir}, 包含 {file_count} 个文件")
                    
                    except Exception as e:
                        logger.error(f"归档目录失败: {src_dir}, {str(e)}")
        
        logger.info(f"共归档 {archived_count} 个文件")
        return archived_count
    
    def get_storage_stats(self) -> Dict[str, Any]:
        """
        获取存储统计信息
        
        Returns:
            存储统计信息
        """
        stats = {
            "total_size": 0,
            "file_count": 0,
            "news": {
                "size": 0,
                "count": 0,
                "sources": {}
            },
            "policies": {
                "size": 0,
                "count": 0,
                "sources": {}
            }
        }
        
        # 遍历所有文件
        for content_type in ['news', 'policies']:
            content_dir = os.path.join(self.base_dir, content_type)
            if not os.path.exists(content_dir):
                continue
            
            # 遍历年月目录
            for root, dirs, files in os.walk(content_dir):
                for file in files:
                    if file.endswith('.json') or file.endswith('.json.gz'):
                        file_path = os.path.join(root, file)
                        file_size = os.path.getsize(file_path)
                        
                        # 更新总计数据
                        stats["total_size"] += file_size
                        stats["file_count"] += 1
                        
                        # 更新类型数据
                        stats[content_type]["size"] += file_size
                        stats[content_type]["count"] += 1
                        
                        # 提取来源
                        rel_path = os.path.relpath(root, content_dir)
                        parts = rel_path.split(os.sep)
                        if len(parts) >= 2:
                            source = parts[1]
                            if source not in stats[content_type]["sources"]:
                                stats[content_type]["sources"][source] = {
                                    "size": 0,
                                    "count": 0
                                }
                            stats[content_type]["sources"][source]["size"] += file_size
                            stats[content_type]["sources"][source]["count"] += 1
        
        return stats
