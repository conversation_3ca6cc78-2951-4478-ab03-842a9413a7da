"""
测试新闻监控模块
"""

import os
import time
from datetime import datetime

# 创建必要的目录
os.makedirs('logs', exist_ok=True)
os.makedirs('data/news_monitor', exist_ok=True)

# 导入新闻监控器
from news_monitor import NewsMonitor

def test_news_monitor():
    """测试新闻监控器"""
    print("\n测试新闻监控器...")
    
    # 创建新闻监控器
    monitor = NewsMonitor()
    
    # 测试获取并处理新闻
    print("\n测试获取并处理新闻...")
    processed_news = monitor.fetch_and_process_news()
    
    print(f"处理后的新闻数量: {len(processed_news)}")
    print("前5条新闻:")
    for i, news in enumerate(processed_news[:5]):
        print(f"{i+1}. {news['title']} (来源: {news['source']}, 热度: {news['score']:.2f})")
    
    # 测试分析热点
    print("\n测试分析热点...")
    hot_sectors, hot_stocks = monitor.analyze_hot_topics()
    
    print("热点板块:")
    for sector, score in sorted(hot_sectors.items(), key=lambda x: x[1], reverse=True)[:5]:
        print(f"- {sector}: {score:.2f}")
    
    print("\n热点个股:")
    for stock, score in sorted(hot_stocks.items(), key=lambda x: x[1], reverse=True)[:5]:
        print(f"- {stock}: {score:.2f}")
    
    # 测试生成每日报告
    print("\n测试生成每日报告...")
    report_file = monitor.generate_daily_report()
    
    if report_file:
        print(f"报告生成成功: {report_file}")
        
        # 读取报告内容
        with open(report_file, 'r', encoding='utf-8') as f:
            report_content = f.read()
        
        # 打印报告前500个字符
        print("\n报告预览:")
        print(report_content[:500] + "...")
    else:
        print("报告生成失败")
    
    return monitor

def test_monitoring():
    """测试监控功能"""
    print("\n测试监控功能...")
    
    # 创建新闻监控器
    monitor = NewsMonitor()
    
    # 开始监控
    monitor.start_monitoring()
    
    print("新闻监控已启动，将运行5分钟...")
    
    # 运行5分钟
    for i in range(5):
        time.sleep(60)
        print(f"已运行 {i+1} 分钟...")
    
    # 停止监控
    monitor.stop_monitoring()
    
    print("新闻监控已停止")
    
    return monitor

if __name__ == "__main__":
    print("开始测试新闻监控模块...")
    
    # 测试新闻监控器
    monitor = test_news_monitor()
    
    # 询问是否测试监控功能
    answer = input("\n是否测试监控功能？(y/n): ")
    if answer.lower() == 'y':
        test_monitoring()
    
    print("\n测试完成!")
