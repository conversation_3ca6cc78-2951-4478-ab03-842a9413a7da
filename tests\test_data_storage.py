"""
数据存储测试

测试数据存储模块的基本功能
"""

import os
import sys
import unittest
import json
import logging
import shutil
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('test_data_storage')

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入被测试的模块
from core.data_storage import DataStorage, StorageLevel

class TestDataStorage(unittest.TestCase):
    """数据存储测试类"""
    
    @classmethod
    def setUpClass(cls):
        """测试前的准备工作"""
        logger.info("初始化数据存储测试...")
        
        # 创建测试目录
        cls.test_dir = os.path.join('data', 'test_data_storage')
        os.makedirs(cls.test_dir, exist_ok=True)
        
        # 创建配置目录
        os.makedirs('config', exist_ok=True)
        
        # 创建测试配置文件
        cls.config_path = os.path.join('config', 'data_storage_config.json')
        with open(cls.config_path, 'w', encoding='utf-8') as f:
            json.dump({
                'file_storage': {
                    'hot_dir': os.path.join(cls.test_dir, 'hot'),
                    'warm_dir': os.path.join(cls.test_dir, 'warm'),
                    'cold_dir': os.path.join(cls.test_dir, 'cold'),
                    'hot_to_warm_days': 7,
                    'warm_to_cold_days': 30
                },
                'memory_cache': {
                    'enabled': True,
                    'max_size_mb': 100,
                    'ttl_seconds': 3600
                },
                'redis': {
                    'enabled': False,
                    'host': 'localhost',
                    'port': 6379,
                    'db': 0,
                    'ttl_seconds': 86400
                }
            }, f, indent=4)
        
        # 创建存储目录
        os.makedirs(os.path.join(cls.test_dir, 'hot'), exist_ok=True)
        os.makedirs(os.path.join(cls.test_dir, 'warm'), exist_ok=True)
        os.makedirs(os.path.join(cls.test_dir, 'cold'), exist_ok=True)
        
        logger.info("数据存储测试初始化完成")
    
    @classmethod
    def tearDownClass(cls):
        """测试后的清理工作"""
        logger.info("清理数据存储测试...")
        
        # 删除测试配置文件
        if os.path.exists(cls.config_path):
            os.remove(cls.config_path)
        
        # 删除测试目录
        if os.path.exists(cls.test_dir):
            shutil.rmtree(cls.test_dir)
        
        logger.info("数据存储测试清理完成")
    
    def test_init(self):
        """测试初始化"""
        logger.info("测试数据存储初始化...")
        
        # 创建数据存储对象
        data_storage = DataStorage(config_path=self.config_path)
        
        # 验证配置
        self.assertIsNotNone(data_storage.config)
        self.assertEqual(data_storage.config['file_storage']['hot_dir'], os.path.join(self.test_dir, 'hot'))
        self.assertEqual(data_storage.config['file_storage']['warm_dir'], os.path.join(self.test_dir, 'warm'))
        self.assertEqual(data_storage.config['file_storage']['cold_dir'], os.path.join(self.test_dir, 'cold'))
        
        logger.info("数据存储初始化测试通过")
    
    def test_save_and_load_hot(self):
        """测试热存储的保存和加载"""
        logger.info("测试热存储的保存和加载...")
        
        # 创建数据存储对象
        data_storage = DataStorage(config_path=self.config_path)
        
        # 测试数据
        module_name = 'test_module'
        data_name = 'test_data'
        test_data = {
            'test_key': 'test_value',
            'timestamp': datetime.now().isoformat()
        }
        
        # 保存数据到热存储
        result = data_storage.save(module_name, data_name, test_data, StorageLevel.HOT)
        self.assertTrue(result)
        
        # 从热存储加载数据
        loaded_data = data_storage.load(module_name, data_name, level=StorageLevel.HOT)
        self.assertIsNotNone(loaded_data)
        self.assertEqual(loaded_data.get('test_key'), 'test_value')
        
        logger.info("热存储的保存和加载测试通过")
    
    def test_save_and_load_warm(self):
        """测试温存储的保存和加载"""
        logger.info("测试温存储的保存和加载...")
        
        # 创建数据存储对象
        data_storage = DataStorage(config_path=self.config_path)
        
        # 测试数据
        module_name = 'test_module'
        data_name = 'test_data_warm'
        test_data = {
            'test_key': 'test_value_warm',
            'timestamp': datetime.now().isoformat()
        }
        
        # 保存数据到温存储
        result = data_storage.save(module_name, data_name, test_data, StorageLevel.WARM)
        self.assertTrue(result)
        
        # 从温存储加载数据
        loaded_data = data_storage.load(module_name, data_name, level=StorageLevel.WARM)
        self.assertIsNotNone(loaded_data)
        self.assertEqual(loaded_data.get('test_key'), 'test_value_warm')
        
        logger.info("温存储的保存和加载测试通过")
    
    def test_save_and_load_cold(self):
        """测试冷存储的保存和加载"""
        logger.info("测试冷存储的保存和加载...")
        
        # 创建数据存储对象
        data_storage = DataStorage(config_path=self.config_path)
        
        # 测试数据
        module_name = 'test_module'
        data_name = 'test_data_cold'
        test_data = {
            'test_key': 'test_value_cold',
            'timestamp': datetime.now().isoformat()
        }
        
        # 保存数据到冷存储
        result = data_storage.save(module_name, data_name, test_data, StorageLevel.COLD)
        self.assertTrue(result)
        
        # 从冷存储加载数据
        loaded_data = data_storage.load(module_name, data_name, level=StorageLevel.COLD)
        self.assertIsNotNone(loaded_data)
        self.assertEqual(loaded_data.get('test_key'), 'test_value_cold')
        
        logger.info("冷存储的保存和加载测试通过")
    
    def test_auto_level_detection(self):
        """测试自动级别检测"""
        logger.info("测试自动级别检测...")
        
        # 创建数据存储对象
        data_storage = DataStorage(config_path=self.config_path)
        
        # 测试数据
        module_name = 'test_module'
        data_name = 'test_data_auto'
        test_data = {
            'test_key': 'test_value_auto',
            'timestamp': datetime.now().isoformat()
        }
        
        # 保存数据到温存储
        data_storage.save(module_name, data_name, test_data, StorageLevel.WARM)
        
        # 不指定级别加载数据（应该自动检测）
        loaded_data = data_storage.load(module_name, data_name)
        self.assertIsNotNone(loaded_data)
        self.assertEqual(loaded_data.get('test_key'), 'test_value_auto')
        
        logger.info("自动级别检测测试通过")
    
    def test_data_exists(self):
        """测试数据存在检查"""
        logger.info("测试数据存在检查...")
        
        # 创建数据存储对象
        data_storage = DataStorage(config_path=self.config_path)
        
        # 测试数据
        module_name = 'test_module'
        data_name = 'test_data_exists'
        test_data = {
            'test_key': 'test_value_exists',
            'timestamp': datetime.now().isoformat()
        }
        
        # 保存数据
        data_storage.save(module_name, data_name, test_data, StorageLevel.WARM)
        
        # 检查数据是否存在
        exists = data_storage.exists(module_name, data_name)
        self.assertTrue(exists)
        
        # 检查不存在的数据
        not_exists = data_storage.exists(module_name, 'non_existent_data')
        self.assertFalse(not_exists)
        
        logger.info("数据存在检查测试通过")
    
    def test_data_delete(self):
        """测试数据删除"""
        logger.info("测试数据删除...")
        
        # 创建数据存储对象
        data_storage = DataStorage(config_path=self.config_path)
        
        # 测试数据
        module_name = 'test_module'
        data_name = 'test_data_delete'
        test_data = {
            'test_key': 'test_value_delete',
            'timestamp': datetime.now().isoformat()
        }
        
        # 保存数据
        data_storage.save(module_name, data_name, test_data, StorageLevel.WARM)
        
        # 检查数据是否存在
        exists_before = data_storage.exists(module_name, data_name)
        self.assertTrue(exists_before)
        
        # 删除数据
        result = data_storage.delete(module_name, data_name)
        self.assertTrue(result)
        
        # 检查数据是否已删除
        exists_after = data_storage.exists(module_name, data_name)
        self.assertFalse(exists_after)
        
        logger.info("数据删除测试通过")

if __name__ == '__main__':
    unittest.main()
