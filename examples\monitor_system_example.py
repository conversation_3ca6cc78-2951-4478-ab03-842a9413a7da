"""
全天候监控框架示例

展示如何使用全天候监控框架
"""

import os
import sys
import time
import json
from datetime import datetime, timedelta

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入监控系统
from monitor_system import MonitorSystem
from data_sources.news_processor_module import NewsProcessorModule

def example_basic_usage():
    """基本使用示例"""
    print("\n=== 基本使用示例 ===")
    
    # 创建监控系统
    monitor = MonitorSystem()
    
    # 注册模块
    monitor.register_module('news_processor', NewsProcessorModule())
    
    # 启动系统
    monitor.start()
    
    # 等待系统初始化
    print("等待系统初始化...")
    time.sleep(5)
    
    # 检查系统状态
    status = monitor.health_check()
    print(f"系统状态: {status['status']}")
    print(f"消息: {status['message']}")
    
    # 停止系统
    print("停止系统...")
    monitor.stop()
    
    print("基本使用示例完成")

def example_task_scheduling():
    """任务调度示例"""
    print("\n=== 任务调度示例 ===")
    
    # 创建监控系统
    monitor = MonitorSystem()
    
    # 注册模块
    news_processor = NewsProcessorModule()
    monitor.register_module('news_processor', news_processor)
    
    # 启动系统
    monitor.start()
    
    # 等待系统初始化
    print("等待系统初始化...")
    time.sleep(5)
    
    # 手动调度任务
    print("手动调度任务...")
    task_id = news_processor.schedule_task(
        function='process_news_batch',
        params={'batch_size': 50},
        priority='high'
    )
    
    print(f"任务ID: {task_id}")
    
    # 等待任务完成
    print("等待任务完成...")
    for _ in range(10):
        status = news_processor.get_task_status(task_id)
        print(f"任务状态: {status.name if status else 'Unknown'}")
        
        if status and status.name in ('COMPLETED', 'FAILED'):
            break
            
        time.sleep(1)
    
    # 获取任务结果
    result = news_processor.get_task_result(task_id)
    if result:
        print(f"任务结果: {json.dumps(result, indent=2)}")
    else:
        print("任务未完成或失败")
    
    # 停止系统
    print("停止系统...")
    monitor.stop()
    
    print("任务调度示例完成")

def example_market_state():
    """市场状态示例"""
    print("\n=== 市场状态示例 ===")
    
    # 创建监控系统
    monitor = MonitorSystem()
    
    # 启动系统
    monitor.start()
    
    # 等待系统初始化
    print("等待系统初始化...")
    time.sleep(5)
    
    # 获取调度器
    scheduler = monitor.scheduler
    
    # 获取当前市场状态
    market_state = scheduler.market_state
    print(f"当前市场状态: {market_state.name}")
    
    # 获取资源分配策略
    resource_allocation = scheduler.resource_allocation
    print("资源分配策略:")
    for module, allocation in resource_allocation.items():
        print(f"  {module}: {allocation}")
    
    # 停止系统
    print("停止系统...")
    monitor.stop()
    
    print("市场状态示例完成")

def example_health_check():
    """健康检查示例"""
    print("\n=== 健康检查示例 ===")
    
    # 创建监控系统
    monitor = MonitorSystem()
    
    # 注册模块
    monitor.register_module('news_processor', NewsProcessorModule())
    
    # 启动系统
    monitor.start()
    
    # 等待系统初始化
    print("等待系统初始化...")
    time.sleep(5)
    
    # 执行健康检查
    print("执行健康检查...")
    health_status = monitor.health_check()
    
    print(f"系统状态: {health_status['status']}")
    print(f"消息: {health_status['message']}")
    
    print("调度器状态:")
    for key, value in health_status['scheduler'].items():
        print(f"  {key}: {value}")
    
    print("模块状态:")
    for module_name, module_status in health_status['modules'].items():
        print(f"  {module_name}: {module_status['status']}")
        print(f"    消息: {module_status['message']}")
    
    # 停止系统
    print("停止系统...")
    monitor.stop()
    
    print("健康检查示例完成")

def example_long_running():
    """长时间运行示例"""
    print("\n=== 长时间运行示例 ===")
    
    # 创建监控系统
    monitor = MonitorSystem()
    
    # 注册模块
    monitor.register_module('news_processor', NewsProcessorModule())
    
    # 启动系统
    monitor.start()
    
    # 运行一段时间
    print("系统将运行60秒...")
    try:
        for i in range(60):
            time.sleep(1)
            
            # 每10秒执行一次健康检查
            if i % 10 == 0:
                health_status = monitor.health_check()
                print(f"[{i}s] 系统状态: {health_status['status']}")
    
    except KeyboardInterrupt:
        print("接收到中断信号")
    
    finally:
        # 停止系统
        print("停止系统...")
        monitor.stop()
    
    print("长时间运行示例完成")

def main():
    """主函数"""
    print("=== 全天候监控框架示例 ===")
    
    # 创建必要的目录
    os.makedirs('logs', exist_ok=True)
    os.makedirs('data', exist_ok=True)
    os.makedirs('config', exist_ok=True)
    
    # 询问用户选择示例
    print("\n请选择要运行的示例:")
    print("1. 基本使用示例")
    print("2. 任务调度示例")
    print("3. 市场状态示例")
    print("4. 健康检查示例")
    print("5. 长时间运行示例")
    print("0. 退出")
    
    choice = input("\n请输入选项（0-5）: ")
    
    if choice == '1':
        example_basic_usage()
    elif choice == '2':
        example_task_scheduling()
    elif choice == '3':
        example_market_state()
    elif choice == '4':
        example_health_check()
    elif choice == '5':
        example_long_running()
    elif choice == '0':
        print("退出")
    else:
        print("无效选项")

if __name__ == '__main__':
    main()
