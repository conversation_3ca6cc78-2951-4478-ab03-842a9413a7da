# 分支管理策略

本文档详细说明了政策-流动性-波动率套利系统的Git分支管理策略。

## 分支结构

项目采用GitFlow工作流模型的变体，包含以下分支类型：

### 长期分支

- **main**：主分支，包含稳定的生产版本代码
  - 只接受来自`release`分支和`hotfix`分支的合并
  - 每次合并到`main`分支都应该打上版本标签
  - 禁止直接在`main`分支上提交代码

- **develop**：开发分支，包含最新的开发代码
  - 所有功能开发完成后都合并到此分支
  - 应保持此分支的代码可构建、可测试
  - 作为创建`feature`分支和`release`分支的基础

### 临时分支

- **feature/xxx**：功能分支，用于开发新功能
  - 从`develop`分支创建
  - 完成后合并回`develop`分支
  - 命名规范：`feature/[功能名称]`
  - 当前功能分支：
    - **feature/nlp-model-upgrade**：NLP模型升级分支
    - **feature/news-monitor-enhancement**：新闻监控增强分支
    - **feature/system-integration**：系统集成分支

- **release/x.x.x**：发布分支，用于准备新版本发布
  - 从`develop`分支创建
  - 只允许修复bug，不允许添加新功能
  - 完成后同时合并到`main`和`develop`分支
  - 命名规范：`release/[版本号]`，如`release/1.0.0`

- **hotfix/xxx**：热修复分支，用于修复生产环境中的紧急问题
  - 从`main`分支创建
  - 完成后同时合并到`main`和`develop`分支
  - 命名规范：`hotfix/[问题描述]`或`hotfix/[版本号]`

## 工作流程

### 功能开发流程

1. 从`develop`分支创建功能分支
   ```bash
   git checkout develop
   git pull
   git checkout -b feature/new-feature
   ```

2. 在功能分支上进行开发
   ```bash
   # 进行代码修改
   git add .
   git commit -m "实现新功能"
   ```

3. 定期从`develop`分支同步更新
   ```bash
   git checkout develop
   git pull
   git checkout feature/new-feature
   git merge develop
   # 解决冲突（如果有）
   ```

4. 完成功能开发后，将功能分支合并回`develop`分支
   ```bash
   git checkout develop
   git pull
   git merge --no-ff feature/new-feature
   git push
   ```

5. 删除功能分支（可选）
   ```bash
   git branch -d feature/new-feature
   ```

### 发布流程

1. 从`develop`分支创建发布分支
   ```bash
   git checkout develop
   git pull
   git checkout -b release/1.0.0
   ```

2. 在发布分支上进行测试和bug修复
   ```bash
   # 修复bug
   git add .
   git commit -m "修复发布前的bug"
   ```

3. 完成发布准备后，将发布分支合并到`main`分支
   ```bash
   git checkout main
   git pull
   git merge --no-ff release/1.0.0
   git tag -a v1.0.0 -m "版本1.0.0"
   git push --follow-tags
   ```

4. 将发布分支合并回`develop`分支
   ```bash
   git checkout develop
   git pull
   git merge --no-ff release/1.0.0
   git push
   ```

5. 删除发布分支
   ```bash
   git branch -d release/1.0.0
   ```

### 热修复流程

1. 从`main`分支创建热修复分支
   ```bash
   git checkout main
   git pull
   git checkout -b hotfix/critical-bug
   ```

2. 在热修复分支上修复问题
   ```bash
   # 修复bug
   git add .
   git commit -m "修复关键bug"
   ```

3. 将热修复分支合并到`main`分支
   ```bash
   git checkout main
   git pull
   git merge --no-ff hotfix/critical-bug
   git tag -a v1.0.1 -m "热修复版本1.0.1"
   git push --follow-tags
   ```

4. 将热修复分支合并回`develop`分支
   ```bash
   git checkout develop
   git pull
   git merge --no-ff hotfix/critical-bug
   git push
   ```

5. 删除热修复分支
   ```bash
   git branch -d hotfix/critical-bug
   ```

## 提交规范

为了保持提交历史的清晰和一致，我们采用以下提交信息规范：

```
<类型>(<范围>): <简短描述>

<详细描述>

<关联问题>
```

### 类型

- **feat**: 新功能
- **fix**: 修复bug
- **docs**: 文档更新
- **style**: 代码风格调整（不影响代码功能）
- **refactor**: 代码重构
- **perf**: 性能优化
- **test**: 测试相关
- **chore**: 构建过程或辅助工具的变动

### 范围

指明修改的模块或组件，如：

- **policy**: 政策分析模块
- **fund-flow**: 资金流分析模块
- **volatility**: 波动率分析模块
- **news**: 新闻监控模块
- **core**: 核心功能
- **docs**: 文档
- **tests**: 测试

### 示例

```
feat(fund-flow): 实现北向南向资金监测功能

- 使用stock_hsgt_fund_flow_summary_em接口获取数据
- 实现数据库存储功能
- 添加南向资金分析和北向南向资金综合分析功能

Closes #123
```

## 版本号规范

项目采用语义化版本号（Semantic Versioning）规范，格式为：`主版本号.次版本号.修订号`

- **主版本号**：当进行不兼容的API修改时增加
- **次版本号**：当增加向下兼容的功能时增加
- **修订号**：当进行向下兼容的bug修复时增加

## 分支保护规则

为了保证代码质量和稳定性，我们对重要分支设置了保护规则：

- **main分支**：
  - 禁止直接推送
  - 必须通过Pull Request进行合并
  - 必须通过代码审查
  - 必须通过自动化测试

- **develop分支**：
  - 禁止直接推送
  - 必须通过Pull Request进行合并
  - 必须通过自动化测试

## 当前开发计划

### 近期开发任务

1. **NLP模型升级**（feature/nlp-model-upgrade分支）
   - 使用FinBERT或DistilRoBERTa-Financial模型替代当前模型
   - 优化政策分析模块的情感分析功能
   - 提高模型处理金融文本的准确性

2. **新闻监控增强**（feature/news-monitor-enhancement分支）
   - 优化新闻去重算法
   - 增加更多新闻源
   - 改进热点事件检测功能

3. **系统集成**（feature/system-integration分支）
   - 完成政策-波动率集成测试
   - 实现系统操作面板
   - 优化系统整体性能和稳定性

### 发布计划

- **v1.0.0**：计划于2025年6月发布
  - 包含所有核心功能模块
  - 完成全面的系统测试
  - 提供完整的用户文档
