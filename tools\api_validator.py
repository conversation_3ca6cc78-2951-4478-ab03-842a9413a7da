"""
API数据获取校验工具
逐个验证每个数据源的可用性、数据质量和存储效果
"""

import os
import sys
import json
import time
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import logging

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import akshare as ak
from utils.logger import logger
from core.data_storage import DataStorage, StorageLevel

class APIValidator:
    """API数据获取校验器"""

    def __init__(self):
        self.data_storage = DataStorage()
        self.validation_results = {}
        self.setup_logging()

    def setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('logs/api_validation.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger('api_validator')

    def validate_all_apis(self) -> Dict[str, Any]:
        """验证所有API"""
        self.logger.info("开始API数据获取校验...")

        # 1. 新闻数据API
        self.validate_news_apis()

        # 2. 资金流数据API
        self.validate_fund_flow_apis()

        # 3. 市场数据API
        self.validate_market_data_apis()

        # 4. 政策数据API
        self.validate_policy_apis()

        # 5. 生成校验报告
        self.generate_validation_report()

        return self.validation_results

    def validate_news_apis(self):
        """验证新闻数据API"""
        self.logger.info("=== 验证新闻数据API ===")

        news_apis = {
            'stock_news_em': {
                'function': ak.stock_news_em,
                'params': {},
                'description': '东方财富股票新闻'
            },
            'news_cctv': {
                'function': ak.news_cctv,
                'params': {},
                'description': '央视财经新闻'
            },
            'stock_news_main_cx': {
                'function': ak.stock_news_main_cx,
                'params': {},
                'description': '财经内容精选'
            }
        }

        for api_name, api_info in news_apis.items():
            self.validate_single_api('news', api_name, api_info)

    def validate_fund_flow_apis(self):
        """验证资金流数据API"""
        self.logger.info("=== 验证资金流数据API ===")

        fund_flow_apis = {
            'stock_hsgt_fund_flow_summary_em': {
                'function': ak.stock_hsgt_fund_flow_summary_em,
                'params': {},
                'description': '北向资金流向汇总'
            },
            'stock_fund_flow_industry': {
                'function': ak.stock_fund_flow_industry,
                'params': {'symbol': '即时'},
                'description': '行业资金流向'
            },
            'stock_fund_flow_individual': {
                'function': ak.stock_fund_flow_individual,
                'params': {'symbol': '即时'},
                'description': '个股资金流向'
            },
            'stock_fund_flow_concept': {
                'function': ak.stock_fund_flow_concept,
                'params': {'symbol': '即时'},
                'description': '概念资金流向'
            },
            'stock_sector_fund_flow_rank': {
                'function': ak.stock_sector_fund_flow_rank,
                'params': {'indicator': '今日'},
                'description': '板块资金流向排名'
            }
        }

        for api_name, api_info in fund_flow_apis.items():
            self.validate_single_api('fund_flow', api_name, api_info)

    def validate_market_data_apis(self):
        """验证市场数据API"""
        self.logger.info("=== 验证市场数据API ===")

        market_apis = {
            'stock_zh_a_spot_em': {
                'function': ak.stock_zh_a_spot_em,
                'params': {},
                'description': 'A股实时行情'
            },
            'stock_zh_index_daily': {
                'function': ak.stock_zh_index_daily,
                'params': {'symbol': 'sh000001'},
                'description': '指数日线数据'
            },
            'stock_board_industry_name_em': {
                'function': ak.stock_board_industry_name_em,
                'params': {},
                'description': '行业板块名称'
            }
        }

        for api_name, api_info in market_apis.items():
            self.validate_single_api('market', api_name, api_info)

    def validate_policy_apis(self):
        """验证政策数据API（网络爬虫）"""
        self.logger.info("=== 验证政策数据API ===")

        # 政策数据主要通过网络爬虫获取，这里验证基础功能
        policy_sources = {
            'gov_cn_policies': {
                'url': 'https://www.gov.cn/zhengce/zhengcewenjianku/',
                'description': '中国政府网政策文件库'
            },
            'ndrc_policies': {
                'url': 'https://www.ndrc.gov.cn/xxgk/',
                'description': '发改委政策信息'
            }
        }

        self.validation_results['policy'] = {
            'status': 'manual_check_required',
            'message': '政策数据需要网络爬虫实现，需要手动验证网站可访问性',
            'sources': policy_sources
        }

    def validate_single_api(self, category: str, api_name: str, api_info: Dict):
        """验证单个API"""
        self.logger.info(f"验证 {api_name}: {api_info['description']}")

        result = {
            'api_name': api_name,
            'description': api_info['description'],
            'status': 'unknown',
            'data_sample': None,
            'data_shape': None,
            'columns': None,
            'error_message': None,
            'execution_time': None,
            'storage_test': False
        }

        try:
            # 记录开始时间
            start_time = time.time()

            # 调用API
            if api_info['params']:
                data = api_info['function'](**api_info['params'])
            else:
                data = api_info['function']()

            # 记录执行时间
            execution_time = time.time() - start_time
            result['execution_time'] = round(execution_time, 2)

            # 检查数据
            if data is not None and not data.empty:
                result['status'] = 'success'
                result['data_shape'] = data.shape
                result['columns'] = list(data.columns) if hasattr(data, 'columns') else None

                # 获取数据样本（前3行）
                if hasattr(data, 'head'):
                    sample_data = data.head(3)
                    result['data_sample'] = sample_data.to_dict('records') if hasattr(sample_data, 'to_dict') else str(sample_data)

                # 测试数据存储
                try:
                    storage_success = self.data_storage.store_data(
                        data_type=f"{category}_{api_name}",
                        data=data,
                        level=StorageLevel.HOT
                    )
                    result['storage_test'] = storage_success

                    if storage_success:
                        self.logger.info(f"✅ {api_name} 数据存储成功")
                    else:
                        self.logger.warning(f"⚠️ {api_name} 数据存储失败")

                except Exception as e:
                    result['storage_test'] = False
                    self.logger.error(f"❌ {api_name} 数据存储异常: {str(e)}")

                self.logger.info(f"✅ {api_name} 验证成功 - 数据形状: {data.shape}, 执行时间: {execution_time:.2f}s")

            else:
                result['status'] = 'empty_data'
                result['error_message'] = '返回数据为空'
                self.logger.warning(f"⚠️ {api_name} 返回数据为空")

        except Exception as e:
            result['status'] = 'error'
            result['error_message'] = str(e)
            self.logger.error(f"❌ {api_name} 验证失败: {str(e)}")

        # 保存结果
        if category not in self.validation_results:
            self.validation_results[category] = {}
        self.validation_results[category][api_name] = result

    def generate_validation_report(self):
        """生成验证报告"""
        self.logger.info("=== 生成API验证报告 ===")

        # 统计信息
        total_apis = 0
        success_apis = 0
        failed_apis = 0
        empty_data_apis = 0

        for category, apis in self.validation_results.items():
            if isinstance(apis, dict) and 'status' not in apis:  # 排除policy类别
                for api_name, result in apis.items():
                    total_apis += 1
                    if result['status'] == 'success':
                        success_apis += 1
                    elif result['status'] == 'error':
                        failed_apis += 1
                    elif result['status'] == 'empty_data':
                        empty_data_apis += 1

        # 生成报告
        report = {
            'validation_time': datetime.now().isoformat(),
            'summary': {
                'total_apis': total_apis,
                'success_apis': success_apis,
                'failed_apis': failed_apis,
                'empty_data_apis': empty_data_apis,
                'success_rate': round(success_apis / total_apis * 100, 2) if total_apis > 0 else 0
            },
            'detailed_results': self.validation_results
        }

        # 保存报告
        os.makedirs('reports', exist_ok=True)
        report_file = f"reports/api_validation_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"

        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)

        # 打印摘要
        self.logger.info("=== API验证摘要 ===")
        self.logger.info(f"总API数量: {total_apis}")
        self.logger.info(f"成功: {success_apis}")
        self.logger.info(f"失败: {failed_apis}")
        self.logger.info(f"空数据: {empty_data_apis}")
        self.logger.info(f"成功率: {report['summary']['success_rate']}%")
        self.logger.info(f"详细报告已保存至: {report_file}")

        return report

def main():
    """主函数"""
    print("🔍 开始API数据获取校验...")

    # 创建必要的目录
    os.makedirs('logs', exist_ok=True)
    os.makedirs('reports', exist_ok=True)

    # 创建验证器
    validator = APIValidator()

    # 执行验证
    results = validator.validate_all_apis()

    print("\n🎯 API验证完成！请查看生成的报告文件。")

    return results

if __name__ == "__main__":
    main()
