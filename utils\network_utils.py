"""
Network utilities for policy_liquidity_volatility_arbitrage.
"""

import asyncio
import aiohttp
import requests
from concurrent.futures import ThreadPoolExecutor
from utils.logger import logger
from utils.error_utils import retry

@retry(max_retries=3, delay=2)
def make_request(url, method="GET", params=None, headers=None, timeout=30):
    """
    Make a synchronous HTTP request.
    
    Args:
        url (str): URL to request.
        method (str): HTTP method (GET, POST, etc.).
        params (dict, optional): Request parameters.
        headers (dict, optional): Request headers.
        timeout (int): Request timeout in seconds.
        
    Returns:
        requests.Response: Response object.
    """
    try:
        response = requests.request(
            method=method,
            url=url,
            params=params,
            headers=headers,
            timeout=timeout
        )
        response.raise_for_status()
        return response
    except requests.exceptions.RequestException as e:
        logger.error(f"Request error for {url}: {str(e)}")
        raise

async def make_async_request(url, method="GET", params=None, headers=None, timeout=30):
    """
    Make an asynchronous HTTP request.
    
    Args:
        url (str): URL to request.
        method (str): HTTP method (GET, POST, etc.).
        params (dict, optional): Request parameters.
        headers (dict, optional): Request headers.
        timeout (int): Request timeout in seconds.
        
    Returns:
        dict: Response data.
    """
    async with aiohttp.ClientSession() as session:
        try:
            async with session.request(
                method=method,
                url=url,
                params=params,
                headers=headers,
                timeout=aiohttp.ClientTimeout(total=timeout)
            ) as response:
                if response.status >= 400:
                    logger.error(f"Async request error for {url}: HTTP {response.status}")
                    return None
                
                if response.content_type == 'application/json':
                    return await response.json()
                else:
                    return await response.text()
        except aiohttp.ClientError as e:
            logger.error(f"Async request error for {url}: {str(e)}")
            return None

async def fetch_all_async(urls, method="GET", params=None, headers=None, timeout=30):
    """
    Fetch multiple URLs asynchronously.
    
    Args:
        urls (list): List of URLs to fetch.
        method (str): HTTP method (GET, POST, etc.).
        params (dict, optional): Request parameters.
        headers (dict, optional): Request headers.
        timeout (int): Request timeout in seconds.
        
    Returns:
        list: List of response data.
    """
    tasks = []
    for url in urls:
        task = asyncio.create_task(make_async_request(
            url=url,
            method=method,
            params=params,
            headers=headers,
            timeout=timeout
        ))
        tasks.append(task)
    
    return await asyncio.gather(*tasks)

def fetch_all_parallel(urls, method="GET", params=None, headers=None, timeout=30, max_workers=10):
    """
    Fetch multiple URLs in parallel using threads.
    
    Args:
        urls (list): List of URLs to fetch.
        method (str): HTTP method (GET, POST, etc.).
        params (dict, optional): Request parameters.
        headers (dict, optional): Request headers.
        timeout (int): Request timeout in seconds.
        max_workers (int): Maximum number of worker threads.
        
    Returns:
        list: List of response objects.
    """
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        results = list(executor.map(
            lambda url: make_request(url, method, params, headers, timeout),
            urls
        ))
    
    return results
