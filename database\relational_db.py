"""
关系型数据库客户端

实现PostgreSQL数据库的访问
"""

import logging
import json
from typing import Dict, Any, List, Optional, Union
import sqlite3
import os

from .db_client import DBClient
from .data_types import RELATIONAL_DATA_TYPES

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('relational_db')

class PostgreSQLClient(DBClient):
    """PostgreSQL数据库客户端"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化PostgreSQL数据库客户端
        
        Args:
            config: 配置信息
        """
        super().__init__(config)
        
        # 如果配置了使用SQLite作为回退，则初始化SQLite连接
        self.use_sqlite_fallback = config.get('use_sqlite_fallback', True)
        self.sqlite_db_path = config.get('sqlite_db_path', os.path.join('data', 'financial_system.db'))
        self.sqlite_connection = None
        
        # 尝试连接数据库
        self.connect()
    
    def connect(self) -> bool:
        """
        连接数据库
        
        Returns:
            连接结果
        """
        try:
            # 尝试导入psycopg2
            import psycopg2
            import psycopg2.extras
            
            # 连接PostgreSQL
            self.connection = psycopg2.connect(
                host=self.config.get('host', 'localhost'),
                port=self.config.get('port', 5432),
                database=self.config.get('database', 'financial_system'),
                user=self.config.get('user', 'postgres'),
                password=self.config.get('password', 'postgres')
            )
            
            self._log_info("PostgreSQL数据库连接成功")
            return True
        
        except ImportError:
            self._log_error("psycopg2模块未安装，无法连接PostgreSQL", Exception("Module not found"))
            
            # 如果配置了使用SQLite作为回退，则连接SQLite
            if self.use_sqlite_fallback:
                return self._connect_sqlite()
            
            return False
        
        except Exception as e:
            self._log_error("PostgreSQL数据库连接失败", e)
            
            # 如果配置了使用SQLite作为回退，则连接SQLite
            if self.use_sqlite_fallback:
                return self._connect_sqlite()
            
            return False
    
    def _connect_sqlite(self) -> bool:
        """
        连接SQLite数据库
        
        Returns:
            连接结果
        """
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(self.sqlite_db_path), exist_ok=True)
            
            # 连接SQLite
            self.sqlite_connection = sqlite3.connect(self.sqlite_db_path)
            self.sqlite_connection.row_factory = sqlite3.Row
            
            # 初始化表结构
            self._init_sqlite_tables()
            
            self._log_info("SQLite数据库连接成功（作为PostgreSQL的回退）")
            return True
        
        except Exception as e:
            self._log_error("SQLite数据库连接失败", e)
            return False
    
    def _init_sqlite_tables(self) -> None:
        """初始化SQLite表结构"""
        cursor = self.sqlite_connection.cursor()
        
        # 为每个关系型数据类型创建表
        for data_type, info in RELATIONAL_DATA_TYPES.items():
            table_name = info.get('table')
            if not table_name:
                continue
            
            # 检查表是否存在
            cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table_name}'")
            if cursor.fetchone() is None:
                # 创建表
                if table_name == 'stocks':
                    cursor.execute('''
                    CREATE TABLE stocks (
                        code TEXT PRIMARY KEY,
                        name TEXT NOT NULL,
                        listing_date TEXT,
                        industry_code TEXT,
                        sector_code TEXT,
                        market TEXT,
                        is_st INTEGER DEFAULT 0,
                        is_suspended INTEGER DEFAULT 0,
                        updated_at TEXT DEFAULT CURRENT_TIMESTAMP
                    )
                    ''')
                
                elif table_name == 'industries':
                    cursor.execute('''
                    CREATE TABLE industries (
                        code TEXT PRIMARY KEY,
                        name TEXT NOT NULL,
                        level INTEGER NOT NULL,
                        parent_code TEXT,
                        description TEXT,
                        updated_at TEXT DEFAULT CURRENT_TIMESTAMP
                    )
                    ''')
                
                elif table_name == 'trading_calendar':
                    cursor.execute('''
                    CREATE TABLE trading_calendar (
                        date TEXT PRIMARY KEY,
                        is_trading_day INTEGER NOT NULL,
                        week_of_year INTEGER,
                        day_of_week INTEGER,
                        quarter INTEGER,
                        month INTEGER,
                        description TEXT
                    )
                    ''')
                
                elif table_name == 'metadata_index':
                    cursor.execute('''
                    CREATE TABLE metadata_index (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        data_type TEXT NOT NULL,
                        reference_id TEXT NOT NULL,
                        storage_type TEXT NOT NULL,
                        storage_location TEXT NOT NULL,
                        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                        updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
                        UNIQUE(data_type, reference_id)
                    )
                    ''')
                
                elif table_name == 'data_sources':
                    cursor.execute('''
                    CREATE TABLE data_sources (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        name TEXT NOT NULL,
                        type TEXT NOT NULL,
                        config TEXT,
                        is_active INTEGER DEFAULT 1,
                        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                        updated_at TEXT DEFAULT CURRENT_TIMESTAMP
                    )
                    ''')
                
                elif table_name == 'system_params':
                    cursor.execute('''
                    CREATE TABLE system_params (
                        param_key TEXT PRIMARY KEY,
                        param_value TEXT,
                        description TEXT,
                        updated_at TEXT DEFAULT CURRENT_TIMESTAMP
                    )
                    ''')
                
                elif table_name == 'user_settings':
                    cursor.execute('''
                    CREATE TABLE user_settings (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        user_id TEXT NOT NULL,
                        setting_key TEXT NOT NULL,
                        setting_value TEXT,
                        updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
                        UNIQUE(user_id, setting_key)
                    )
                    ''')
        
        self.sqlite_connection.commit()
    
    def disconnect(self) -> bool:
        """
        断开数据库连接
        
        Returns:
            断开结果
        """
        try:
            if self.connection:
                self.connection.close()
                self.connection = None
            
            if self.sqlite_connection:
                self.sqlite_connection.close()
                self.sqlite_connection = None
            
            self._log_info("数据库连接已断开")
            return True
        
        except Exception as e:
            self._log_error("断开数据库连接失败", e)
            return False
    
    def is_connected(self) -> bool:
        """
        检查数据库连接状态
        
        Returns:
            连接状态
        """
        if self.connection:
            try:
                # 尝试执行简单查询
                cursor = self.connection.cursor()
                cursor.execute("SELECT 1")
                return True
            except:
                return False
        
        if self.sqlite_connection:
            try:
                # 尝试执行简单查询
                cursor = self.sqlite_connection.cursor()
                cursor.execute("SELECT 1")
                return True
            except:
                return False
        
        return False
    
    def query(self, data_type: str, query_params: Dict[str, Any], options: Optional[Dict[str, Any]] = None) -> Any:
        """
        查询数据
        
        Args:
            data_type: 数据类型
            query_params: 查询参数
            options: 选项参数
        
        Returns:
            查询结果
        """
        options = options or {}
        
        if data_type not in RELATIONAL_DATA_TYPES:
            raise ValueError(f"未知的关系型数据类型: {data_type}")
        
        table_name = RELATIONAL_DATA_TYPES[data_type].get('table')
        if not table_name:
            raise ValueError(f"数据类型 {data_type} 没有指定表名")
        
        # 使用PostgreSQL或SQLite执行查询
        if self.connection:
            return self._query_postgresql(table_name, query_params, options)
        elif self.sqlite_connection:
            return self._query_sqlite(table_name, query_params, options)
        else:
            raise ConnectionError("数据库未连接")
    
    def _query_postgresql(self, table_name: str, query_params: Dict[str, Any], options: Dict[str, Any]) -> Any:
        """
        使用PostgreSQL执行查询
        
        Args:
            table_name: 表名
            query_params: 查询参数
            options: 选项参数
        
        Returns:
            查询结果
        """
        try:
            cursor = self.connection.cursor(cursor_factory=psycopg2.extras.DictCursor)
            
            # 构建查询语句
            if query_params:
                conditions = []
                params = []
                for key, value in query_params.items():
                    conditions.append(f"{key} = %s")
                    params.append(value)
                
                query = f"SELECT * FROM {table_name} WHERE {' AND '.join(conditions)}"
                cursor.execute(query, params)
            else:
                query = f"SELECT * FROM {table_name}"
                cursor.execute(query)
            
            # 获取结果
            rows = cursor.fetchall()
            result = [dict(row) for row in rows]
            
            # 如果只有一条记录且options中指定了single=True，则返回单个记录
            if options.get('single', False) and len(result) == 1:
                return result[0]
            
            return result
        
        except Exception as e:
            self._log_error(f"PostgreSQL查询失败: {table_name}", e)
            raise
    
    def _query_sqlite(self, table_name: str, query_params: Dict[str, Any], options: Dict[str, Any]) -> Any:
        """
        使用SQLite执行查询
        
        Args:
            table_name: 表名
            query_params: 查询参数
            options: 选项参数
        
        Returns:
            查询结果
        """
        try:
            cursor = self.sqlite_connection.cursor()
            
            # 构建查询语句
            if query_params:
                conditions = []
                params = []
                for key, value in query_params.items():
                    conditions.append(f"{key} = ?")
                    params.append(value)
                
                query = f"SELECT * FROM {table_name} WHERE {' AND '.join(conditions)}"
                cursor.execute(query, params)
            else:
                query = f"SELECT * FROM {table_name}"
                cursor.execute(query)
            
            # 获取结果
            rows = cursor.fetchall()
            result = [dict(zip([column[0] for column in cursor.description], row)) for row in rows]
            
            # 如果只有一条记录且options中指定了single=True，则返回单个记录
            if options.get('single', False) and len(result) == 1:
                return result[0]
            
            return result
        
        except Exception as e:
            self._log_error(f"SQLite查询失败: {table_name}", e)
            raise
    
    def save(self, data_type: str, data: Any, options: Optional[Dict[str, Any]] = None) -> Any:
        """
        保存数据
        
        Args:
            data_type: 数据类型
            data: 要保存的数据
            options: 选项参数
        
        Returns:
            保存结果
        """
        options = options or {}
        
        if data_type not in RELATIONAL_DATA_TYPES:
            raise ValueError(f"未知的关系型数据类型: {data_type}")
        
        table_name = RELATIONAL_DATA_TYPES[data_type].get('table')
        if not table_name:
            raise ValueError(f"数据类型 {data_type} 没有指定表名")
        
        # 使用PostgreSQL或SQLite保存数据
        if self.connection:
            return self._save_postgresql(table_name, data, options)
        elif self.sqlite_connection:
            return self._save_sqlite(table_name, data, options)
        else:
            raise ConnectionError("数据库未连接")
    
    def _save_postgresql(self, table_name: str, data: Any, options: Dict[str, Any]) -> Any:
        """
        使用PostgreSQL保存数据
        
        Args:
            table_name: 表名
            data: 要保存的数据
            options: 选项参数
        
        Returns:
            保存结果
        """
        try:
            cursor = self.connection.cursor()
            
            # 处理单条数据或多条数据
            if isinstance(data, dict):
                result = self._save_single_record_postgresql(cursor, table_name, data, options)
            elif isinstance(data, list) and all(isinstance(item, dict) for item in data):
                result = [self._save_single_record_postgresql(cursor, table_name, item, options) for item in data]
            else:
                raise ValueError("数据必须是字典或字典列表")
            
            self.connection.commit()
            return result
        
        except Exception as e:
            self.connection.rollback()
            self._log_error(f"PostgreSQL保存失败: {table_name}", e)
            raise
    
    def _save_single_record_postgresql(self, cursor, table_name: str, data: Dict[str, Any], options: Dict[str, Any]) -> Any:
        """
        使用PostgreSQL保存单条记录
        
        Args:
            cursor: 数据库游标
            table_name: 表名
            data: 要保存的数据
            options: 选项参数
        
        Returns:
            保存结果
        """
        # 检查是否为更新操作
        is_update = options.get('update', False)
        primary_key = RELATIONAL_DATA_TYPES.get(table_name, {}).get('primary_key')
        
        if is_update and primary_key and primary_key in data:
            # 更新记录
            set_items = []
            params = []
            for key, value in data.items():
                if key != primary_key:
                    set_items.append(f"{key} = %s")
                    params.append(value)
            
            params.append(data[primary_key])
            query = f"UPDATE {table_name} SET {', '.join(set_items)} WHERE {primary_key} = %s RETURNING *"
            cursor.execute(query, params)
        else:
            # 插入记录
            columns = list(data.keys())
            placeholders = ["%s"] * len(columns)
            values = [data[column] for column in columns]
            
            query = f"INSERT INTO {table_name} ({', '.join(columns)}) VALUES ({', '.join(placeholders)}) RETURNING *"
            cursor.execute(query, values)
        
        # 返回插入或更新后的记录
        return dict(cursor.fetchone())
    
    def _save_sqlite(self, table_name: str, data: Any, options: Dict[str, Any]) -> Any:
        """
        使用SQLite保存数据
        
        Args:
            table_name: 表名
            data: 要保存的数据
            options: 选项参数
        
        Returns:
            保存结果
        """
        try:
            cursor = self.sqlite_connection.cursor()
            
            # 处理单条数据或多条数据
            if isinstance(data, dict):
                result = self._save_single_record_sqlite(cursor, table_name, data, options)
            elif isinstance(data, list) and all(isinstance(item, dict) for item in data):
                result = [self._save_single_record_sqlite(cursor, table_name, item, options) for item in data]
            else:
                raise ValueError("数据必须是字典或字典列表")
            
            self.sqlite_connection.commit()
            return result
        
        except Exception as e:
            self.sqlite_connection.rollback()
            self._log_error(f"SQLite保存失败: {table_name}", e)
            raise
    
    def _save_single_record_sqlite(self, cursor, table_name: str, data: Dict[str, Any], options: Dict[str, Any]) -> Any:
        """
        使用SQLite保存单条记录
        
        Args:
            cursor: 数据库游标
            table_name: 表名
            data: 要保存的数据
            options: 选项参数
        
        Returns:
            保存结果
        """
        # 检查是否为更新操作
        is_update = options.get('update', False)
        primary_key = RELATIONAL_DATA_TYPES.get(table_name, {}).get('primary_key')
        
        if is_update and primary_key and primary_key in data:
            # 更新记录
            set_items = []
            params = []
            for key, value in data.items():
                if key != primary_key:
                    set_items.append(f"{key} = ?")
                    params.append(value)
            
            params.append(data[primary_key])
            query = f"UPDATE {table_name} SET {', '.join(set_items)} WHERE {primary_key} = ?"
            cursor.execute(query, params)
        else:
            # 插入记录
            columns = list(data.keys())
            placeholders = ["?"] * len(columns)
            values = [data[column] for column in columns]
            
            query = f"INSERT INTO {table_name} ({', '.join(columns)}) VALUES ({', '.join(placeholders)})"
            cursor.execute(query, values)
        
        # 返回插入或更新后的记录
        if primary_key and primary_key in data:
            cursor.execute(f"SELECT * FROM {table_name} WHERE {primary_key} = ?", (data[primary_key],))
            row = cursor.fetchone()
            if row:
                return dict(zip([column[0] for column in cursor.description], row))
        
        return data
    
    def delete(self, data_type: str, query_params: Dict[str, Any], options: Optional[Dict[str, Any]] = None) -> bool:
        """
        删除数据
        
        Args:
            data_type: 数据类型
            query_params: 查询参数
            options: 选项参数
        
        Returns:
            删除结果
        """
        options = options or {}
        
        if data_type not in RELATIONAL_DATA_TYPES:
            raise ValueError(f"未知的关系型数据类型: {data_type}")
        
        table_name = RELATIONAL_DATA_TYPES[data_type].get('table')
        if not table_name:
            raise ValueError(f"数据类型 {data_type} 没有指定表名")
        
        if not query_params:
            raise ValueError("删除操作必须指定查询参数")
        
        # 使用PostgreSQL或SQLite删除数据
        if self.connection:
            return self._delete_postgresql(table_name, query_params, options)
        elif self.sqlite_connection:
            return self._delete_sqlite(table_name, query_params, options)
        else:
            raise ConnectionError("数据库未连接")
    
    def _delete_postgresql(self, table_name: str, query_params: Dict[str, Any], options: Dict[str, Any]) -> bool:
        """
        使用PostgreSQL删除数据
        
        Args:
            table_name: 表名
            query_params: 查询参数
            options: 选项参数
        
        Returns:
            删除结果
        """
        try:
            cursor = self.connection.cursor()
            
            # 构建删除语句
            conditions = []
            params = []
            for key, value in query_params.items():
                conditions.append(f"{key} = %s")
                params.append(value)
            
            query = f"DELETE FROM {table_name} WHERE {' AND '.join(conditions)}"
            cursor.execute(query, params)
            
            self.connection.commit()
            return cursor.rowcount > 0
        
        except Exception as e:
            self.connection.rollback()
            self._log_error(f"PostgreSQL删除失败: {table_name}", e)
            raise
    
    def _delete_sqlite(self, table_name: str, query_params: Dict[str, Any], options: Dict[str, Any]) -> bool:
        """
        使用SQLite删除数据
        
        Args:
            table_name: 表名
            query_params: 查询参数
            options: 选项参数
        
        Returns:
            删除结果
        """
        try:
            cursor = self.sqlite_connection.cursor()
            
            # 构建删除语句
            conditions = []
            params = []
            for key, value in query_params.items():
                conditions.append(f"{key} = ?")
                params.append(value)
            
            query = f"DELETE FROM {table_name} WHERE {' AND '.join(conditions)}"
            cursor.execute(query, params)
            
            self.sqlite_connection.commit()
            return cursor.rowcount > 0
        
        except Exception as e:
            self.sqlite_connection.rollback()
            self._log_error(f"SQLite删除失败: {table_name}", e)
            raise
