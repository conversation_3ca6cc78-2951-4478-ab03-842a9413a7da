#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
增强版政策情感分析模块
使用更先进的NLP模型提高政策情感分析的准确性
"""

import os
import sys
import json
import logging
import numpy as np
import pandas as pd
from datetime import datetime
from typing import Dict, List, Tuple, Any, Optional, Union

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入必要的模块
from core.module_interface import ModuleInterface
from core.data_storage import DataStorage, StorageLevel

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('logs/enhanced_policy_sentiment.log')
    ]
)
logger = logging.getLogger("EnhancedPolicySentiment")

try:
    import torch
    from transformers import BertTokenizer, BertForSequenceClassification
    from transformers import pipeline
    TRANSFORMERS_AVAILABLE = True
except ImportError:
    logger.warning("transformers库不可用，将使用备用情感分析方法")
    TRANSFORMERS_AVAILABLE = False

try:
    import jieba
    import jieba.analyse
    JIEBA_AVAILABLE = True
except ImportError:
    logger.warning("jieba库不可用，将使用备用分词方法")
    JIEBA_AVAILABLE = False

class EnhancedPolicySentiment:
    """增强版政策情感分析类"""

    def __init__(self, data_storage: Optional[DataStorage] = None):
        """
        初始化增强版政策情感分析

        Args:
            data_storage: 数据存储对象
        """
        self.data_storage = data_storage

        # 初始化模型
        self.sentiment_model = None
        self.tokenizer = None
        self.sentiment_pipeline = None

        # 加载模型
        self.load_models()

        # 加载情感词典
        self.sentiment_dict = self.load_sentiment_dict()

        # 加载行业关键词
        self.industry_keywords = self.load_industry_keywords()

        logger.info("增强版政策情感分析模块初始化完成")

    def load_models(self) -> None:
        """加载NLP模型"""
        if TRANSFORMERS_AVAILABLE:
            try:
                # 使用预训练的中文BERT模型
                model_name = "bert-base-chinese"

                # 检查是否有FinBERT模型
                finbert_path = "models/finbert-chinese"
                if os.path.exists(finbert_path):
                    model_name = finbert_path
                    logger.info(f"使用本地FinBERT模型: {finbert_path}")

                # 加载tokenizer
                self.tokenizer = BertTokenizer.from_pretrained(model_name)

                # 加载模型
                self.sentiment_model = BertForSequenceClassification.from_pretrained(
                    model_name,
                    num_labels=3  # 积极、消极、中性
                )

                # 创建情感分析pipeline
                self.sentiment_pipeline = pipeline(
                    "sentiment-analysis",
                    model=self.sentiment_model,
                    tokenizer=self.tokenizer,
                    device=0 if torch.cuda.is_available() else -1
                )

                logger.info(f"成功加载BERT模型: {model_name}")
                logger.info(f"使用GPU: {torch.cuda.is_available()}")

            except Exception as e:
                logger.error(f"加载BERT模型失败: {str(e)}")
                self.sentiment_model = None
                self.tokenizer = None
                self.sentiment_pipeline = None

    def load_sentiment_dict(self) -> Dict[str, float]:
        """
        加载情感词典

        Returns:
            Dict[str, float]: 情感词典，键为词语，值为情感得分
        """
        sentiment_dict = {}

        try:
            # 尝试从数据存储加载
            if self.data_storage:
                dict_data = self.data_storage.load('policy_analyzer', 'sentiment_dict')
                if dict_data:
                    return dict_data

            # 如果数据存储中没有，则从文件加载
            dict_path = "data/sentiment_dict.json"
            if os.path.exists(dict_path):
                with open(dict_path, 'r', encoding='utf-8') as f:
                    sentiment_dict = json.load(f)
                logger.info(f"从文件加载情感词典: {len(sentiment_dict)}个词")
            else:
                # 创建基本的情感词典
                sentiment_dict = {
                    "利好": 1.0, "积极": 0.8, "促进": 0.6, "提高": 0.5, "增加": 0.5,
                    "支持": 0.7, "鼓励": 0.7, "优化": 0.6, "改善": 0.6, "加强": 0.5,
                    "利空": -1.0, "消极": -0.8, "限制": -0.6, "降低": -0.5, "减少": -0.5,
                    "抑制": -0.7, "打击": -0.7, "整顿": -0.6, "调控": -0.4, "监管": -0.3
                }
                logger.info("使用默认情感词典")

            # 保存到数据存储
            if self.data_storage:
                self.data_storage.save('policy_analyzer', 'sentiment_dict', sentiment_dict, StorageLevel.WARM)

            return sentiment_dict

        except Exception as e:
            logger.error(f"加载情感词典失败: {str(e)}")
            return {
                "利好": 1.0, "积极": 0.8, "促进": 0.6, "提高": 0.5, "增加": 0.5,
                "支持": 0.7, "鼓励": 0.7, "优化": 0.6, "改善": 0.6, "加强": 0.5,
                "利空": -1.0, "消极": -0.8, "限制": -0.6, "降低": -0.5, "减少": -0.5,
                "抑制": -0.7, "打击": -0.7, "整顿": -0.6, "调控": -0.4, "监管": -0.3
            }

    def load_industry_keywords(self) -> Dict[str, List[str]]:
        """
        加载行业关键词

        Returns:
            Dict[str, List[str]]: 行业关键词，键为行业名称，值为关键词列表
        """
        industry_keywords = {}

        try:
            # 尝试从数据存储加载
            if self.data_storage:
                keywords_data = self.data_storage.load('policy_analyzer', 'industry_keywords')
                if keywords_data:
                    return keywords_data

            # 如果数据存储中没有，则从文件加载
            keywords_path = "data/industry_keywords.json"
            if os.path.exists(keywords_path):
                with open(keywords_path, 'r', encoding='utf-8') as f:
                    industry_keywords = json.load(f)
                logger.info(f"从文件加载行业关键词: {len(industry_keywords)}个行业")
            else:
                # 创建基本的行业关键词
                industry_keywords = {
                    "银行": ["银行", "存款", "贷款", "利率", "金融机构", "理财", "存贷比"],
                    "保险": ["保险", "保费", "理赔", "寿险", "财险", "健康险", "养老金"],
                    "证券": ["证券", "股票", "债券", "基金", "投资", "资本市场", "交易所"],
                    "房地产": ["房地产", "楼市", "地产", "房价", "住宅", "商铺", "土地"],
                    "医药": ["医药", "药品", "医疗", "生物", "疫苗", "器械", "诊断"],
                    "计算机": ["计算机", "软件", "互联网", "信息技术", "云计算", "大数据", "人工智能"],
                    "电子": ["电子", "芯片", "半导体", "集成电路", "面板", "消费电子", "元器件"],
                    "通信": ["通信", "5G", "运营商", "基站", "光纤", "网络", "终端"],
                    "汽车": ["汽车", "新能源车", "零部件", "整车", "充电桩", "智能驾驶", "电池"],
                    "食品饮料": ["食品", "饮料", "白酒", "乳业", "调味品", "餐饮", "零食"]
                }
                logger.info("使用默认行业关键词")

            # 保存到数据存储
            if self.data_storage:
                self.data_storage.save('policy_analyzer', 'industry_keywords', industry_keywords, StorageLevel.WARM)

            return industry_keywords

        except Exception as e:
            logger.error(f"加载行业关键词失败: {str(e)}")
            return {
                "银行": ["银行", "存款", "贷款", "利率", "金融机构"],
                "医药": ["医药", "药品", "医疗", "生物", "疫苗"],
                "科技": ["科技", "互联网", "软件", "人工智能", "大数据"]
            }

    def analyze_policy_sentiment(self, policy_text: str) -> Dict[str, Any]:
        """
        分析政策情感

        Args:
            policy_text: 政策文本

        Returns:
            Dict[str, Any]: 情感分析结果
        """
        # 使用多种方法分析情感，并综合结果
        bert_sentiment = self.analyze_with_bert(policy_text)
        dict_sentiment = self.analyze_with_dict(policy_text)

        # 综合两种方法的结果
        sentiment_score = 0.0
        sentiment_label = "中性"

        if bert_sentiment and dict_sentiment:
            # BERT模型结果权重0.7，词典方法权重0.3
            sentiment_score = bert_sentiment['score'] * 0.7 + dict_sentiment['score'] * 0.3
        elif bert_sentiment:
            sentiment_score = bert_sentiment['score']
        elif dict_sentiment:
            sentiment_score = dict_sentiment['score']

        # 根据得分确定情感标签
        if sentiment_score > 0.2:
            sentiment_label = "积极"
        elif sentiment_score < -0.2:
            sentiment_label = "消极"

        # 提取关键词
        keywords = self.extract_keywords(policy_text)

        # 识别相关行业
        related_industries = self.identify_related_industries(policy_text, keywords)

        return {
            'score': sentiment_score,
            'label': sentiment_label,
            'keywords': keywords,
            'related_industries': related_industries,
            'bert_result': bert_sentiment,
            'dict_result': dict_sentiment
        }

    def analyze_with_bert(self, text: str) -> Optional[Dict[str, Any]]:
        """
        使用BERT模型分析情感

        Args:
            text: 文本内容

        Returns:
            Optional[Dict[str, Any]]: BERT情感分析结果
        """
        if not TRANSFORMERS_AVAILABLE or not self.sentiment_pipeline:
            return None

        try:
            # 截取文本，避免超出BERT模型的最大长度
            max_length = 512
            if len(text) > max_length:
                # 取开头和结尾的文本
                text = text[:max_length//2] + text[-max_length//2:]

            # 使用情感分析pipeline
            result = self.sentiment_pipeline(text)[0]

            # 转换结果
            label = result['label']
            score = result['score']

            # 标准化得分
            if label == 'POSITIVE':
                sentiment_score = score
            elif label == 'NEGATIVE':
                sentiment_score = -score
            else:
                sentiment_score = 0.0

            return {
                'score': sentiment_score,
                'label': label,
                'confidence': score
            }

        except Exception as e:
            logger.error(f"BERT情感分析失败: {str(e)}")
            return None

    def analyze_with_dict(self, text: str) -> Dict[str, Any]:
        """
        使用情感词典分析情感

        Args:
            text: 文本内容

        Returns:
            Dict[str, Any]: 词典情感分析结果
        """
        try:
            # 分词
            if JIEBA_AVAILABLE:
                words = jieba.lcut(text)
            else:
                # 简单的分词方法
                words = list(text)

            # 计算情感得分
            total_score = 0.0
            sentiment_words = []

            for word in words:
                if word in self.sentiment_dict:
                    score = self.sentiment_dict[word]
                    total_score += score
                    sentiment_words.append({
                        'word': word,
                        'score': score
                    })

            # 归一化得分
            if sentiment_words:
                normalized_score = total_score / len(sentiment_words)
            else:
                normalized_score = 0.0

            # 确定情感标签
            if normalized_score > 0.2:
                sentiment_label = "积极"
            elif normalized_score < -0.2:
                sentiment_label = "消极"
            else:
                sentiment_label = "中性"

            return {
                'score': normalized_score,
                'label': sentiment_label,
                'sentiment_words': sentiment_words,
                'total_score': total_score,
                'word_count': len(sentiment_words)
            }

        except Exception as e:
            logger.error(f"词典情感分析失败: {str(e)}")
            return {
                'score': 0.0,
                'label': "中性",
                'sentiment_words': [],
                'total_score': 0.0,
                'word_count': 0
            }

    def extract_keywords(self, text: str, top_n: int = 10) -> List[Dict[str, Any]]:
        """
        提取文本关键词

        Args:
            text: 文本内容
            top_n: 返回的关键词数量

        Returns:
            List[Dict[str, Any]]: 关键词列表，每个关键词包含词语和权重
        """
        try:
            if JIEBA_AVAILABLE:
                # 使用jieba提取关键词
                keywords = jieba.analyse.extract_tags(text, topK=top_n, withWeight=True)
                return [{'word': word, 'weight': weight} for word, weight in keywords]
            else:
                # 简单的关键词提取方法
                words = {}
                for char in text:
                    if char in words:
                        words[char] += 1
                    else:
                        words[char] = 1

                # 排序并返回前N个
                sorted_words = sorted(words.items(), key=lambda x: x[1], reverse=True)
                return [{'word': word, 'weight': count/len(text)} for word, count in sorted_words[:top_n]]

        except Exception as e:
            logger.error(f"关键词提取失败: {str(e)}")
            return []

    def identify_related_industries(self, text: str, keywords: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        识别政策相关行业

        Args:
            text: 政策文本
            keywords: 提取的关键词

        Returns:
            List[Dict[str, Any]]: 相关行业列表，每个行业包含名称和相关度
        """
        related_industries = []

        try:
            # 提取关键词列表
            keyword_list = [item['word'] for item in keywords]

            # 计算每个行业的相关度
            for industry, industry_keywords in self.industry_keywords.items():
                # 计算行业关键词在文本中的出现次数
                keyword_count = 0
                matched_keywords = []

                for keyword in industry_keywords:
                    if keyword in text:
                        keyword_count += text.count(keyword)
                        matched_keywords.append(keyword)

                # 计算关键词匹配率
                if industry_keywords:
                    match_rate = len(matched_keywords) / len(industry_keywords)
                else:
                    match_rate = 0.0

                # 计算与提取关键词的重叠度
                overlap = [kw for kw in keyword_list if kw in industry_keywords]
                overlap_rate = len(overlap) / len(keyword_list) if keyword_list else 0.0

                # 综合计算相关度
                relevance = (keyword_count * 0.4 + match_rate * 0.3 + overlap_rate * 0.3)

                if relevance > 0:
                    related_industries.append({
                        'industry': industry,
                        'relevance': relevance,
                        'matched_keywords': matched_keywords,
                        'keyword_count': keyword_count,
                        'match_rate': match_rate,
                        'overlap_rate': overlap_rate
                    })

            # 按相关度排序
            related_industries = sorted(related_industries, key=lambda x: x['relevance'], reverse=True)

            return related_industries

        except Exception as e:
            logger.error(f"行业识别失败: {str(e)}")
            return []
