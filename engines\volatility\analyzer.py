"""
Volatility Analyzer module.
Responsible for calculating and analyzing stock and market volatility.
"""

import pandas as pd
import numpy as np
import akshare as ak
from datetime import datetime, timedelta
import math
from arch import arch_model

from utils.logger import logger
from utils.config_loader import config_loader
from utils.error_utils import retry, handle_api_error, safe_execute
from utils.cache_utils import cached
from utils.data_utils import get_trading_dates

class VolatilityAnalyzer:
    """
    Class for analyzing volatility.
    """
    
    def __init__(self, config=None):
        """
        Initialize the VolatilityAnalyzer.
        
        Args:
            config: Configuration object or None to use default.
        """
        self.config = config if config else config_loader
        
        # Load settings from configuration
        self.historical_window = self.config.get('engines.volatility.historical_window', 20)
        self.garch_p = self.config.get('engines.volatility.garch_params.p', 1)
        self.garch_q = self.config.get('engines.volatility.garch_params.q', 1)
        
        # Load volatility regime thresholds
        self.low_volatility_threshold = self.config.get('engines.volatility.regime_thresholds.low_volatility', 0.15)
        self.high_volatility_threshold = self.config.get('engines.volatility.regime_thresholds.high_volatility', 0.3)
        
        logger.info("VolatilityAnalyzer initialized")
    
    @cached(expiry=3600)  # Cache for 1 hour
    @retry(max_retries=3, delay=2)
    def get_historical_prices(self, stock_code, start_date, end_date):
        """
        Get historical prices for a stock.
        
        Args:
            stock_code (str): Stock code.
            start_date (str or datetime): Start date.
            end_date (str or datetime): End date.
            
        Returns:
            pandas.DataFrame: DataFrame containing historical prices.
        """
        try:
            # Convert dates to string format if they are datetime objects
            if isinstance(start_date, (datetime, pd.Timestamp)):
                start_date = start_date.strftime('%Y-%m-%d')
            if isinstance(end_date, (datetime, pd.Timestamp)):
                end_date = end_date.strftime('%Y-%m-%d')
            
            # Format stock code
            if stock_code.startswith('6'):
                symbol = f"sh{stock_code}"
            else:
                symbol = f"sz{stock_code}"
            
            # Get historical prices
            try:
                price_df = ak.stock_zh_a_hist(symbol=symbol, start_date=start_date, end_date=end_date)
                
                # Rename columns for consistency
                price_df.columns = ['date', 'open', 'close', 'high', 'low', 'volume', 'amount', 'amplitude', 'change_pct', 'change', 'turnover']
                
                # Convert date to datetime
                price_df['date'] = pd.to_datetime(price_df['date'])
                
                # Add stock code column
                price_df['stock_code'] = stock_code
                
                return price_df
            except Exception as e:
                logger.error(f"Error getting historical prices for {stock_code}: {str(e)}")
                
                # Generate synthetic data if real data is not available
                trading_dates = get_trading_dates(start_date, end_date)
                
                # Generate synthetic data
                data = []
                price = np.random.uniform(10, 100)  # Initial price
                
                for date in trading_dates:
                    # Generate random price changes
                    daily_return = np.random.normal(0, 0.02)  # Random return with mean 0 and std 2%
                    price = price * (1 + daily_return)
                    
                    # Generate other values
                    open_price = price * np.random.uniform(0.99, 1.01)
                    high_price = price * np.random.uniform(1.0, 1.03)
                    low_price = price * np.random.uniform(0.97, 1.0)
                    volume = np.random.uniform(1e6, 1e7)
                    amount = volume * price
                    
                    data.append({
                        'date': pd.to_datetime(date),
                        'open': open_price,
                        'close': price,
                        'high': high_price,
                        'low': low_price,
                        'volume': volume,
                        'amount': amount,
                        'amplitude': (high_price - low_price) / price * 100,
                        'change_pct': daily_return * 100,
                        'change': price * daily_return,
                        'turnover': np.random.uniform(1, 5),
                        'stock_code': stock_code
                    })
                
                return pd.DataFrame(data)
        except Exception as e:
            logger.error(f"Error getting historical prices for {stock_code}: {str(e)}")
            return pd.DataFrame(columns=['date', 'open', 'close', 'high', 'low', 'volume', 'amount', 'amplitude', 'change_pct', 'change', 'turnover', 'stock_code'])
    
    @safe_execute(default_return=0.0)
    def calculate_historical_volatility(self, prices_df, window=None, annualize=True):
        """
        Calculate historical volatility.
        
        Args:
            prices_df (pandas.DataFrame): DataFrame containing prices.
            window (int, optional): Window size. Defaults to self.historical_window.
            annualize (bool): Whether to annualize the volatility.
            
        Returns:
            float: Historical volatility.
        """
        if window is None:
            window = self.historical_window
        
        if prices_df.empty:
            return 0.0
        
        # Calculate log returns
        prices_df = prices_df.sort_values('date')
        prices_df['log_return'] = np.log(prices_df['close'] / prices_df['close'].shift(1))
        
        # Drop NaN values
        prices_df = prices_df.dropna(subset=['log_return'])
        
        if len(prices_df) < window:
            logger.warning(f"Not enough data to calculate historical volatility. Required: {window}, Available: {len(prices_df)}")
            return 0.0
        
        # Calculate volatility
        volatility = prices_df['log_return'].rolling(window=window).std().iloc[-1]
        
        # Annualize if requested
        if annualize:
            volatility = volatility * np.sqrt(252)  # Assuming 252 trading days in a year
        
        return volatility
    
    @safe_execute(default_return=0.0)
    def calculate_garch_volatility(self, prices_df, p=None, q=None):
        """
        Calculate GARCH volatility.
        
        Args:
            prices_df (pandas.DataFrame): DataFrame containing prices.
            p (int, optional): GARCH p parameter. Defaults to self.garch_p.
            q (int, optional): GARCH q parameter. Defaults to self.garch_q.
            
        Returns:
            float: GARCH volatility forecast.
        """
        if p is None:
            p = self.garch_p
        if q is None:
            q = self.garch_q
        
        if prices_df.empty:
            return 0.0
        
        # Calculate log returns
        prices_df = prices_df.sort_values('date')
        prices_df['log_return'] = np.log(prices_df['close'] / prices_df['close'].shift(1))
        
        # Drop NaN values
        prices_df = prices_df.dropna(subset=['log_return'])
        
        if len(prices_df) < 30:  # Need enough data for GARCH
            logger.warning(f"Not enough data to fit GARCH model. Required: 30, Available: {len(prices_df)}")
            return 0.0
        
        # Fit GARCH model
        returns = 100 * prices_df['log_return'].values  # Convert to percentage
        model = arch_model(returns, vol='Garch', p=p, q=q)
        
        try:
            model_fit = model.fit(disp='off')
            forecast = model_fit.forecast(horizon=1)
            volatility = np.sqrt(forecast.variance.iloc[-1, 0]) / 100  # Convert back from percentage
            
            # Annualize
            volatility = volatility * np.sqrt(252)  # Assuming 252 trading days in a year
            
            return volatility
        except Exception as e:
            logger.error(f"Error fitting GARCH model: {str(e)}")
            return 0.0
    
    @cached(expiry=3600)  # Cache for 1 hour
    @retry(max_retries=3, delay=2)
    def get_implied_volatility(self, stock_code, current_date=None):
        """
        Get implied volatility for a stock.
        
        Args:
            stock_code (str): Stock code.
            current_date (str or datetime, optional): Current date. Defaults to today.
            
        Returns:
            dict: Dictionary containing implied volatility.
        """
        try:
            # This is a placeholder, in a real system you would use actual option data
            # For now, return a random value
            return {
                'implied_vol': np.random.uniform(0.2, 0.4)
            }
        except Exception as e:
            logger.error(f"Error getting implied volatility for {stock_code}: {str(e)}")
            return {'implied_vol': 0.0}
    
    @cached(expiry=3600)  # Cache for 1 hour
    def get_stock_volatility_profile(self, stock_code, start_date, end_date):
        """
        Get volatility profile for a stock.
        
        Args:
            stock_code (str): Stock code.
            start_date (str or datetime): Start date.
            end_date (str or datetime): End date.
            
        Returns:
            dict: Dictionary containing volatility profile.
        """
        try:
            # Get historical prices
            prices_df = self.get_historical_prices(stock_code, start_date, end_date)
            
            if prices_df.empty:
                logger.warning(f"No price data available for {stock_code}")
                return {
                    'historical_vol_20d': 0.0,
                    'historical_vol_60d': 0.0,
                    'garch_forecast': 0.0,
                    'implied_vol': 0.0
                }
            
            # Calculate historical volatility (20-day)
            historical_vol_20d = self.calculate_historical_volatility(prices_df, window=20)
            
            # Calculate historical volatility (60-day)
            historical_vol_60d = self.calculate_historical_volatility(prices_df, window=60)
            
            # Calculate GARCH volatility
            garch_forecast = self.calculate_garch_volatility(prices_df)
            
            # Get implied volatility
            implied_vol_data = self.get_implied_volatility(stock_code)
            
            return {
                'historical_vol_20d': historical_vol_20d,
                'historical_vol_60d': historical_vol_60d,
                'garch_forecast': garch_forecast,
                'implied_vol': implied_vol_data.get('implied_vol', 0.0)
            }
        except Exception as e:
            logger.error(f"Error getting volatility profile for {stock_code}: {str(e)}")
            return {
                'historical_vol_20d': 0.0,
                'historical_vol_60d': 0.0,
                'garch_forecast': 0.0,
                'implied_vol': 0.0
            }
    
    @cached(expiry=3600)  # Cache for 1 hour
    def analyze_market_volatility_regime(self, market_index_code='sh000001', start_date=None, end_date=None):
        """
        Analyze market volatility regime.
        
        Args:
            market_index_code (str): Market index code.
            start_date (str or datetime, optional): Start date. Defaults to 60 days ago.
            end_date (str or datetime, optional): End date. Defaults to today.
            
        Returns:
            dict: Dictionary containing market volatility regime.
        """
        try:
            # Set default dates if not provided
            if end_date is None:
                end_date = datetime.now().strftime('%Y-%m-%d')
            if start_date is None:
                start_date = (datetime.now() - timedelta(days=60)).strftime('%Y-%m-%d')
            
            # Get historical prices for the market index
            prices_df = self.get_historical_prices(market_index_code, start_date, end_date)
            
            if prices_df.empty:
                logger.warning(f"No price data available for market index {market_index_code}")
                return {
                    'market_vol_regime': 'normal',
                    'market_volatility': 0.0,
                    'market_trend': 'neutral'
                }
            
            # Calculate historical volatility
            market_volatility = self.calculate_historical_volatility(prices_df)
            
            # Determine volatility regime
            if market_volatility < self.low_volatility_threshold:
                market_vol_regime = 'low_volatility'
            elif market_volatility > self.high_volatility_threshold:
                market_vol_regime = 'high_volatility'
            else:
                market_vol_regime = 'normal_volatility'
            
            # Determine market trend
            prices_df = prices_df.sort_values('date')
            recent_returns = prices_df['change_pct'].iloc[-10:].mean()
            
            if recent_returns > 1.0:  # 1% average daily return
                market_trend = 'trending_up'
            elif recent_returns < -1.0:  # -1% average daily return
                market_trend = 'trending_down'
            else:
                market_trend = 'neutral'
            
            return {
                'market_vol_regime': market_vol_regime,
                'market_volatility': market_volatility,
                'market_trend': market_trend
            }
        except Exception as e:
            logger.error(f"Error analyzing market volatility regime: {str(e)}")
            return {
                'market_vol_regime': 'normal',
                'market_volatility': 0.0,
                'market_trend': 'neutral'
            }
