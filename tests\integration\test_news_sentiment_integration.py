"""
新闻-情绪共振集成测试

测试新闻监控模块和情绪共振模型的集成功能
"""

import os
import sys
import unittest
import json
import logging
from datetime import datetime, timedelta

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('test_news_sentiment_integration')

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

# 导入模块
from data_sources.unified_news_monitor import UnifiedNewsMonitor
from data_sources.sentiment_resonance import SentimentResonance
from core.data_storage import DataStorage, StorageLevel

class TestNewsSentimentIntegration(unittest.TestCase):
    """新闻-情绪共振集成测试类"""

    @classmethod
    def setUpClass(cls):
        """测试前的准备工作"""
        logger.info("初始化新闻-情绪共振集成测试...")

        # 创建数据存储
        cls.data_storage = DataStorage()

        # 创建新闻监控模块
        cls.news_monitor = UnifiedNewsMonitor()

        # 创建情绪共振模型
        cls.sentiment_resonance = SentimentResonance()

        # 初始化模块
        cls.news_monitor.initialize()
        cls.sentiment_resonance.initialize()

        # 清理测试数据
        cls.clean_test_data()

        logger.info("新闻-情绪共振集成测试初始化完成")

    @classmethod
    def tearDownClass(cls):
        """测试后的清理工作"""
        logger.info("清理新闻-情绪共振集成测试...")

        # 清理测试数据
        cls.clean_test_data()

        logger.info("新闻-情绪共振集成测试清理完成")

    @classmethod
    def clean_test_data(cls):
        """清理测试数据"""
        try:
            # 使用save方法覆盖数据，而不是使用clean方法
            # 清理新闻数据
            cls.data_storage.save('unified_news_monitor', 'processed_news', [], StorageLevel.WARM)

            # 清理情绪共振数据
            cls.data_storage.save('sentiment_resonance', 'resonance_data', {
                'daily_sentiment': [],
                'policy_sentiment': [],
                'market_sentiment': [],
                'resonance_points': [],
                'turning_points': []
            }, StorageLevel.WARM)

            logger.info("测试数据清理完成")
        except Exception as e:
            logger.error(f"清理测试数据失败: {str(e)}")

    def test_news_fetch_and_sentiment_analysis(self):
        """测试新闻获取和情绪分析的集成 (NS-001)"""
        try:
            # 创建模拟新闻数据
            mock_news = [
                {
                    'id': '1',
                    'title': '央行降准0.5个百分点，释放流动性1万亿',
                    'content': '中国人民银行决定下调金融机构存款准备金率0.5个百分点，释放长期资金约1万亿元。',
                    'source': '央行',
                    'publish_date': datetime.now().isoformat(),
                    'url': 'http://example.com/news/1',
                    'keywords': ['央行', '降准', '流动性'],
                    'score': 1.5,
                    'processed_at': datetime.now().isoformat()
                },
                {
                    'id': '2',
                    'title': '国务院：加大对实体经济支持力度',
                    'content': '国务院常务会议指出，要加大对实体经济的支持力度，降低企业融资成本，促进经济稳定增长。',
                    'source': '国务院',
                    'publish_date': datetime.now().isoformat(),
                    'url': 'http://example.com/news/2',
                    'keywords': ['国务院', '实体经济', '融资成本'],
                    'score': 1.3,
                    'processed_at': datetime.now().isoformat()
                },
                {
                    'id': '3',
                    'title': '证监会：严厉打击市场操纵行为',
                    'content': '证监会表示将严厉打击市场操纵、内幕交易等违法行为，维护市场公平公正。',
                    'source': '证监会',
                    'publish_date': datetime.now().isoformat(),
                    'url': 'http://example.com/news/3',
                    'keywords': ['证监会', '市场操纵', '内幕交易'],
                    'score': 1.2,
                    'processed_at': datetime.now().isoformat()
                }
            ]

            # 直接保存已处理的新闻数据
            self.data_storage.save('unified_news_monitor', 'processed_news', mock_news, StorageLevel.HOT)

            # 验证新闻数据
            processed_news = self.data_storage.load('unified_news_monitor', 'processed_news', [])
            self.assertTrue(len(processed_news) > 0, "处理后的新闻数据应该存在")

            # 分析情绪共振
            sentiment_result = self.sentiment_resonance.analyze_sentiment_resonance()

            # 验证情绪分析结果
            self.assertIn('status', sentiment_result)

            # 情绪共振数据应该已经存储在数据库中
            resonance_data = self.data_storage.load('sentiment_resonance', 'resonance_data', {})
            self.assertTrue('daily_sentiment' in resonance_data, "情绪共振数据应该存在")

            logger.info(f"新闻获取和情绪分析集成测试通过，处理了{len(processed_news)}条新闻")

        except Exception as e:
            logger.error(f"新闻获取和情绪分析集成测试失败: {str(e)}")
            raise

    def test_hot_topics_and_sentiment_resonance(self):
        """测试热点话题和情绪共振的集成 (NS-002)"""
        try:
            # 创建模拟新闻数据，包含行业关键词
            mock_news = [
                {
                    'id': '1',
                    'title': '芯片行业迎来新机遇，国产替代加速',
                    'content': '随着国产替代进程加速，中芯国际、华为等芯片企业迎来发展新机遇。',
                    'source': '证券时报',
                    'publish_date': datetime.now().isoformat(),
                    'url': 'http://example.com/news/1',
                    'score': 1.2,
                    'keywords': ['芯片', '国产替代', '中芯国际', '华为'],
                    'processed_at': datetime.now().isoformat()
                },
                {
                    'id': '2',
                    'title': '新能源汽车销量持续增长，比亚迪市占率提升',
                    'content': '4月新能源汽车销量同比增长30%，比亚迪、特斯拉等头部企业市占率进一步提升。',
                    'source': '中国证券报',
                    'publish_date': datetime.now().isoformat(),
                    'url': 'http://example.com/news/2',
                    'score': 1.3,
                    'keywords': ['新能源汽车', '比亚迪', '特斯拉', '销量'],
                    'processed_at': datetime.now().isoformat()
                },
                {
                    'id': '3',
                    'title': '人工智能应用场景扩展，科大讯飞语音技术领先',
                    'content': '随着AIGC技术发展，人工智能应用场景不断扩展，科大讯飞在语音识别领域保持领先地位。',
                    'source': '上海证券报',
                    'publish_date': datetime.now().isoformat(),
                    'url': 'http://example.com/news/3',
                    'score': 1.4,
                    'keywords': ['人工智能', 'AIGC', '科大讯飞', '语音识别'],
                    'processed_at': datetime.now().isoformat()
                }
            ]

            # 保存模拟新闻数据
            self.data_storage.save('unified_news_monitor', 'processed_news', mock_news, StorageLevel.WARM)

            # 分析热点话题
            hot_topics_result = self.news_monitor.analyze_hot_topics()

            # 验证热点话题分析结果
            self.assertIn('status', hot_topics_result)
            self.assertIn('hot_sectors', hot_topics_result)
            self.assertIn('hot_stocks', hot_topics_result)

            # 获取情绪共振报告
            resonance_report_result = self.sentiment_resonance.get_resonance_report(days=7)

            # 验证情绪共振报告结果
            self.assertIn('status', resonance_report_result)

            logger.info(f"热点话题和情绪共振集成测试通过，分析了{len(hot_topics_result.get('hot_sectors', {}))}个热点板块")

        except Exception as e:
            logger.error(f"热点话题和情绪共振集成测试失败: {str(e)}")
            raise

    def test_daily_report_generation(self):
        """测试每日报告生成 (NS-003)"""
        try:
            # 确保有足够的模拟数据用于生成报告
            # 使用test_hot_topics_and_sentiment_resonance中的模拟数据

            # 生成每日报告
            report_result = self.news_monitor.generate_daily_report()

            # 验证报告生成结果
            self.assertIn('status', report_result)
            self.assertIn('report_file', report_result)

            # 验证报告内容
            if report_result.get('status') == 'success':
                report_content = report_result.get('report_content', '')
                self.assertTrue(len(report_content) > 0, "报告内容不应为空")

                # 验证报告包含热点板块和个股
                self.assertIn('热点板块', report_content)
                self.assertIn('热点个股', report_content)

                # 验证报告包含重要新闻
                self.assertIn('今日重要新闻', report_content)

                # 验证报告包含政策动态
                self.assertIn('政策动态', report_content)

                # 验证报告包含板块关注建议
                self.assertIn('板块关注建议', report_content)

            logger.info(f"每日报告生成测试通过，生成了报告: {report_result.get('report_file')}")

        except Exception as e:
            logger.error(f"每日报告生成测试失败: {str(e)}")
            raise

    def test_sentiment_trend_analysis(self):
        """测试情绪趋势分析 (NS-004)"""
        try:
            # 模拟多天的情绪数据
            # 首先获取当前的情绪共振数据
            resonance_data = self.data_storage.load('sentiment_resonance', 'resonance_data', {
                'daily_sentiment': [],
                'policy_sentiment': [],
                'market_sentiment': [],
                'resonance_points': [],
                'turning_points': []
            })

            # 添加模拟的每日情绪数据
            daily_sentiment = resonance_data.get('daily_sentiment', [])

            # 添加7天的模拟数据，形成一个上升趋势
            for i in range(7):
                day = datetime.now() - timedelta(days=6-i)
                # 创建一个上升趋势
                score = -0.3 + i * 0.1  # 从-0.3到0.3的上升趋势

                daily_sentiment.append({
                    'date': day.strftime('%Y-%m-%d'),
                    'news_sentiment_score': score,
                    'policy_sentiment_score': score,
                    'combined_sentiment_score': score,
                    'sentiment_label': self.sentiment_resonance._get_sentiment_label(score),
                    'news_count': 10,
                    'policy_count': 5,
                    'resonance_points_count': 2,
                    'turning_points_count': 1,
                    'timestamp': day.isoformat()
                })

            # 更新情绪共振数据
            resonance_data['daily_sentiment'] = daily_sentiment
            self.data_storage.save('sentiment_resonance', 'resonance_data', resonance_data, StorageLevel.WARM)

            # 获取情绪共振报告
            resonance_report_result = self.sentiment_resonance.get_resonance_report(days=7)

            # 验证情绪共振报告结果
            self.assertIn('status', resonance_report_result)

            # 验证情绪趋势
            if 'report' in resonance_report_result:
                report = resonance_report_result['report']
                if 'sentiment_trend' in report:
                    sentiment_trend = report['sentiment_trend']

                    # 验证趋势方向是上升
                    self.assertEqual(sentiment_trend.get('direction'), 'up', "情绪趋势方向应该是上升")

                    # 验证趋势强度
                    self.assertTrue(sentiment_trend.get('strength') > 0, "情绪趋势强度应该大于0")

                    # 验证趋势描述
                    self.assertIn('情绪上升', sentiment_trend.get('description', ''), "情绪趋势描述应该包含'情绪上升'")

                    logger.info(f"情绪趋势分析测试通过，趋势方向: {sentiment_trend.get('direction')}")
                else:
                    logger.warning("情绪趋势分析测试：报告中没有情绪趋势数据")
            else:
                logger.warning("情绪趋势分析测试：没有获取到报告数据")

        except Exception as e:
            logger.error(f"情绪趋势分析测试失败: {str(e)}")
            raise

if __name__ == '__main__':
    unittest.main()
