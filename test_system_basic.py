"""
基础系统测试脚本
在模型下载完成前，先测试基础功能
"""

import os
import sys
import pandas as pd
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_basic_imports():
    """测试基础导入"""
    print("🔍 测试基础导入...")

    try:
        # 测试核心模块导入
        from core.data_storage import DataStorage
        print("✅ DataStorage 导入成功")

        from core.unified_data_collector import UnifiedDataCollector
        print("✅ UnifiedDataCollector 导入成功")

        from utils.logger import logger
        print("✅ Logger 导入成功")

        # 测试AKShare
        import akshare as ak
        print(f"✅ AKShare 导入成功，版本: {ak.__version__}")

        return True

    except Exception as e:
        print(f"❌ 导入失败: {str(e)}")
        return False

def test_data_storage():
    """测试数据存储"""
    print("\n🔍 测试数据存储...")

    try:
        from core.data_storage import DataStorage, StorageLevel

        storage = DataStorage()

        # 测试存储
        test_data = {
            'test_key': 'test_value',
            'timestamp': datetime.now().isoformat()
        }

        success = storage.store_data('test_basic', test_data, StorageLevel.HOT)
        if success:
            print("✅ 数据存储成功")
        else:
            print("❌ 数据存储失败")
            return False

        # 测试读取
        retrieved_data = storage.get_latest_data('test_basic', limit=1)
        if retrieved_data:
            print("✅ 数据读取成功")
        else:
            print("❌ 数据读取失败")
            return False

        # 测试状态
        status = storage.get_storage_status()
        if status:
            print("✅ 存储状态获取成功")
        else:
            print("❌ 存储状态获取失败")
            return False

        return True

    except Exception as e:
        print(f"❌ 数据存储测试失败: {str(e)}")
        return False

def test_data_collection():
    """测试数据收集"""
    print("\n🔍 测试数据收集...")

    try:
        from core.unified_data_collector import UnifiedDataCollector
        import asyncio

        collector = UnifiedDataCollector()

        # 测试新闻数据收集（同步版本）
        print("  测试新闻数据收集...")
        try:
            # 尝试同步调用
            news_data = collector.collect_news_data_sync() if hasattr(collector, 'collect_news_data_sync') else None
            if not news_data:
                # 如果没有同步版本，跳过异步测试
                print("⚠️ 新闻数据收集跳过（异步方法需要事件循环）")
                news_success = True  # 不算作失败
            else:
                if len(news_data) > 0:
                    print(f"✅ 新闻数据收集成功: {len(news_data)}条")
                    news_success = True
                else:
                    print("⚠️ 新闻数据收集为空")
                    news_success = True
        except Exception as e:
            print(f"⚠️ 新闻数据收集跳过: {str(e)}")
            news_success = True  # 不算作失败

        # 测试资金流数据收集
        print("  测试资金流数据收集...")
        try:
            fund_flow_data = collector.collect_fund_flow_data_sync() if hasattr(collector, 'collect_fund_flow_data_sync') else None
            if fund_flow_data:
                print("✅ 资金流数据收集成功")
                fund_flow_success = True
            else:
                print("⚠️ 资金流数据收集跳过（异步方法）")
                fund_flow_success = True
        except Exception as e:
            print(f"⚠️ 资金流数据收集跳过: {str(e)}")
            fund_flow_success = True

        # 测试市场数据收集
        print("  测试市场数据收集...")
        try:
            market_data = collector.collect_market_data_sync() if hasattr(collector, 'collect_market_data_sync') else None
            if market_data:
                print("✅ 市场数据收集成功")
                market_success = True
            else:
                print("⚠️ 市场数据收集跳过（异步方法）")
                market_success = True
        except Exception as e:
            print(f"⚠️ 市场数据收集跳过: {str(e)}")
            market_success = True

        return news_success and fund_flow_success and market_success

    except Exception as e:
        print(f"❌ 数据收集测试失败: {str(e)}")
        return False

def test_akshare_apis():
    """测试AKShare API"""
    print("\n🔍 测试AKShare API...")

    try:
        import akshare as ak

        # 测试股票实时数据
        print("  测试股票实时数据...")
        stock_data = ak.stock_zh_a_spot_em()
        if stock_data is not None and not stock_data.empty:
            print(f"✅ 股票数据获取成功: {len(stock_data)}条")
        else:
            print("❌ 股票数据获取失败")
            return False

        # 测试新闻数据
        print("  测试新闻数据...")
        news_data = ak.stock_news_em()
        if news_data is not None and not news_data.empty:
            print(f"✅ 新闻数据获取成功: {len(news_data)}条")
        else:
            print("❌ 新闻数据获取失败")
            return False

        # 测试资金流数据
        print("  测试资金流数据...")
        fund_data = ak.stock_fund_flow_individual(symbol="即时")
        if fund_data is not None and not fund_data.empty:
            print(f"✅ 资金流数据获取成功: {len(fund_data)}条")
        else:
            print("❌ 资金流数据获取失败")
            return False

        return True

    except Exception as e:
        print(f"❌ AKShare API测试失败: {str(e)}")
        return False

def test_main_system():
    """测试主系统"""
    print("\n🔍 测试主系统...")

    try:
        # 导入主系统
        import main

        print("✅ 主系统模块导入成功")

        # 这里可以添加更多主系统测试
        return True

    except Exception as e:
        print(f"❌ 主系统测试失败: {str(e)}")
        return False

def test_web_ui():
    """测试Web界面"""
    print("\n🔍 测试Web界面...")

    try:
        # 测试Flask应用导入
        from web_ui.intelligent_app import IntelligentDashboardApp

        print("✅ 智能驾驶舱应用导入成功")

        # 创建应用实例
        app = IntelligentDashboardApp()
        print("✅ 应用实例创建成功")

        return True

    except Exception as e:
        print(f"❌ Web界面测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始基础系统测试")
    print("=" * 60)

    test_results = []

    # 执行各项测试
    tests = [
        ("基础导入", test_basic_imports),
        ("数据存储", test_data_storage),
        ("数据收集", test_data_collection),
        ("AKShare API", test_akshare_apis),
        ("主系统", test_main_system),
        ("Web界面", test_web_ui)
    ]

    for test_name, test_func in tests:
        try:
            result = test_func()
            test_results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试异常: {str(e)}")
            test_results.append((test_name, False))

    # 生成测试报告
    print("\n" + "=" * 60)
    print("📊 基础系统测试报告")
    print("=" * 60)

    passed = 0
    total = len(test_results)

    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1

    print("-" * 60)
    print(f"总测试数: {total}")
    print(f"通过: {passed}")
    print(f"失败: {total - passed}")
    print(f"成功率: {passed/total*100:.1f}%")

    if passed == total:
        print("\n🎉 所有基础测试通过！系统基础功能正常")
    elif passed >= total * 0.8:
        print("\n⚠️ 大部分测试通过，系统基本可用")
    else:
        print("\n❌ 多项测试失败，需要检查系统配置")

    print("=" * 60)

    return passed == total

if __name__ == "__main__":
    main()
