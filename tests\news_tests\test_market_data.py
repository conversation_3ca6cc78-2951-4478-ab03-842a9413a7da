"""
测试市场数据源
"""

import os
import sys
import pandas as pd
from datetime import datetime, timedelta
import time

# 创建必要的目录
os.makedirs('logs', exist_ok=True)
os.makedirs('data/cache/market', exist_ok=True)

# 导入市场数据源
from data_sources.market_data import MarketDataSource

def test_stock_list():
    """测试获取股票列表"""
    print("\n测试获取股票列表...")
    
    market_data = MarketDataSource()
    
    # 测试获取股票列表
    start_time = time.time()
    stock_list = market_data.get_stock_list(use_cache=False)
    end_time = time.time()
    
    print(f"获取股票列表成功，耗时: {end_time - start_time:.2f}秒")
    print(f"股票数量: {len(stock_list)}")
    print("前5只股票:")
    print(stock_list.head())
    
    # 测试缓存
    start_time = time.time()
    stock_list_cached = market_data.get_stock_list(use_cache=True)
    end_time = time.time()
    
    print(f"从缓存获取股票列表，耗时: {end_time - start_time:.2f}秒")
    print(f"股票数量: {len(stock_list_cached)}")
    
    return stock_list

def test_stock_industry():
    """测试获取股票行业分类"""
    print("\n测试获取股票行业分类...")
    
    market_data = MarketDataSource()
    
    # 测试获取申万行业分类
    start_time = time.time()
    industry_sw = market_data.get_stock_industry(industry_type='sw', use_cache=False)
    end_time = time.time()
    
    print(f"获取申万行业分类成功，耗时: {end_time - start_time:.2f}秒")
    print(f"记录数量: {len(industry_sw)}")
    print("前5条记录:")
    print(industry_sw.head())
    
    # 测试缓存
    start_time = time.time()
    industry_sw_cached = market_data.get_stock_industry(industry_type='sw', use_cache=True)
    end_time = time.time()
    
    print(f"从缓存获取申万行业分类，耗时: {end_time - start_time:.2f}秒")
    print(f"记录数量: {len(industry_sw_cached)}")
    
    return industry_sw

def test_stock_history():
    """测试获取股票历史行情"""
    print("\n测试获取股票历史行情...")
    
    market_data = MarketDataSource()
    
    # 选择测试股票
    stock_code = '600000'  # 浦发银行
    
    # 设置日期范围
    end_date = datetime.now().strftime('%Y-%m-%d')
    start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
    
    # 测试获取股票历史行情
    start_time = time.time()
    history = market_data.get_stock_history(stock_code, start_date, end_date, use_cache=False)
    end_time = time.time()
    
    print(f"获取股票{stock_code}历史行情成功，耗时: {end_time - start_time:.2f}秒")
    print(f"记录数量: {len(history)}")
    print("前5条记录:")
    print(history.head())
    
    # 测试缓存
    start_time = time.time()
    history_cached = market_data.get_stock_history(stock_code, start_date, end_date, use_cache=True)
    end_time = time.time()
    
    print(f"从缓存获取股票{stock_code}历史行情，耗时: {end_time - start_time:.2f}秒")
    print(f"记录数量: {len(history_cached)}")
    
    return history

def test_index_history():
    """测试获取指数历史行情"""
    print("\n测试获取指数历史行情...")
    
    market_data = MarketDataSource()
    
    # 选择测试指数
    index_code = '000001'  # 上证指数
    
    # 设置日期范围
    end_date = datetime.now().strftime('%Y-%m-%d')
    start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
    
    # 测试获取指数历史行情
    start_time = time.time()
    history = market_data.get_index_history(index_code, start_date, end_date, use_cache=False)
    end_time = time.time()
    
    print(f"获取指数{index_code}历史行情成功，耗时: {end_time - start_time:.2f}秒")
    print(f"记录数量: {len(history)}")
    print("前5条记录:")
    print(history.head())
    
    # 测试缓存
    start_time = time.time()
    history_cached = market_data.get_index_history(index_code, start_date, end_date, use_cache=True)
    end_time = time.time()
    
    print(f"从缓存获取指数{index_code}历史行情，耗时: {end_time - start_time:.2f}秒")
    print(f"记录数量: {len(history_cached)}")
    
    return history

if __name__ == "__main__":
    print("开始测试市场数据源...")
    
    # 测试获取股票列表
    stock_list = test_stock_list()
    
    # 测试获取股票行业分类
    industry = test_stock_industry()
    
    # 测试获取股票历史行情
    stock_history = test_stock_history()
    
    # 测试获取指数历史行情
    index_history = test_index_history()
    
    print("\n所有测试完成!")
