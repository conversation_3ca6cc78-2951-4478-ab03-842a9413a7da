"""
市场数据源模块
提供A股市场基础数据
"""

import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import time
import akshare as ak
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/market_data.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('market_data')

class MarketDataSource:
    """市场数据源类"""

    def __init__(self, cache_dir='data/cache/market'):
        """
        初始化市场数据源

        Args:
            cache_dir (str): 缓存目录
        """
        self.cache_dir = cache_dir
        os.makedirs(cache_dir, exist_ok=True)
        logger.info(f"MarketDataSource initialized with cache_dir: {cache_dir}")

    def get_stock_list(self, use_cache=True, cache_days=1):
        """
        获取A股股票列表

        Args:
            use_cache (bool): 是否使用缓存
            cache_days (int): 缓存有效期（天）

        Returns:
            pandas.DataFrame: A股股票列表，包含股票代码和名称
        """
        cache_file = os.path.join(self.cache_dir, 'stock_list.csv')

        # 检查缓存
        if use_cache and os.path.exists(cache_file):
            # 检查缓存是否过期
            file_time = datetime.fromtimestamp(os.path.getmtime(cache_file))
            if datetime.now() - file_time < timedelta(days=cache_days):
                logger.info(f"Loading stock list from cache: {cache_file}")
                return pd.read_csv(cache_file)

        # 获取数据
        try:
            logger.info("Fetching stock list from akshare")
            start_time = time.time()
            stock_list = ak.stock_info_a_code_name()
            end_time = time.time()

            logger.info(f"Stock list fetched successfully in {end_time - start_time:.2f}s. Shape: {stock_list.shape}")

            # 保存缓存
            stock_list.to_csv(cache_file, index=False)

            return stock_list
        except Exception as e:
            logger.error(f"Error fetching stock list: {str(e)}")

            # 如果出错且缓存存在，则使用缓存
            if os.path.exists(cache_file):
                logger.info(f"Using cached stock list due to error: {cache_file}")
                return pd.read_csv(cache_file)

            # 如果没有缓存，则抛出异常
            raise

    def get_stock_industry(self, industry_type='sw', use_cache=True, cache_days=7):
        """
        获取股票行业分类

        Args:
            industry_type (str): 行业分类类型，'sw'申万行业分类，'zjw'证监会行业分类
            use_cache (bool): 是否使用缓存
            cache_days (int): 缓存有效期（天）

        Returns:
            pandas.DataFrame: 股票行业分类
        """
        cache_file = os.path.join(self.cache_dir, f'stock_industry_{industry_type}.csv')

        # 检查缓存
        if use_cache and os.path.exists(cache_file):
            # 检查缓存是否过期
            file_time = datetime.fromtimestamp(os.path.getmtime(cache_file))
            if datetime.now() - file_time < timedelta(days=cache_days):
                logger.info(f"Loading stock industry from cache: {cache_file}")
                return pd.read_csv(cache_file)

        # 获取数据
        try:
            logger.info(f"Fetching stock industry ({industry_type}) from akshare")
            start_time = time.time()

            if industry_type == 'sw':
                # 申万行业分类
                # 首先获取板块列表
                sector_df = ak.stock_sector_spot()

                # 创建一个空的结果DataFrame
                result_df = pd.DataFrame(columns=['stock_code', 'stock_name', 'industry_name', 'industry_code'])

                # 遍历每个板块，获取板块内的股票
                for idx, row in sector_df.iterrows():
                    sector_code = row['label']
                    sector_name = row['板块']

                    # 获取板块内的股票
                    try:
                        stocks_df = ak.stock_sector_detail(sector=sector_code)

                        # 提取股票代码和名称
                        if not stocks_df.empty:
                            temp_df = pd.DataFrame({
                                'stock_code': stocks_df['code'],
                                'stock_name': stocks_df['name'],
                                'industry_name': sector_name,
                                'industry_code': sector_code
                            })

                            # 添加到结果DataFrame
                            result_df = pd.concat([result_df, temp_df], ignore_index=True)
                    except Exception as e:
                        logger.warning(f"Error fetching stocks for sector {sector_code}: {str(e)}")
                        continue

                # 去重
                result_df = result_df.drop_duplicates(subset=['stock_code'])

            elif industry_type == 'zjw':
                # 证监会行业分类
                # 这里需要使用其他API获取证监会行业分类
                # 暂时返回空DataFrame
                result_df = pd.DataFrame(columns=['stock_code', 'stock_name', 'industry_name', 'industry_code'])
                logger.warning("证监会行业分类暂未实现")
            else:
                raise ValueError(f"Unsupported industry_type: {industry_type}")

            end_time = time.time()

            logger.info(f"Stock industry fetched successfully in {end_time - start_time:.2f}s. Shape: {result_df.shape}")

            # 保存缓存
            result_df.to_csv(cache_file, index=False)

            return result_df
        except Exception as e:
            logger.error(f"Error fetching stock industry: {str(e)}")

            # 如果出错且缓存存在，则使用缓存
            if os.path.exists(cache_file):
                logger.info(f"Using cached stock industry due to error: {cache_file}")
                return pd.read_csv(cache_file)

            # 如果没有缓存，则抛出异常
            raise

    def get_stock_history(self, stock_code, start_date, end_date=None, adjust='qfq', use_cache=True):
        """
        获取股票历史行情数据

        Args:
            stock_code (str): 股票代码，如'600000'
            start_date (str): 开始日期，格式'YYYY-MM-DD'
            end_date (str, optional): 结束日期，格式'YYYY-MM-DD'，默认为今天
            adjust (str): 复权方式，'qfq'前复权，'hfq'后复权，''不复权
            use_cache (bool): 是否使用缓存

        Returns:
            pandas.DataFrame: 股票历史行情数据
        """
        # 设置默认结束日期
        if end_date is None:
            end_date = datetime.now().strftime('%Y-%m-%d')

        # 生成缓存文件名
        cache_key = f"{stock_code}_{start_date}_{end_date}_{adjust}"
        cache_file = os.path.join(self.cache_dir, f'stock_history_{cache_key}.csv')

        # 检查缓存
        if use_cache and os.path.exists(cache_file):
            # 对于历史数据，如果请求的结束日期是今天，则缓存只有当天有效
            # 否则缓存永久有效（因为历史数据不会变）
            file_time = datetime.fromtimestamp(os.path.getmtime(cache_file))

            if end_date != datetime.now().strftime('%Y-%m-%d') or datetime.now().date() == file_time.date():
                logger.info(f"Loading stock history from cache: {cache_file}")
                return pd.read_csv(cache_file)

        # 获取数据
        try:
            logger.info(f"Fetching stock history for {stock_code} from {start_date} to {end_date}")
            start_time = time.time()

            # 格式化股票代码
            if stock_code.startswith('6'):
                symbol = f"sh{stock_code}"
            else:
                symbol = f"sz{stock_code}"

            # 获取历史数据
            # 注意：akshare的日期格式是YYYYMMDD，需要转换
            start_date_fmt = start_date.replace('-', '')
            end_date_fmt = end_date.replace('-', '')

            history_df = ak.stock_zh_a_hist(symbol=symbol, period="daily", start_date=start_date_fmt, end_date=end_date_fmt, adjust=adjust)

            # 检查是否获取到数据
            if history_df.empty:
                logger.warning(f"No history data for {stock_code} from {start_date} to {end_date}")

                # 如果缓存存在，则使用缓存
                if os.path.exists(cache_file):
                    logger.info(f"Using cached stock history due to empty result: {cache_file}")
                    return pd.read_csv(cache_file)

                # 如果没有缓存，则返回空DataFrame
                return pd.DataFrame(columns=['date', 'open', 'close', 'high', 'low', 'volume', 'amount', 'amplitude', 'change_pct', 'change', 'turnover', 'stock_code'])

            # 重命名列
            # 根据测试，akshare返回的列名可能是：['日期', '开盘', '收盘', '最高', '最低', '成交量', '成交额', '振幅', '涨跌幅', '涨跌额', '换手率']
            column_mapping = {
                '日期': 'date',
                '开盘': 'open',
                '收盘': 'close',
                '最高': 'high',
                '最低': 'low',
                '成交量': 'volume',
                '成交额': 'amount',
                '振幅': 'amplitude',
                '涨跌幅': 'change_pct',
                '涨跌额': 'change',
                '换手率': 'turnover'
            }

            history_df = history_df.rename(columns=column_mapping)

            # 添加股票代码列
            history_df['stock_code'] = stock_code

            end_time = time.time()

            logger.info(f"Stock history fetched successfully in {end_time - start_time:.2f}s. Shape: {history_df.shape}")

            # 保存缓存
            history_df.to_csv(cache_file, index=False)

            return history_df
        except Exception as e:
            logger.error(f"Error fetching stock history for {stock_code}: {str(e)}")

            # 如果出错且缓存存在，则使用缓存
            if os.path.exists(cache_file):
                logger.info(f"Using cached stock history due to error: {cache_file}")
                return pd.read_csv(cache_file)

            # 如果没有缓存，则抛出异常
            raise

    def get_index_history(self, index_code, start_date, end_date=None, use_cache=True):
        """
        获取指数历史行情数据

        Args:
            index_code (str): 指数代码，如'000001'上证指数，'399001'深证成指
            start_date (str): 开始日期，格式'YYYY-MM-DD'
            end_date (str, optional): 结束日期，格式'YYYY-MM-DD'，默认为今天
            use_cache (bool): 是否使用缓存

        Returns:
            pandas.DataFrame: 指数历史行情数据
        """
        # 设置默认结束日期
        if end_date is None:
            end_date = datetime.now().strftime('%Y-%m-%d')

        # 生成缓存文件名
        cache_key = f"{index_code}_{start_date}_{end_date}"
        cache_file = os.path.join(self.cache_dir, f'index_history_{cache_key}.csv')

        # 检查缓存
        if use_cache and os.path.exists(cache_file):
            # 对于历史数据，如果请求的结束日期是今天，则缓存只有当天有效
            # 否则缓存永久有效（因为历史数据不会变）
            file_time = datetime.fromtimestamp(os.path.getmtime(cache_file))

            if end_date != datetime.now().strftime('%Y-%m-%d') or datetime.now().date() == file_time.date():
                logger.info(f"Loading index history from cache: {cache_file}")
                return pd.read_csv(cache_file)

        # 获取数据
        try:
            logger.info(f"Fetching index history for {index_code} from {start_date} to {end_date}")
            start_time = time.time()

            # 格式化指数代码
            if index_code.startswith('0'):
                symbol = f"sh{index_code}"
            else:
                symbol = f"sz{index_code}"

            # 获取历史数据
            history_df = ak.stock_zh_index_daily(symbol=symbol)

            # 检查是否获取到数据
            if history_df.empty:
                logger.warning(f"No history data for index {index_code}")

                # 如果缓存存在，则使用缓存
                if os.path.exists(cache_file):
                    logger.info(f"Using cached index history due to empty result: {cache_file}")
                    return pd.read_csv(cache_file)

                # 如果没有缓存，则返回空DataFrame
                return pd.DataFrame(columns=['date', 'open', 'high', 'low', 'close', 'volume', 'index_code'])

            # 筛选日期范围
            history_df['date'] = pd.to_datetime(history_df['date'])
            start_date_dt = pd.to_datetime(start_date)
            end_date_dt = pd.to_datetime(end_date)
            history_df = history_df[(history_df['date'] >= start_date_dt) & (history_df['date'] <= end_date_dt)]

            # 将日期转回字符串格式
            history_df['date'] = history_df['date'].dt.strftime('%Y-%m-%d')

            # 添加指数代码列
            history_df['index_code'] = index_code

            end_time = time.time()

            logger.info(f"Index history fetched successfully in {end_time - start_time:.2f}s. Shape: {history_df.shape}")

            # 保存缓存
            history_df.to_csv(cache_file, index=False)

            return history_df
        except Exception as e:
            logger.error(f"Error fetching index history for {index_code}: {str(e)}")

            # 如果出错且缓存存在，则使用缓存
            if os.path.exists(cache_file):
                logger.info(f"Using cached index history due to error: {cache_file}")
                return pd.read_csv(cache_file)

            # 如果没有缓存，则抛出异常
            raise
