{"data_collection_20250524_170133": {"task_id": "data_collection_20250524_170133", "task_type": "data_collection", "module": "monitor_system", "function": "run_data_collection", "params": {}, "priority": "MEDIUM", "schedule_time": "2025-05-24T17:01:33.479584", "timeout": 300, "status": "COMPLETED", "created_at": "2025-05-24T17:01:33.479584", "started_at": "2025-05-24T17:01:33.480031", "completed_at": "2025-05-24T17:01:38.125409", "result": null, "error": null, "retry_count": 0, "max_retries": 3}, "anomaly_detection_20250524_170133": {"task_id": "anomaly_detection_20250524_170133", "task_type": "anomaly_detection", "module": "monitor_system", "function": "run_anomaly_detection", "params": {}, "priority": "HIGH", "schedule_time": "2025-05-24T17:01:33.480031", "timeout": 300, "status": "COMPLETED", "created_at": "2025-05-24T17:01:33.480031", "started_at": "2025-05-24T17:01:33.480031", "completed_at": "2025-05-24T17:01:33.489865", "result": null, "error": null, "retry_count": 0, "max_retries": 3}}