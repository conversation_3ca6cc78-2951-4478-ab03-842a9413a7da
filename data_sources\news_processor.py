"""
新闻处理模块
提供新闻去重、合并和优化功能
"""

import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import time
import logging
import re
import json
from collections import defaultdict
import jieba
import jieba.analyse
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
import hashlib

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/news_processor.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('news_processor')

class NewsProcessor:
    """新闻处理类"""
    
    def __init__(self, cache_dir='data/cache/news_processor'):
        """
        初始化新闻处理器
        
        Args:
            cache_dir (str): 缓存目录
        """
        self.cache_dir = cache_dir
        os.makedirs(cache_dir, exist_ok=True)
        logger.info(f"NewsProcessor initialized with cache_dir: {cache_dir}")
        
        # 加载停用词
        self.stopwords = self._load_stopwords()
        
        # 初始化TF-IDF向量化器
        self.vectorizer = TfidfVectorizer(
            analyzer='word',
            tokenizer=self._tokenize,
            max_features=5000
        )
        
        # 新闻相似度阈值
        self.similarity_threshold = 0.7
        
        # 新闻热度衰减因子（每小时）
        self.decay_factor = 0.95
        
        # 新闻热度基础分
        self.base_score = 1.0
        
        # 新闻来源权重
        self.source_weights = {
            '东方财富': 1.0,
            '东方财富财经早餐': 1.2,
            '东方财富全球财经快讯': 1.1,
            '东方财富研究报告': 1.3,
            '新浪财经全球财经快讯': 1.0,
            '新浪财经证券原创': 1.2,
            '富途牛牛快讯': 0.9,
            '同花顺财经全球财经直播': 1.0,
            '财联社电报': 1.1,
            '财新网': 1.3,
            '国务院': 1.5,
            '发改委': 1.5,
            '证监会': 1.5,
            '默认': 1.0
        }
    
    def _load_stopwords(self):
        """加载停用词"""
        try:
            # 尝试加载停用词文件
            stopwords_file = os.path.join(self.cache_dir, 'stopwords.txt')
            if os.path.exists(stopwords_file):
                with open(stopwords_file, 'r', encoding='utf-8') as f:
                    stopwords = set([line.strip() for line in f])
                logger.info(f"Loaded {len(stopwords)} stopwords from file")
                return stopwords
            
            # 如果文件不存在，使用默认停用词
            stopwords = set(['的', '了', '和', '是', '在', '有', '为', '以', '与', '这', '那', '从', '到', '由', '也', '或', '但', '并', '等', '将', '用', '于', '对', '之', '一', '地', '中', '上', '下', '时', '就', '要', '能', '可', '会', '年', '月', '日'])
            
            # 保存停用词文件
            with open(stopwords_file, 'w', encoding='utf-8') as f:
                for word in stopwords:
                    f.write(word + '\n')
            
            logger.info(f"Created default stopwords file with {len(stopwords)} words")
            return stopwords
        
        except Exception as e:
            logger.error(f"Error loading stopwords: {str(e)}")
            return set(['的', '了', '和', '是', '在', '有', '为', '以', '与', '这', '那', '从', '到', '由', '也', '或', '但', '并', '等', '将', '用', '于', '对', '之', '一', '地', '中', '上', '下', '时', '就', '要', '能', '可', '会', '年', '月', '日'])
    
    def _tokenize(self, text):
        """分词并去除停用词"""
        if not isinstance(text, str):
            return []
        
        # 使用jieba分词
        words = jieba.cut(text)
        
        # 去除停用词
        return [word for word in words if word not in self.stopwords and len(word.strip()) > 1]
    
    def _extract_keywords(self, text, topK=5):
        """提取关键词"""
        if not isinstance(text, str) or not text.strip():
            return []
        
        # 使用jieba提取关键词
        keywords = jieba.analyse.extract_tags(text, topK=topK)
        return keywords
    
    def _calculate_text_similarity(self, text1, text2):
        """计算文本相似度"""
        if not isinstance(text1, str) or not isinstance(text2, str):
            return 0.0
        
        if not text1.strip() or not text2.strip():
            return 0.0
        
        try:
            # 将文本转换为TF-IDF向量
            tfidf_matrix = self.vectorizer.fit_transform([text1, text2])
            
            # 计算余弦相似度
            similarity = cosine_similarity(tfidf_matrix[0:1], tfidf_matrix[1:2])[0][0]
            
            return similarity
        except Exception as e:
            logger.error(f"Error calculating text similarity: {str(e)}")
            return 0.0
    
    def _generate_news_id(self, news):
        """生成新闻ID"""
        # 使用标题和来源生成唯一ID
        title = news.get('title', '')
        source = news.get('source', '')
        url = news.get('url', '')
        
        # 如果有URL，使用URL作为ID的一部分
        if url:
            id_str = f"{title}_{source}_{url}"
        else:
            id_str = f"{title}_{source}"
        
        # 使用MD5生成唯一ID
        return hashlib.md5(id_str.encode('utf-8')).hexdigest()
    
    def _calculate_news_score(self, news, current_time=None):
        """计算新闻热度分数"""
        if current_time is None:
            current_time = datetime.now()
        
        # 获取新闻发布时间
        publish_date = news.get('publish_date', '')
        if not publish_date:
            # 如果没有发布时间，使用当前时间
            publish_time = current_time
        else:
            try:
                # 尝试解析发布时间
                publish_time = datetime.strptime(publish_date, '%Y-%m-%d %H:%M:%S')
            except:
                try:
                    # 尝试其他格式
                    publish_time = datetime.strptime(publish_date, '%Y-%m-%d')
                except:
                    # 如果解析失败，使用当前时间
                    publish_time = current_time
        
        # 计算时间差（小时）
        time_diff = (current_time - publish_time).total_seconds() / 3600
        
        # 计算时间衰减因子
        time_decay = self.decay_factor ** time_diff
        
        # 获取来源权重
        source = news.get('source', '默认')
        source_weight = self.source_weights.get(source, self.source_weights['默认'])
        
        # 计算基础分数
        base_score = self.base_score * source_weight
        
        # 计算最终分数
        score = base_score * time_decay
        
        return score
    
    def process_news(self, news_list):
        """
        处理新闻列表，去重并计算热度
        
        Args:
            news_list (list): 新闻列表，每个元素是一个字典
            
        Returns:
            list: 处理后的新闻列表
        """
        if not news_list:
            return []
        
        logger.info(f"Processing {len(news_list)} news items")
        start_time = time.time()
        
        # 转换为DataFrame
        news_df = pd.DataFrame(news_list)
        
        # 确保必要的列存在
        required_columns = ['title', 'source', 'publish_date', 'url']
        for col in required_columns:
            if col not in news_df.columns:
                news_df[col] = ''
        
        # 生成新闻ID
        news_df['news_id'] = news_df.apply(lambda x: self._generate_news_id(x), axis=1)
        
        # 去除完全重复的新闻（相同ID）
        news_df = news_df.drop_duplicates(subset=['news_id'])
        
        # 提取关键词
        news_df['keywords'] = news_df['title'].apply(lambda x: self._extract_keywords(x))
        
        # 计算热度分数
        current_time = datetime.now()
        news_df['score'] = news_df.apply(lambda x: self._calculate_news_score(x, current_time), axis=1)
        
        # 按热度分数排序
        news_df = news_df.sort_values(by='score', ascending=False)
        
        # 相似度去重
        unique_news = []
        processed_ids = set()
        
        for _, news in news_df.iterrows():
            news_id = news['news_id']
            
            # 如果已经处理过，跳过
            if news_id in processed_ids:
                continue
            
            # 标记为已处理
            processed_ids.add(news_id)
            
            # 添加到结果列表
            unique_news.append(news.to_dict())
            
            # 查找相似新闻
            for _, other_news in news_df.iterrows():
                other_id = other_news['news_id']
                
                # 如果已经处理过，跳过
                if other_id in processed_ids:
                    continue
                
                # 计算标题相似度
                similarity = self._calculate_text_similarity(news['title'], other_news['title'])
                
                # 如果相似度超过阈值，标记为已处理，并增加当前新闻的热度
                if similarity > self.similarity_threshold:
                    processed_ids.add(other_id)
                    unique_news[-1]['score'] += other_news['score'] * 0.5  # 增加50%的热度
        
        # 再次按热度分数排序
        unique_news = sorted(unique_news, key=lambda x: x['score'], reverse=True)
        
        end_time = time.time()
        logger.info(f"Processed news in {end_time - start_time:.2f}s. Reduced from {len(news_list)} to {len(unique_news)} items")
        
        return unique_news
    
    def save_processed_news(self, news_list, filename='processed_news.json'):
        """
        保存处理后的新闻
        
        Args:
            news_list (list): 处理后的新闻列表
            filename (str): 保存的文件名
            
        Returns:
            bool: 是否保存成功
        """
        try:
            filepath = os.path.join(self.cache_dir, filename)
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(news_list, f, ensure_ascii=False, indent=2)
            
            logger.info(f"Saved {len(news_list)} processed news items to {filepath}")
            return True
        
        except Exception as e:
            logger.error(f"Error saving processed news: {str(e)}")
            return False
    
    def load_processed_news(self, filename='processed_news.json'):
        """
        加载处理后的新闻
        
        Args:
            filename (str): 文件名
            
        Returns:
            list: 处理后的新闻列表
        """
        try:
            filepath = os.path.join(self.cache_dir, filename)
            if not os.path.exists(filepath):
                logger.warning(f"Processed news file not found: {filepath}")
                return []
            
            with open(filepath, 'r', encoding='utf-8') as f:
                news_list = json.load(f)
            
            logger.info(f"Loaded {len(news_list)} processed news items from {filepath}")
            return news_list
        
        except Exception as e:
            logger.error(f"Error loading processed news: {str(e)}")
            return []
    
    def update_processed_news(self, new_news_list, filename='processed_news.json'):
        """
        更新处理后的新闻
        
        Args:
            new_news_list (list): 新的新闻列表
            filename (str): 文件名
            
        Returns:
            list: 更新后的新闻列表
        """
        # 加载已处理的新闻
        existing_news = self.load_processed_news(filename)
        
        # 合并新闻列表
        combined_news = existing_news + new_news_list
        
        # 处理合并后的新闻
        processed_news = self.process_news(combined_news)
        
        # 保存处理后的新闻
        self.save_processed_news(processed_news, filename)
        
        return processed_news
