"""
Configuration loader module for policy_liquidity_volatility_arbitrage.
Loads configuration from config/config.yaml.
"""

import os
import yaml
from utils.logger import logger

class ConfigLoader:
    """
    Configuration loader class.
    Loads and provides access to configuration settings.
    """
    
    def __init__(self, config_path="config/config.yaml"):
        """
        Initialize the ConfigLoader.
        
        Args:
            config_path (str): Path to the configuration file.
        """
        self.config_path = config_path
        self.config = self._load_config()
        
    def _load_config(self):
        """
        Load configuration from YAML file.
        
        Returns:
            dict: Configuration dictionary.
        """
        try:
            if not os.path.exists(self.config_path):
                logger.warning(f"Configuration file not found at {self.config_path}. Using default configuration.")
                return self._create_default_config()
            
            with open(self.config_path, 'r', encoding='utf-8') as file:
                config = yaml.safe_load(file)
                logger.info(f"Configuration loaded from {self.config_path}")
                return config
        except Exception as e:
            logger.error(f"Error loading configuration: {str(e)}")
            logger.info("Using default configuration.")
            return self._create_default_config()
    
    def _create_default_config(self):
        """
        Create default configuration.
        
        Returns:
            dict: Default configuration dictionary.
        """
        default_config = {
            "paths": {
                "data_dir": "data",
                "logs_dir": "logs",
                "models_dir": "models"
            },
            "api": {
                "akshare": {
                    "timeout": 30
                }
            },
            "engines": {
                "news_policy": {
                    "sources": {
                        "policy": [
                            "国务院",
                            "央行",
                            "财政部",
                            "发改委",
                            "证监会"
                        ],
                        "news": [
                            "新浪财经",
                            "东方财富网",
                            "中国证券报"
                        ]
                    },
                    "lookback_days": 7
                },
                "tiered_fund_flow": {
                    "lookback_days": 30
                },
                "volatility": {
                    "historical_window": 20,
                    "garch_params": {
                        "p": 1,
                        "q": 1
                    }
                }
            },
            "decision_engine": {
                "weights": {
                    "news_policy_score": 0.3,
                    "tiered_flow_score": 0.4,
                    "volatility_factor": 0.3
                },
                "top_n": 30
            }
        }
        
        # Create config directory if it doesn't exist
        os.makedirs(os.path.dirname(self.config_path), exist_ok=True)
        
        # Save default configuration
        try:
            with open(self.config_path, 'w', encoding='utf-8') as file:
                yaml.dump(default_config, file, default_flow_style=False, allow_unicode=True)
                logger.info(f"Default configuration saved to {self.config_path}")
        except Exception as e:
            logger.error(f"Error saving default configuration: {str(e)}")
        
        return default_config
    
    def get(self, key, default=None):
        """
        Get configuration value by key.
        
        Args:
            key (str): Configuration key (can be nested using dot notation, e.g., 'engines.news_policy.lookback_days').
            default: Default value to return if key is not found.
            
        Returns:
            Configuration value or default.
        """
        keys = key.split('.')
        value = self.config
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            logger.warning(f"Configuration key '{key}' not found. Using default value: {default}")
            return default
    
    def reload(self):
        """
        Reload configuration from file.
        
        Returns:
            dict: Updated configuration dictionary.
        """
        self.config = self._load_config()
        return self.config

# Default config loader instance
config_loader = ConfigLoader()
