# 领域驱动的混合数据库设计方案

## 1. 设计理念

本文档提出了一种领域驱动的混合数据库设计方案，旨在为政策-流动性-波动率套利系统提供高效、灵活且一致的数据存储和访问机制。该方案基于对系统各模块数据特性和访问模式的深入分析，采用"按数据特性选择存储方式"而非"按模块划分数据库"的设计思路。

### 1.1 核心设计原则

1. **领域驱动设计**：按照数据的领域特性而非模块边界划分数据存储
2. **专业化存储**：为不同特性的数据选择最适合的存储技术
3. **统一访问**：提供统一的数据访问接口，屏蔽底层存储差异
4. **数据一致性**：通过数据同步服务确保跨存储的数据一致性
5. **性能优先**：针对高频访问模式进行专门优化
6. **可扩展性**：支持系统规模和功能的持续扩展

## 2. 数据特性分析

通过对系统各模块数据需求的分析，我们识别出以下几类具有不同特性的数据：

### 2.1 基础数据

- **特点**：结构化、相对稳定、被多个模块共享
- **示例**：股票基本信息、行业分类、交易日历
- **访问模式**：高频读取、低频更新
- **最适存储**：关系型数据库

### 2.2 时间序列数据

- **特点**：结构化、时间维度、大量数据点、需要高效时间范围查询
- **示例**：股票价格、指数数据、资金流数据、波动率数据
- **访问模式**：高频读取特定时间范围、批量写入、聚合计算
- **最适存储**：时序数据库

### 2.3 文档数据

- **特点**：半结构化、内容丰富、需要全文搜索
- **示例**：政策文件、新闻文章、分析报告
- **访问模式**：全文检索、关键词匹配、语义分析
- **最适存储**：文档数据库

### 2.4 分析结果数据

- **特点**：结构多变、依赖于原始数据、需要版本控制
- **示例**：政策解析结果、情绪分析结果、波动率分析结果
- **访问模式**：写入后多次读取、按版本查询
- **最适存储**：文档数据库 + 关系型元数据

### 2.5 实时数据

- **特点**：高频更新、短暂有效、需要快速访问
- **示例**：实时行情、热点话题、最新政策解析
- **访问模式**：极高频读写、发布订阅、过期淘汰
- **最适存储**：内存数据库/缓存

## 3. 混合数据库架构

基于上述分析，我们设计了以下混合数据库架构：

```
+--------------------------------------------------------------+
|                     统一数据访问接口                           |
+--------------------------------------------------------------+
                |                |                |
+---------------+    +----------+    +-----------+    +--------+
| 关系型数据库    |    | 时序数据库 |    | 文档数据库  |    | 内存库 |
| (PostgreSQL)  |    | (InfluxDB) |    | (MongoDB)  |    | (Redis)|
+---------------+    +------------+    +-----------+    +--------+
      |                   |                 |               |
+--------------------------------------------------------------+
|                     数据同步服务                              |
+--------------------------------------------------------------+
```

### 3.1 关系型数据库 (PostgreSQL)

**存储内容**：
- 股票基本信息表
- 行业分类表
- 交易日历表
- 数据源配置表
- 系统参数表
- 用户设置表
- 元数据索引表（指向其他数据库的数据）

**优势**：
- 强大的事务支持和数据一致性保证
- 复杂关系和约束的处理能力
- 成熟的索引和查询优化
- 广泛的工具和生态系统

### 3.2 时序数据库 (InfluxDB)

**存储内容**：
- 股票价格数据
- 指数数据
- 北向南向资金流数据
- 行业资金流数据
- 个股资金流数据
- 波动率数据
- 情绪指标时间序列

**优势**：
- 针对时间序列数据优化的存储结构
- 高效的时间范围查询和聚合
- 内置的降采样和数据保留策略
- 专为高写入吞吐量设计

### 3.3 文档数据库 (MongoDB)

**存储内容**：
- 政策文件内容
- 新闻文章内容
- 政策解析结果
- 新闻分析结果
- 热点话题分析
- 情绪共振分析报告
- 波动率分析报告

**优势**：
- 灵活的文档模型适应半结构化数据
- 强大的查询语言支持复杂查询
- 内置的全文搜索能力
- 良好的水平扩展能力

### 3.4 内存数据库 (Redis)

**存储内容**：
- 实时行情数据缓存
- 热点数据缓存
- 计算结果缓存
- 会话数据
- 发布/订阅消息队列
- 分布式锁

**优势**：
- 极高的读写性能
- 丰富的数据结构
- 内置的过期机制
- 发布/订阅模式支持

## 4. 数据模型设计

### 4.1 关系型数据库模型

#### 4.1.1 股票基本信息表 (stocks)

```sql
CREATE TABLE stocks (
    code VARCHAR(10) PRIMARY KEY,
    name VARCHAR(50) NOT NULL,
    listing_date DATE,
    industry_code VARCHAR(10),
    sector_code VARCHAR(10),
    market VARCHAR(10),
    is_st BOOLEAN DEFAULT FALSE,
    is_suspended BOOLEAN DEFAULT FALSE,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 4.1.2 行业分类表 (industries)

```sql
CREATE TABLE industries (
    code VARCHAR(10) PRIMARY KEY,
    name VARCHAR(50) NOT NULL,
    level INTEGER NOT NULL,
    parent_code VARCHAR(10),
    description TEXT,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 4.1.3 交易日历表 (trading_calendar)

```sql
CREATE TABLE trading_calendar (
    date DATE PRIMARY KEY,
    is_trading_day BOOLEAN NOT NULL,
    week_of_year INTEGER,
    day_of_week INTEGER,
    quarter INTEGER,
    month INTEGER,
    description TEXT
);
```

#### 4.1.4 元数据索引表 (metadata_index)

```sql
CREATE TABLE metadata_index (
    id SERIAL PRIMARY KEY,
    data_type VARCHAR(50) NOT NULL,
    reference_id VARCHAR(100) NOT NULL,
    storage_type VARCHAR(20) NOT NULL,
    storage_location VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(data_type, reference_id)
);
```

### 4.2 时序数据库模型

#### 4.2.1 股票价格数据 (stock_prices)

```
measurement: stock_prices
tags:
  - code: 股票代码
  - market: 市场类型
fields:
  - open: 开盘价
  - high: 最高价
  - low: 最低价
  - close: 收盘价
  - volume: 成交量
  - amount: 成交额
  - change_pct: 涨跌幅
timestamp: 时间戳
```

#### 4.2.2 资金流数据 (fund_flows)

```
measurement: fund_flows
tags:
  - type: 资金类型 (north, south, sector, stock)
  - code: 代码 (股票代码或行业代码)
fields:
  - net_flow: 净流入
  - inflow: 流入
  - outflow: 流出
  - accumulated: 累计净流入
timestamp: 时间戳
```

#### 4.2.3 波动率数据 (volatility)

```
measurement: volatility
tags:
  - type: 类型 (market, sector, stock)
  - code: 代码
  - window: 窗口期 (20d, 60d)
fields:
  - value: 波动率值
  - realized: 已实现波动率
  - implied: 隐含波动率
  - skew: 偏度
timestamp: 时间戳
```

### 4.3 文档数据库模型

#### 4.3.1 政策元数据 (policy_metadata)

```json
{
  "_id": "ObjectId",
  "policy_id": "唯一标识符",
  "title": "政策标题",
  "source": "发布机构",
  "publish_date": "ISODate",
  "policy_code": "政策文号",
  "policy_type": "产业政策",
  "url": "原文链接",
  "importance_score": 0.8,
  "is_parsed": true,
  "content_storage_path": "policies/2025-05/ndrc/policy_20250515_123456.json",
  "created_at": "ISODate",
  "updated_at": "ISODate"
}
```

#### 4.3.2 政策解析结果 (policy_analysis)

```json
{
  "_id": "ObjectId",
  "policy_id": "政策ID",
  "parsed_at": "ISODate",
  "subject": "政策主体",
  "action": "政策动作",
  "object": "影响对象",
  "policy_type": "政策类型",
  "sentiment": {
    "score": 0.75,
    "label": "positive",
    "confidence": 0.92
  },
  "keywords": [
    {"word": "关键词1", "weight": 0.85, "is_entity": true, "entity_type": "industry"},
    {"word": "关键词2", "weight": 0.72, "is_entity": false}
  ],
  "industry_impacts": {
    "行业1": 0.8,
    "行业2": 0.6
  },
  "stock_impacts": [
    {"code": "000001", "impact": 0.7},
    {"code": "600000", "impact": 0.5}
  ],
  "summary": "这是政策的自动生成摘要，包含核心内容和要点...",
  "key_points": [
    "政策要点1",
    "政策要点2"
  ],
  "decay_factor": 0.05,
  "version": 1
}
```

#### 4.3.3 新闻元数据 (news_metadata)

```json
{
  "_id": "ObjectId",
  "news_id": "唯一标识符",
  "title": "新闻标题",
  "source": "新闻来源",
  "publish_date": "ISODate",
  "url": "原文链接",
  "importance_score": 0.7,
  "is_processed": true,
  "content_storage_path": "news/2025-05/eastmoney/news_20250515_123456.json",
  "created_at": "ISODate",
  "updated_at": "ISODate"
}
```

#### 4.3.4 新闻分析结果 (news_analysis)

```json
{
  "_id": "ObjectId",
  "news_id": "新闻ID",
  "processed_at": "ISODate",
  "sentiment": {
    "score": 0.65,
    "label": "positive",
    "confidence": 0.88
  },
  "keywords": [
    {"word": "关键词1", "weight": 0.78, "is_entity": true, "entity_type": "company"},
    {"word": "关键词2", "weight": 0.65, "is_entity": false}
  ],
  "related_stocks": [
    {"code": "000001", "relevance": 0.9},
    {"code": "600000", "relevance": 0.7}
  ],
  "related_industries": [
    {"code": "BK0475", "name": "银行", "relevance": 0.85}
  ],
  "summary": "这是新闻的自动生成摘要，包含核心内容和要点...",
  "key_points": [
    "新闻要点1",
    "新闻要点2"
  ],
  "cluster_id": "20250515_finance_cluster1",
  "topic_tags": ["货币政策", "利率调整"],
  "version": 1
}
```

#### 4.3.5 热点话题 (hot_topics)

```json
{
  "_id": "ObjectId",
  "topic_id": "20250515_topic1",
  "date": "ISODate",
  "name": "热点话题名称",
  "keywords": [
    {"word": "关键词1", "weight": 0.9},
    {"word": "关键词2", "weight": 0.8}
  ],
  "related_news": [
    {"news_id": "新闻ID1", "relevance": 0.95},
    {"news_id": "新闻ID2", "relevance": 0.85}
  ],
  "related_policies": [
    {"policy_id": "政策ID1", "relevance": 0.8}
  ],
  "related_stocks": [
    {"code": "000001", "relevance": 0.75},
    {"code": "600000", "relevance": 0.65}
  ],
  "related_industries": [
    {"code": "BK0475", "name": "银行", "relevance": 0.8}
  ],
  "heat_score": 0.92,
  "sentiment_distribution": {
    "positive": 0.65,
    "neutral": 0.25,
    "negative": 0.1
  },
  "created_at": "ISODate",
  "updated_at": "ISODate"
}
```

### 4.4 内存数据库模型

#### 4.4.1 实时行情缓存

```
Key: stock:realtime:{code}
Value: Hash
{
  price: 当前价格,
  change: 涨跌幅,
  volume: 成交量,
  amount: 成交额,
  bid: 买一价,
  ask: 卖一价,
  updated_at: 更新时间戳
}
Expiry: 60秒
```

#### 4.4.2 热点话题缓存

```
Key: hot_topics:{date}
Value: Sorted Set
[
  {score: 热度分数, member: "话题1"},
  {score: 热度分数, member: "话题2"}
]
Expiry: 1天
```

## 5. 数据访问接口

为了屏蔽底层存储差异，我们设计了统一的数据访问接口：

```python
class UnifiedDataAccess:
    """统一数据访问接口"""

    def __init__(self, config=None):
        """初始化数据访问接口"""
        # 初始化各数据库连接
        self.relational_db = PostgreSQLClient(config.get('postgresql'))
        self.time_series_db = InfluxDBClient(config.get('influxdb'))
        self.document_db = MongoDBClient(config.get('mongodb'))
        self.memory_db = RedisClient(config.get('redis'))

        # 初始化数据同步服务
        self.sync_service = DataSyncService(self)

    def get_data(self, data_type, query_params, options=None):
        """获取数据"""
        # 根据数据类型路由到相应的数据库
        if data_type in self.RELATIONAL_DATA_TYPES:
            return self.relational_db.query(data_type, query_params, options)
        elif data_type in self.TIME_SERIES_DATA_TYPES:
            return self.time_series_db.query(data_type, query_params, options)
        elif data_type in self.DOCUMENT_DATA_TYPES:
            return self.document_db.query(data_type, query_params, options)
        elif data_type in self.MEMORY_DATA_TYPES:
            return self.memory_db.get(data_type, query_params, options)
        else:
            raise ValueError(f"未知的数据类型: {data_type}")

    def save_data(self, data_type, data, options=None):
        """保存数据"""
        # 根据数据类型路由到相应的数据库
        result = None
        if data_type in self.RELATIONAL_DATA_TYPES:
            result = self.relational_db.save(data_type, data, options)
        elif data_type in self.TIME_SERIES_DATA_TYPES:
            result = self.time_series_db.save(data_type, data, options)
        elif data_type in self.DOCUMENT_DATA_TYPES:
            result = self.document_db.save(data_type, data, options)
        elif data_type in self.MEMORY_DATA_TYPES:
            result = self.memory_db.set(data_type, data, options)
        else:
            raise ValueError(f"未知的数据类型: {data_type}")

        # 触发数据同步
        if result and options and options.get('sync', True):
            self.sync_service.sync_data(data_type, data, result)

        return result
```

## 6. 数据同步服务

数据同步服务负责维护跨数据库的数据一致性：

```python
class DataSyncService:
    """数据同步服务"""

    def __init__(self, data_access):
        """初始化数据同步服务"""
        self.data_access = data_access
        self.sync_rules = self._load_sync_rules()

    def _load_sync_rules(self):
        """加载同步规则"""
        # 从配置文件加载同步规则
        return {
            'stocks': {
                'targets': ['memory_db:stock:info:{code}', 'time_series_db:metadata'],
                'fields': ['code', 'name', 'industry_code', 'is_st']
            },
            'policy_analysis': {
                'targets': ['relational_db:metadata_index', 'memory_db:policy:latest'],
                'fields': ['policy_id', 'sentiment_score', 'industry_impacts']
            }
            # 更多同步规则...
        }

    def sync_data(self, data_type, data, result):
        """同步数据"""
        if data_type not in self.sync_rules:
            return

        rule = self.sync_rules[data_type]
        for target in rule['targets']:
            db_type, target_type = target.split(':', 1)
            # 提取需要同步的字段
            sync_data = {k: v for k, v in data.items() if k in rule['fields']}
            # 执行同步
            if db_type == 'memory_db':
                self.data_access.memory_db.set(target_type, sync_data)
            elif db_type == 'relational_db':
                self.data_access.relational_db.save(target_type, sync_data)
            elif db_type == 'time_series_db':
                self.data_access.time_series_db.save(target_type, sync_data)
            elif db_type == 'document_db':
                self.data_access.document_db.save(target_type, sync_data)
```

## 7. 实施计划

### 7.1 第一阶段：基础设施搭建（2周）

1. 安装和配置各数据库系统
   - PostgreSQL 关系型数据库
   - InfluxDB 时序数据库
   - MongoDB 文档数据库
   - Redis 内存数据库

2. 实现基础数据模型和表结构
   - 创建关系型数据库表
   - 设置时序数据库测量
   - 创建文档数据库集合
   - 设计内存数据库键结构

3. 开发统一数据访问接口
   - 实现各数据库客户端
   - 开发统一接口层
   - 实现数据路由逻辑

### 7.2 第二阶段：数据迁移与集成（3周）

1. 数据迁移
   - 将现有JSON文件数据迁移到相应数据库
   - 转换数据格式和结构
   - 验证数据完整性

2. 模块集成
   - 修改各模块代码，使用新的数据访问接口
   - 实现数据同步服务
   - 开发数据一致性检查工具

3. 缓存策略实现
   - 实现多级缓存机制
   - 开发缓存预热功能
   - 实现缓存失效策略

### 7.3 第三阶段：优化与扩展（2周）

1. 性能优化
   - 优化查询性能
   - 实现数据分区策略
   - 优化索引结构

2. 高级功能实现
   - 实现数据版本控制
   - 开发数据备份恢复功能
   - 实现数据变更通知机制

3. 监控与运维
   - 实现数据库监控
   - 开发性能指标收集
   - 实现自动化运维脚本

## 8. 新闻政策数据优化存储方案

针对新闻和政策数据的特点，我们设计了专门的优化存储方案，以解决原始数据量大、访问频率不均衡的问题。

### 8.1 数据分层存储策略

我们采用分层存储策略，将新闻和政策数据分为三个层次：

1. **元数据层**：存储在关系型数据库中
   - 包含标题、来源、发布时间、URL等基本信息
   - 支持高效的索引和查询
   - 占用空间小，访问频率高

2. **分析结果层**：存储在文档数据库中
   - 包含关键词、情感分析结果、行业影响等分析结果
   - 支持复杂查询和聚合分析
   - 占用空间适中，访问频率中等

3. **原始内容层**：存储在外部文件系统或对象存储中
   - 包含完整的原始文本内容
   - 按日期和类型组织目录结构
   - 占用空间大，访问频率低

### 8.2 新闻政策数据存储优化

#### 8.2.1 元数据与分析结果分离

```
+------------------+       +------------------+       +------------------+
| 元数据 (PostgreSQL)|       | 分析结果 (MongoDB) |       | 原始内容 (文件系统) |
+------------------+       +------------------+       +------------------+
| - 政策/新闻ID      |------>| - 政策/新闻ID      |------>| - 政策/新闻ID      |
| - 标题            |       | - 关键词列表       |       | - 完整文本内容     |
| - 来源            |       | - 情感分析结果     |       | - 原始HTML/PDF    |
| - 发布时间        |       | - 行业影响矩阵     |       +------------------+
| - URL            |       | - 相关股票代码     |
| - 重要性评分      |       +------------------+
+------------------+
```

#### 8.2.2 增量更新与历史归档

1. **增量更新机制**：
   - 每次只获取和处理新的新闻和政策数据
   - 使用时间戳和唯一标识符避免重复处理
   - 分析结果增量更新到数据库

2. **历史数据归档**：
   - 按月归档历史新闻和政策原始内容
   - 压缩存储减少空间占用
   - 保留元数据和分析结果用于长期分析

#### 8.2.3 文件存储优化

1. **目录结构设计**：
```
/data
  /news
    /YYYY-MM
      /source1
        news_YYYYMMDD_ID.json
      /source2
        ...
  /policies
    /YYYY-MM
      /source1
        policy_YYYYMMDD_ID.json
      /source2
        ...
```

2. **文件命名规则**：
   - 使用日期和唯一ID作为文件名
   - 便于按时间和来源快速定位文件

3. **压缩策略**：
   - 实时数据不压缩，保证访问速度
   - 历史数据（30天前）压缩存储
   - 超过1年的数据可考虑迁移到冷存储

### 8.3 特征提取与摘要存储

为了进一步优化存储和访问效率，我们对新闻和政策数据进行特征提取和摘要生成：

1. **特征提取**：
   - 提取关键词和实体（公司、人物、地点等）
   - 计算TF-IDF值确定关键词权重
   - 识别行业和主题标签
   - 生成文本向量表示

2. **自动摘要生成**：
   - 使用抽取式或生成式方法创建摘要
   - 提取核心观点和要点列表
   - 摘要长度控制在原文的10-15%

3. **特征数据存储**：
   - 特征数据存储在文档数据库中
   - 与分析结果关联存储
   - 支持基于特征的相似度搜索和聚类分析

### 8.4 缓存策略

针对新闻和政策数据的访问特点，我们设计了多级缓存策略：

1. **热点数据缓存**：
   - 最近24小时的新闻和政策元数据和分析结果
   - 热门话题相关的新闻和政策
   - 存储在内存数据库中，支持快速访问

2. **查询结果缓存**：
   - 缓存常用查询的结果集
   - 设置合理的过期时间
   - 支持按查询参数的部分匹配

3. **预计算结果缓存**：
   - 预先计算并缓存热点话题分析结果
   - 缓存情感分析趋势数据
   - 缓存行业影响评估结果

### 8.5 数据一致性保证

在分离存储的情况下，我们通过以下机制保证数据一致性：

1. **事务处理**：
   - 使用两阶段提交确保跨数据库操作的原子性
   - 失败时自动回滚所有相关操作

2. **引用完整性**：
   - 使用唯一ID关联不同存储层的数据
   - 删除操作级联处理所有相关数据

3. **版本控制**：
   - 为分析结果添加版本号
   - 支持追踪数据变更历史
   - 允许回滚到之前的版本

## 9. 总结

领域驱动的混合数据库设计方案通过为不同特性的数据选择最适合的存储技术，在保持数据一致性的同时，充分发挥各类专业数据库的优势。特别是针对新闻和政策数据，我们采用了分层存储和特征提取的策略，有效解决了原始数据量大、访问模式不均衡的问题。

统一的数据访问接口和数据同步服务确保了系统各模块能够无缝集成，提供一致的数据访问体验。多级缓存策略和增量更新机制进一步提升了系统性能和资源利用效率。

这种设计不仅能满足当前系统的需求，也为未来的扩展和演化提供了坚实的基础。通过分阶段实施，可以平稳地完成从现有存储方式到混合数据库架构的过渡，最终构建一个高效、可靠、可扩展的数据存储系统。

---

*最后更新时间：2025-05-25*
