# 领域驱动的混合数据库设计方案

## 1. 设计理念

本文档提出了一种领域驱动的混合数据库设计方案，旨在为政策-流动性-波动率套利系统提供高效、灵活且一致的数据存储和访问机制。该方案基于对系统各模块数据特性和访问模式的深入分析，采用"按数据特性选择存储方式"而非"按模块划分数据库"的设计思路。

### 1.1 核心设计原则

1. **领域驱动设计**：按照数据的领域特性而非模块边界划分数据存储
2. **专业化存储**：为不同特性的数据选择最适合的存储技术
3. **统一访问**：提供统一的数据访问接口，屏蔽底层存储差异
4. **数据一致性**：通过数据同步服务确保跨存储的数据一致性
5. **性能优先**：针对高频访问模式进行专门优化
6. **可扩展性**：支持系统规模和功能的持续扩展

## 2. 数据特性分析

通过对系统各模块数据需求的分析，我们识别出以下几类具有不同特性的数据：

### 2.1 基础数据

- **特点**：结构化、相对稳定、被多个模块共享
- **示例**：股票基本信息、行业分类、交易日历
- **访问模式**：高频读取、低频更新
- **最适存储**：关系型数据库

### 2.2 时间序列数据

- **特点**：结构化、时间维度、大量数据点、需要高效时间范围查询
- **示例**：股票价格、指数数据、资金流数据、波动率数据
- **访问模式**：高频读取特定时间范围、批量写入、聚合计算
- **最适存储**：时序数据库

### 2.3 文档数据

- **特点**：半结构化、内容丰富、需要全文搜索
- **示例**：政策文件、新闻文章、分析报告
- **访问模式**：全文检索、关键词匹配、语义分析
- **最适存储**：文档数据库

### 2.4 分析结果数据

- **特点**：结构多变、依赖于原始数据、需要版本控制
- **示例**：政策解析结果、情绪分析结果、波动率分析结果
- **访问模式**：写入后多次读取、按版本查询
- **最适存储**：文档数据库 + 关系型元数据

### 2.5 实时数据

- **特点**：高频更新、短暂有效、需要快速访问
- **示例**：实时行情、热点话题、最新政策解析
- **访问模式**：极高频读写、发布订阅、过期淘汰
- **最适存储**：内存数据库/缓存

## 3. 混合数据库架构

基于上述分析，我们设计了以下混合数据库架构：

```
+--------------------------------------------------------------+
|                     统一数据访问接口                           |
+--------------------------------------------------------------+
                |                |                |
+---------------+    +----------+    +-----------+    +--------+
| 关系型数据库    |    | 时序数据库 |    | 文档数据库  |    | 内存库 |
| (PostgreSQL)  |    | (InfluxDB) |    | (MongoDB)  |    | (Redis)|
+---------------+    +------------+    +-----------+    +--------+
      |                   |                 |               |
+--------------------------------------------------------------+
|                     数据同步服务                              |
+--------------------------------------------------------------+
```

### 3.1 关系型数据库 (PostgreSQL)

**存储内容**：
- 股票基本信息表
- 行业分类表
- 交易日历表
- 数据源配置表
- 系统参数表
- 用户设置表
- 元数据索引表（指向其他数据库的数据）

**优势**：
- 强大的事务支持和数据一致性保证
- 复杂关系和约束的处理能力
- 成熟的索引和查询优化
- 广泛的工具和生态系统

### 3.2 时序数据库 (InfluxDB)

**存储内容**：
- 股票价格数据
- 指数数据
- 北向南向资金流数据
- 行业资金流数据
- 个股资金流数据
- 波动率数据
- 情绪指标时间序列

**优势**：
- 针对时间序列数据优化的存储结构
- 高效的时间范围查询和聚合
- 内置的降采样和数据保留策略
- 专为高写入吞吐量设计

### 3.3 文档数据库 (MongoDB)

**存储内容**：
- 政策文件内容
- 新闻文章内容
- 政策解析结果
- 新闻分析结果
- 热点话题分析
- 情绪共振分析报告
- 波动率分析报告

**优势**：
- 灵活的文档模型适应半结构化数据
- 强大的查询语言支持复杂查询
- 内置的全文搜索能力
- 良好的水平扩展能力

### 3.4 内存数据库 (Redis)

**存储内容**：
- 实时行情数据缓存
- 热点数据缓存
- 计算结果缓存
- 会话数据
- 发布/订阅消息队列
- 分布式锁

**优势**：
- 极高的读写性能
- 丰富的数据结构
- 内置的过期机制
- 发布/订阅模式支持

## 4. 数据模型设计

### 4.1 关系型数据库模型

#### 4.1.1 股票基本信息表 (stocks)

```sql
CREATE TABLE stocks (
    code VARCHAR(10) PRIMARY KEY,
    name VARCHAR(50) NOT NULL,
    listing_date DATE,
    industry_code VARCHAR(10),
    sector_code VARCHAR(10),
    market VARCHAR(10),
    is_st BOOLEAN DEFAULT FALSE,
    is_suspended BOOLEAN DEFAULT FALSE,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 4.1.2 行业分类表 (industries)

```sql
CREATE TABLE industries (
    code VARCHAR(10) PRIMARY KEY,
    name VARCHAR(50) NOT NULL,
    level INTEGER NOT NULL,
    parent_code VARCHAR(10),
    description TEXT,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 4.1.3 交易日历表 (trading_calendar)

```sql
CREATE TABLE trading_calendar (
    date DATE PRIMARY KEY,
    is_trading_day BOOLEAN NOT NULL,
    week_of_year INTEGER,
    day_of_week INTEGER,
    quarter INTEGER,
    month INTEGER,
    description TEXT
);
```

#### 4.1.4 元数据索引表 (metadata_index)

```sql
CREATE TABLE metadata_index (
    id SERIAL PRIMARY KEY,
    data_type VARCHAR(50) NOT NULL,
    reference_id VARCHAR(100) NOT NULL,
    storage_type VARCHAR(20) NOT NULL,
    storage_location VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(data_type, reference_id)
);
```

### 4.2 时序数据库模型

#### 4.2.1 股票价格数据 (stock_prices)

```
measurement: stock_prices
tags:
  - code: 股票代码
  - market: 市场类型
fields:
  - open: 开盘价
  - high: 最高价
  - low: 最低价
  - close: 收盘价
  - volume: 成交量
  - amount: 成交额
  - change_pct: 涨跌幅
timestamp: 时间戳
```

#### 4.2.2 资金流数据 (fund_flows)

```
measurement: fund_flows
tags:
  - type: 资金类型 (north, south, sector, stock)
  - code: 代码 (股票代码或行业代码)
fields:
  - net_flow: 净流入
  - inflow: 流入
  - outflow: 流出
  - accumulated: 累计净流入
timestamp: 时间戳
```

#### 4.2.3 波动率数据 (volatility)

```
measurement: volatility
tags:
  - type: 类型 (market, sector, stock)
  - code: 代码
  - window: 窗口期 (20d, 60d)
fields:
  - value: 波动率值
  - realized: 已实现波动率
  - implied: 隐含波动率
  - skew: 偏度
timestamp: 时间戳
```

### 4.3 文档数据库模型

#### 4.3.1 政策文档 (policies)

```json
{
  "_id": "ObjectId",
  "title": "政策标题",
  "source": "发布机构",
  "publish_date": "ISODate",
  "policy_code": "政策文号",
  "content": "政策全文内容",
  "url": "原文链接",
  "metadata": {
    "importance": 0.8,
    "policy_type": "产业政策",
    "is_parsed": true
  },
  "created_at": "ISODate",
  "updated_at": "ISODate"
}
```

#### 4.3.2 政策解析结果 (policy_analysis)

```json
{
  "_id": "ObjectId",
  "policy_id": "政策ID",
  "parsed_at": "ISODate",
  "subject": "政策主体",
  "action": "政策动作",
  "object": "影响对象",
  "policy_type": "政策类型",
  "sentiment_score": 0.75,
  "keywords": ["关键词1", "关键词2"],
  "industry_impacts": {
    "行业1": 0.8,
    "行业2": 0.6
  },
  "decay_factor": 0.05,
  "version": 1
}
```

#### 4.3.3 新闻文档 (news)

```json
{
  "_id": "ObjectId",
  "title": "新闻标题",
  "source": "新闻来源",
  "publish_date": "ISODate",
  "content": "新闻全文",
  "url": "原文链接",
  "metadata": {
    "importance": 0.7,
    "is_processed": true
  },
  "created_at": "ISODate",
  "updated_at": "ISODate"
}
```

### 4.4 内存数据库模型

#### 4.4.1 实时行情缓存

```
Key: stock:realtime:{code}
Value: Hash
{
  price: 当前价格,
  change: 涨跌幅,
  volume: 成交量,
  amount: 成交额,
  bid: 买一价,
  ask: 卖一价,
  updated_at: 更新时间戳
}
Expiry: 60秒
```

#### 4.4.2 热点话题缓存

```
Key: hot_topics:{date}
Value: Sorted Set
[
  {score: 热度分数, member: "话题1"},
  {score: 热度分数, member: "话题2"}
]
Expiry: 1天
```

## 5. 数据访问接口

为了屏蔽底层存储差异，我们设计了统一的数据访问接口：

```python
class UnifiedDataAccess:
    """统一数据访问接口"""
    
    def __init__(self, config=None):
        """初始化数据访问接口"""
        # 初始化各数据库连接
        self.relational_db = PostgreSQLClient(config.get('postgresql'))
        self.time_series_db = InfluxDBClient(config.get('influxdb'))
        self.document_db = MongoDBClient(config.get('mongodb'))
        self.memory_db = RedisClient(config.get('redis'))
        
        # 初始化数据同步服务
        self.sync_service = DataSyncService(self)
    
    def get_data(self, data_type, query_params, options=None):
        """获取数据"""
        # 根据数据类型路由到相应的数据库
        if data_type in self.RELATIONAL_DATA_TYPES:
            return self.relational_db.query(data_type, query_params, options)
        elif data_type in self.TIME_SERIES_DATA_TYPES:
            return self.time_series_db.query(data_type, query_params, options)
        elif data_type in self.DOCUMENT_DATA_TYPES:
            return self.document_db.query(data_type, query_params, options)
        elif data_type in self.MEMORY_DATA_TYPES:
            return self.memory_db.get(data_type, query_params, options)
        else:
            raise ValueError(f"未知的数据类型: {data_type}")
    
    def save_data(self, data_type, data, options=None):
        """保存数据"""
        # 根据数据类型路由到相应的数据库
        result = None
        if data_type in self.RELATIONAL_DATA_TYPES:
            result = self.relational_db.save(data_type, data, options)
        elif data_type in self.TIME_SERIES_DATA_TYPES:
            result = self.time_series_db.save(data_type, data, options)
        elif data_type in self.DOCUMENT_DATA_TYPES:
            result = self.document_db.save(data_type, data, options)
        elif data_type in self.MEMORY_DATA_TYPES:
            result = self.memory_db.set(data_type, data, options)
        else:
            raise ValueError(f"未知的数据类型: {data_type}")
        
        # 触发数据同步
        if result and options and options.get('sync', True):
            self.sync_service.sync_data(data_type, data, result)
        
        return result
```

## 6. 数据同步服务

数据同步服务负责维护跨数据库的数据一致性：

```python
class DataSyncService:
    """数据同步服务"""
    
    def __init__(self, data_access):
        """初始化数据同步服务"""
        self.data_access = data_access
        self.sync_rules = self._load_sync_rules()
    
    def _load_sync_rules(self):
        """加载同步规则"""
        # 从配置文件加载同步规则
        return {
            'stocks': {
                'targets': ['memory_db:stock:info:{code}', 'time_series_db:metadata'],
                'fields': ['code', 'name', 'industry_code', 'is_st']
            },
            'policy_analysis': {
                'targets': ['relational_db:metadata_index', 'memory_db:policy:latest'],
                'fields': ['policy_id', 'sentiment_score', 'industry_impacts']
            }
            # 更多同步规则...
        }
    
    def sync_data(self, data_type, data, result):
        """同步数据"""
        if data_type not in self.sync_rules:
            return
        
        rule = self.sync_rules[data_type]
        for target in rule['targets']:
            db_type, target_type = target.split(':', 1)
            # 提取需要同步的字段
            sync_data = {k: v for k, v in data.items() if k in rule['fields']}
            # 执行同步
            if db_type == 'memory_db':
                self.data_access.memory_db.set(target_type, sync_data)
            elif db_type == 'relational_db':
                self.data_access.relational_db.save(target_type, sync_data)
            elif db_type == 'time_series_db':
                self.data_access.time_series_db.save(target_type, sync_data)
            elif db_type == 'document_db':
                self.data_access.document_db.save(target_type, sync_data)
```

## 7. 实施计划

### 7.1 第一阶段：基础设施搭建（2周）

1. 安装和配置各数据库系统
   - PostgreSQL 关系型数据库
   - InfluxDB 时序数据库
   - MongoDB 文档数据库
   - Redis 内存数据库

2. 实现基础数据模型和表结构
   - 创建关系型数据库表
   - 设置时序数据库测量
   - 创建文档数据库集合
   - 设计内存数据库键结构

3. 开发统一数据访问接口
   - 实现各数据库客户端
   - 开发统一接口层
   - 实现数据路由逻辑

### 7.2 第二阶段：数据迁移与集成（3周）

1. 数据迁移
   - 将现有JSON文件数据迁移到相应数据库
   - 转换数据格式和结构
   - 验证数据完整性

2. 模块集成
   - 修改各模块代码，使用新的数据访问接口
   - 实现数据同步服务
   - 开发数据一致性检查工具

3. 缓存策略实现
   - 实现多级缓存机制
   - 开发缓存预热功能
   - 实现缓存失效策略

### 7.3 第三阶段：优化与扩展（2周）

1. 性能优化
   - 优化查询性能
   - 实现数据分区策略
   - 优化索引结构

2. 高级功能实现
   - 实现数据版本控制
   - 开发数据备份恢复功能
   - 实现数据变更通知机制

3. 监控与运维
   - 实现数据库监控
   - 开发性能指标收集
   - 实现自动化运维脚本

## 8. 总结

领域驱动的混合数据库设计方案通过为不同特性的数据选择最适合的存储技术，在保持数据一致性的同时，充分发挥各类专业数据库的优势。统一的数据访问接口和数据同步服务确保了系统各模块能够无缝集成，提供一致的数据访问体验。

这种设计不仅能满足当前系统的需求，也为未来的扩展和演化提供了坚实的基础。通过分阶段实施，可以平稳地完成从现有存储方式到混合数据库架构的过渡，最终构建一个高效、可靠、可扩展的数据存储系统。

---

*最后更新时间：2025-05-24*
