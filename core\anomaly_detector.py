"""
异动检测器模块
负责检测政策、新闻、资金流等数据的异常变化
"""

import os
import json
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import logging

from utils.logger import logger
from utils.config_loader import config_loader

class AnomalyDetector:
    """异动检测器类"""
    
    def __init__(self, config=None):
        """
        初始化异动检测器
        
        Args:
            config: 配置对象
        """
        self.config = config if config else config_loader
        
        # 异动检测阈值
        self.thresholds = {
            'policy_importance': 0.8,  # 政策重要性阈值
            'news_sentiment_change': 0.3,  # 新闻情绪变化阈值
            'fund_flow_change': 0.2,  # 资金流变化阈值
            'volatility_spike': 2.0,  # 波动率异常阈值
            'volume_spike': 3.0,  # 成交量异常阈值
        }
        
        # 历史数据缓存
        self.historical_data = {}
        
        logger.info("异动检测器初始化完成")
    
    def detect_policy_anomaly(self, policy_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        检测政策异动
        
        Args:
            policy_data: 政策数据
            
        Returns:
            dict: 异动检测结果
        """
        try:
            anomalies = []
            
            # 检测重要政策发布
            if policy_data.get('importance_score', 0) > self.thresholds['policy_importance']:
                anomalies.append({
                    'type': 'high_importance_policy',
                    'title': policy_data.get('title', ''),
                    'importance_score': policy_data.get('importance_score', 0),
                    'affected_industries': policy_data.get('affected_industries', []),
                    'timestamp': datetime.now().isoformat(),
                    'description': f"发现高重要性政策: {policy_data.get('title', '')}"
                })
            
            # 检测政策密集发布
            recent_policies = self._get_recent_policies(hours=24)
            if len(recent_policies) > 5:  # 24小时内超过5个政策
                anomalies.append({
                    'type': 'policy_cluster',
                    'count': len(recent_policies),
                    'timestamp': datetime.now().isoformat(),
                    'description': f"24小时内密集发布{len(recent_policies)}个政策"
                })
            
            return {
                'has_anomaly': len(anomalies) > 0,
                'anomalies': anomalies,
                'detection_time': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"政策异动检测失败: {str(e)}")
            return {'has_anomaly': False, 'anomalies': [], 'error': str(e)}
    
    def detect_news_anomaly(self, news_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        检测新闻异动
        
        Args:
            news_data: 新闻数据
            
        Returns:
            dict: 异动检测结果
        """
        try:
            anomalies = []
            
            # 检测情绪急剧变化
            current_sentiment = news_data.get('sentiment_score', 0)
            historical_sentiment = self._get_historical_sentiment()
            
            if abs(current_sentiment - historical_sentiment) > self.thresholds['news_sentiment_change']:
                anomalies.append({
                    'type': 'sentiment_spike',
                    'current_sentiment': current_sentiment,
                    'historical_sentiment': historical_sentiment,
                    'change': current_sentiment - historical_sentiment,
                    'timestamp': datetime.now().isoformat(),
                    'description': f"市场情绪急剧变化: {current_sentiment - historical_sentiment:.2f}"
                })
            
            # 检测热点新闻
            if news_data.get('heat_score', 0) > 0.8:
                anomalies.append({
                    'type': 'hot_news',
                    'title': news_data.get('title', ''),
                    'heat_score': news_data.get('heat_score', 0),
                    'related_stocks': news_data.get('related_stocks', []),
                    'timestamp': datetime.now().isoformat(),
                    'description': f"发现热点新闻: {news_data.get('title', '')}"
                })
            
            return {
                'has_anomaly': len(anomalies) > 0,
                'anomalies': anomalies,
                'detection_time': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"新闻异动检测失败: {str(e)}")
            return {'has_anomaly': False, 'anomalies': [], 'error': str(e)}
    
    def detect_fund_flow_anomaly(self, fund_flow_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        检测资金流异动
        
        Args:
            fund_flow_data: 资金流数据
            
        Returns:
            dict: 异动检测结果
        """
        try:
            anomalies = []
            
            # 检测北向资金异动
            northbound_flow = fund_flow_data.get('northbound_flow', 0)
            historical_northbound = self._get_historical_northbound_flow()
            
            if abs(northbound_flow) > abs(historical_northbound) * (1 + self.thresholds['fund_flow_change']):
                anomalies.append({
                    'type': 'northbound_flow_spike',
                    'current_flow': northbound_flow,
                    'historical_avg': historical_northbound,
                    'timestamp': datetime.now().isoformat(),
                    'description': f"北向资金异动: {northbound_flow:.2f}亿元"
                })
            
            # 检测行业资金流异动
            sector_flows = fund_flow_data.get('sector_flows', {})
            for sector, flow in sector_flows.items():
                if abs(flow) > 10:  # 超过10亿的资金流动
                    anomalies.append({
                        'type': 'sector_fund_flow',
                        'sector': sector,
                        'flow': flow,
                        'timestamp': datetime.now().isoformat(),
                        'description': f"{sector}行业资金流异动: {flow:.2f}亿元"
                    })
            
            return {
                'has_anomaly': len(anomalies) > 0,
                'anomalies': anomalies,
                'detection_time': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"资金流异动检测失败: {str(e)}")
            return {'has_anomaly': False, 'anomalies': [], 'error': str(e)}
    
    def detect_volatility_anomaly(self, volatility_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        检测波动率异动
        
        Args:
            volatility_data: 波动率数据
            
        Returns:
            dict: 异动检测结果
        """
        try:
            anomalies = []
            
            # 检测市场波动率异常
            current_vol = volatility_data.get('market_volatility', 0)
            historical_vol = self._get_historical_volatility()
            
            if current_vol > historical_vol * self.thresholds['volatility_spike']:
                anomalies.append({
                    'type': 'volatility_spike',
                    'current_volatility': current_vol,
                    'historical_volatility': historical_vol,
                    'spike_ratio': current_vol / historical_vol,
                    'timestamp': datetime.now().isoformat(),
                    'description': f"市场波动率异常: {current_vol:.2f}% (历史均值: {historical_vol:.2f}%)"
                })
            
            # 检测个股波动率异常
            stock_volatilities = volatility_data.get('stock_volatilities', {})
            for stock_code, vol in stock_volatilities.items():
                if vol > 0.05:  # 日波动率超过5%
                    anomalies.append({
                        'type': 'stock_volatility_spike',
                        'stock_code': stock_code,
                        'volatility': vol,
                        'timestamp': datetime.now().isoformat(),
                        'description': f"个股{stock_code}波动率异常: {vol:.2f}%"
                    })
            
            return {
                'has_anomaly': len(anomalies) > 0,
                'anomalies': anomalies,
                'detection_time': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"波动率异动检测失败: {str(e)}")
            return {'has_anomaly': False, 'anomalies': [], 'error': str(e)}
    
    def _get_recent_policies(self, hours: int = 24) -> List[Dict]:
        """获取最近的政策数据"""
        # 这里应该从数据存储中获取最近的政策数据
        # 暂时返回空列表
        return []
    
    def _get_historical_sentiment(self) -> float:
        """获取历史情绪均值"""
        # 这里应该从历史数据中计算情绪均值
        # 暂时返回中性值
        return 0.0
    
    def _get_historical_northbound_flow(self) -> float:
        """获取历史北向资金流均值"""
        # 这里应该从历史数据中计算北向资金流均值
        # 暂时返回0
        return 0.0
    
    def _get_historical_volatility(self) -> float:
        """获取历史波动率均值"""
        # 这里应该从历史数据中计算波动率均值
        # 暂时返回0.02 (2%)
        return 0.02
