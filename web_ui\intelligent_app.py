"""
智能分析驾驶舱后端服务
集成FinBERT引擎和多因子耦合分析
"""

import os
import sys
import json
import asyncio
from datetime import datetime
from typing import Dict, List, Any, Optional

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from flask import Flask, render_template, jsonify, request
from flask_socketio import SocketIO, emit
import pandas as pd

# 导入核心组件
from core.intelligent_finbert_engine import get_finbert_engine
from core.multi_factor_coupling_engine import MultiFactorCouplingEngine
from core.data_storage import DataStorage
from core.unified_data_collector import UnifiedDataCollector
from utils.logger import logger

class IntelligentDashboardApp:
    """智能分析驾驶舱应用"""
    
    def __init__(self):
        self.app = Flask(__name__)
        self.app.config['SECRET_KEY'] = 'intelligent_dashboard_secret_key'
        self.socketio = SocketIO(self.app, cors_allowed_origins="*")
        
        # 初始化核心组件
        self.data_storage = DataStorage()
        self.data_collector = UnifiedDataCollector()
        self.coupling_engine = MultiFactorCouplingEngine()
        
        # 系统状态
        self.system_status = {
            'main_system': 'online',
            'monitor_system': 'online', 
            'ai_engine': 'online',
            'data_collection': 'active'
        }
        
        # 实时数据缓存
        self.data_cache = {
            'news_count': 0,
            'policy_count': 0,
            'sentiment_score': 0.0,
            'coupling_score': 0.0,
            'recommendations': [],
            'last_update': datetime.now()
        }
        
        self.setup_routes()
        self.setup_socketio_events()
        
        logger.info("智能分析驾驶舱应用初始化完成")
    
    def setup_routes(self):
        """设置路由"""
        
        @self.app.route('/')
        def index():
            return render_template('intelligent_dashboard.html')
        
        @self.app.route('/api/system_status')
        def get_system_status():
            return jsonify(self.system_status)
        
        @self.app.route('/api/dashboard_data')
        def get_dashboard_data():
            return jsonify(self.data_cache)
        
        @self.app.route('/api/finbert_status')
        def get_finbert_status():
            try:
                finbert_engine = get_finbert_engine()
                status = finbert_engine.get_model_status()
                return jsonify({'success': True, 'status': status})
            except Exception as e:
                return jsonify({'success': False, 'error': str(e)})
    
    def setup_socketio_events(self):
        """设置WebSocket事件"""
        
        @self.socketio.on('connect')
        def handle_connect():
            logger.info(f"客户端连接: {request.sid}")
            emit('system_status', self.system_status)
            emit('data_update', self.data_cache)
        
        @self.socketio.on('disconnect')
        def handle_disconnect():
            logger.info(f"客户端断开: {request.sid}")
        
        @self.socketio.on('trigger_data_collection')
        def handle_data_collection():
            """触发数据收集"""
            try:
                logger.info("开始数据收集...")
                
                # 异步执行数据收集
                self.socketio.start_background_task(self.collect_data_async)
                
                emit('notification', {
                    'type': 'success',
                    'message': '数据收集已启动'
                })
                
            except Exception as e:
                logger.error(f"数据收集失败: {str(e)}")
                emit('notification', {
                    'type': 'error',
                    'message': f'数据收集失败: {str(e)}'
                })
        
        @self.socketio.on('run_ai_analysis')
        def handle_ai_analysis():
            """运行AI分析"""
            try:
                logger.info("开始AI分析...")
                
                # 异步执行AI分析
                self.socketio.start_background_task(self.run_ai_analysis_async)
                
                emit('notification', {
                    'type': 'success',
                    'message': 'AI分析已启动'
                })
                
            except Exception as e:
                logger.error(f"AI分析失败: {str(e)}")
                emit('notification', {
                    'type': 'error',
                    'message': f'AI分析失败: {str(e)}'
                })
        
        @self.socketio.on('generate_recommendations')
        def handle_generate_recommendations():
            """生成推荐"""
            try:
                logger.info("开始生成推荐...")
                
                # 异步生成推荐
                self.socketio.start_background_task(self.generate_recommendations_async)
                
                emit('notification', {
                    'type': 'success',
                    'message': '正在生成推荐...'
                })
                
            except Exception as e:
                logger.error(f"生成推荐失败: {str(e)}")
                emit('notification', {
                    'type': 'error',
                    'message': f'生成推荐失败: {str(e)}'
                })
        
        @self.socketio.on('get_coupling_analysis')
        def handle_coupling_analysis():
            """获取耦合分析"""
            try:
                logger.info("开始耦合分析...")
                
                # 异步执行耦合分析
                self.socketio.start_background_task(self.coupling_analysis_async)
                
            except Exception as e:
                logger.error(f"耦合分析失败: {str(e)}")
                emit('notification', {
                    'type': 'error',
                    'message': f'耦合分析失败: {str(e)}'
                })
        
        @self.socketio.on('get_model_metrics')
        def handle_model_metrics():
            """获取模型指标"""
            try:
                finbert_engine = get_finbert_engine()
                metrics = finbert_engine.get_model_status()
                
                emit('model_metrics', metrics)
                
            except Exception as e:
                logger.error(f"获取模型指标失败: {str(e)}")
                emit('notification', {
                    'type': 'error',
                    'message': f'获取模型指标失败: {str(e)}'
                })
    
    def collect_data_async(self):
        """异步数据收集"""
        try:
            # 收集新闻数据
            news_data = self.data_collector.collect_news_data()
            if news_data:
                self.data_cache['news_count'] = len(news_data)
                logger.info(f"收集新闻数据: {len(news_data)}条")
            
            # 收集资金流数据
            fund_flow_data = self.data_collector.collect_fund_flow_data()
            if fund_flow_data:
                logger.info("收集资金流数据完成")
            
            # 收集市场数据
            market_data = self.data_collector.collect_market_data()
            if market_data:
                logger.info("收集市场数据完成")
            
            # 更新缓存
            self.data_cache['last_update'] = datetime.now()
            
            # 发送更新通知
            self.socketio.emit('data_update', self.data_cache)
            self.socketio.emit('notification', {
                'type': 'success',
                'message': '数据收集完成'
            })
            
        except Exception as e:
            logger.error(f"异步数据收集失败: {str(e)}")
            self.socketio.emit('notification', {
                'type': 'error',
                'message': f'数据收集失败: {str(e)}'
            })
    
    def run_ai_analysis_async(self):
        """异步AI分析"""
        try:
            # 获取最新数据
            latest_news = self.data_storage.get_latest_data('news', limit=50)
            
            if latest_news:
                # 提取文本进行情感分析
                texts = []
                for news_batch in latest_news:
                    if isinstance(news_batch, list):
                        for news_item in news_batch:
                            if isinstance(news_item, dict) and 'title' in news_item:
                                texts.append(news_item['title'])
                    elif isinstance(news_batch, dict) and 'title' in news_batch:
                        texts.append(news_batch['title'])
                
                if texts:
                    # 使用FinBERT进行情感分析
                    finbert_engine = get_finbert_engine()
                    sentiment_results = finbert_engine.analyze_sentiment(texts[:20])  # 限制数量
                    
                    if sentiment_results:
                        # 计算平均情感分数
                        sentiment_scores = []
                        for result in sentiment_results:
                            sentiment = result.get('sentiment', 'neutral')
                            if sentiment == 'positive':
                                sentiment_scores.append(1)
                            elif sentiment == 'negative':
                                sentiment_scores.append(-1)
                            else:
                                sentiment_scores.append(0)
                        
                        avg_sentiment = sum(sentiment_scores) / len(sentiment_scores) if sentiment_scores else 0
                        self.data_cache['sentiment_score'] = (avg_sentiment + 1) / 2  # 转换为0-1范围
                        
                        logger.info(f"情感分析完成: 平均分数 {avg_sentiment:.3f}")
            
            # 发送更新通知
            self.socketio.emit('data_update', self.data_cache)
            self.socketio.emit('notification', {
                'type': 'success',
                'message': 'AI分析完成'
            })
            
        except Exception as e:
            logger.error(f"异步AI分析失败: {str(e)}")
            self.socketio.emit('notification', {
                'type': 'error',
                'message': f'AI分析失败: {str(e)}'
            })
    
    def generate_recommendations_async(self):
        """异步生成推荐"""
        try:
            # 模拟推荐生成（实际应该调用主系统）
            recommendations = [
                {'code': '600000', 'name': '浦发银行', 'score': 0.856},
                {'code': '601318', 'name': '中国平安', 'score': 0.834},
                {'code': '600036', 'name': '招商银行', 'score': 0.812},
                {'code': '000001', 'name': '平安银行', 'score': 0.798},
                {'code': '600519', 'name': '贵州茅台', 'score': 0.776}
            ]
            
            self.data_cache['recommendations'] = recommendations
            
            # 计算平均置信度
            avg_confidence = sum(rec['score'] for rec in recommendations) / len(recommendations)
            
            # 发送推荐更新
            self.socketio.emit('recommendations_update', {
                'recommendations': recommendations,
                'avg_confidence': avg_confidence
            })
            
            self.socketio.emit('notification', {
                'type': 'success',
                'message': f'生成{len(recommendations)}个推荐'
            })
            
            logger.info(f"生成推荐完成: {len(recommendations)}个")
            
        except Exception as e:
            logger.error(f"异步生成推荐失败: {str(e)}")
            self.socketio.emit('notification', {
                'type': 'error',
                'message': f'生成推荐失败: {str(e)}'
            })
    
    def coupling_analysis_async(self):
        """异步耦合分析"""
        try:
            # 获取各类数据
            data_streams = {}
            
            # 获取新闻数据
            news_data = self.data_storage.get_latest_data('news', limit=10)
            if news_data:
                data_streams['news'] = pd.DataFrame(news_data[0] if news_data else [])
            
            # 获取资金流数据
            fund_flow_data = self.data_storage.get_latest_data('fund_flow', limit=10)
            if fund_flow_data:
                data_streams['fund_flow'] = pd.DataFrame(fund_flow_data[0] if fund_flow_data else [])
            
            # 执行耦合分析
            if data_streams:
                coupling_result = self.coupling_engine.analyze_coupling(data_streams)
                
                if coupling_result and 'coupling_score' in coupling_result:
                    total_score = coupling_result['coupling_score'].get('total_score', 0)
                    self.data_cache['coupling_score'] = total_score / 100  # 转换为0-1范围
                    
                    # 发送耦合分析结果
                    self.socketio.emit('coupling_analysis_result', coupling_result)
                    
                    logger.info(f"耦合分析完成: 总分 {total_score}")
            
        except Exception as e:
            logger.error(f"异步耦合分析失败: {str(e)}")
            self.socketio.emit('notification', {
                'type': 'error',
                'message': f'耦合分析失败: {str(e)}'
            })
    
    def run(self, host='0.0.0.0', port=5001, debug=False):
        """运行应用"""
        logger.info(f"智能分析驾驶舱启动: http://{host}:{port}")
        self.socketio.run(self.app, host=host, port=port, debug=debug)

def main():
    """主函数"""
    app = IntelligentDashboardApp()
    app.run(debug=True)

if __name__ == '__main__':
    main()
