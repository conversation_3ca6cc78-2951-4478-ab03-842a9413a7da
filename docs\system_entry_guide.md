# 系统入口指南

## 系统架构概述

本系统采用**双系统架构**设计，包含三个主要组件：

### 1. 主分析系统 (main.py)
**功能定位**: 股票分析和推荐生成
**运行模式**: 按需运行，生成分析报告
**适用场景**: 
- 需要获取股票推荐时
- 进行策略回测时
- 生成投资分析报告时

### 2. 24小时监控系统 (monitor_system.py)
**功能定位**: 持续数据收集和异动监控
**运行模式**: 24小时不间断运行
**适用场景**:
- 需要实时监控市场异动
- 自动收集和存储数据
- 接收实时提示和警报

### 3. Web可视化界面 (web_ui/app.py)
**功能定位**: 系统监控和数据展示
**运行模式**: Web服务，支持实时交互
**适用场景**:
- 查看系统运行状态
- 监控数据收集情况
- 可视化分析结果

## 系统关系图

```
┌─────────────────────────────────────────────────────────────┐
│                    系统架构关系图                              │
├─────────────────────────────────────────────────────────────┤
│                                                           │
│  ┌─────────────────┐    ┌─────────────────┐               │
│  │   主分析系统      │    │  24小时监控系统   │               │
│  │   (main.py)     │    │(monitor_system.py)│               │
│  │                 │    │                 │               │
│  │ • 股票筛选       │    │ • 数据收集       │               │
│  │ • 推荐生成       │    │ • 异动检测       │               │
│  │ • 策略分析       │    │ • 实时提示       │               │
│  └─────────────────┘    └─────────────────┘               │
│           │                       │                       │
│           │                       │                       │
│           └───────────┬───────────┘                       │
│                       │                                   │
│                       ▼                                   │
│           ┌─────────────────────────┐                     │
│           │      共享数据层          │                     │
│           │                         │                     │
│           │ • 数据存储 (DataStorage) │                     │
│           │ • 核心引擎 (engines/)   │                     │
│           │ • 工具模块 (utils/)     │                     │
│           └─────────────────────────┘                     │
│                       │                                   │
│                       ▼                                   │
│           ┌─────────────────────────┐                     │
│           │    Web可视化界面        │                     │
│           │   (web_ui/app.py)      │                     │
│           │                         │                     │
│           │ • 系统监控              │                     │
│           │ • 数据展示              │                     │
│           │ • 操作控制              │                     │
│           └─────────────────────────┘                     │
│                                                           │
└─────────────────────────────────────────────────────────────┘
```

## 系统入口方式

### 方式一：直接运行Python脚本

#### 1. 主分析系统
```bash
# 进入项目目录
cd D:\System_Developing\policy_liquidity_volatility_arbitrage

# 运行主分析系统
python main.py
```

#### 2. 24小时监控系统
```bash
# 进入项目目录
cd D:\System_Developing\policy_liquidity_volatility_arbitrage

# 启动监控系统
python monitor_system.py start

# 停止监控系统
python monitor_system.py stop

# 查看监控状态
python monitor_system.py status
```

#### 3. Web可视化界面
```bash
# 进入项目目录
cd D:\System_Developing\policy_liquidity_volatility_arbitrage

# 启动Web界面
python web_ui/app.py
```

### 方式二：使用批处理文件（推荐）

为了方便使用，系统提供了批处理文件：

#### 1. 启动主分析系统
双击运行: `start_main_system.bat`

#### 2. 启动24小时监控系统
双击运行: `start_monitor_system.bat`

#### 3. 启动Web可视化界面
双击运行: `start_web_ui.bat`

#### 4. 停止监控系统
双击运行: `stop_monitor_system.bat`

#### 5. 启动所有系统
双击运行: `start_all_systems.bat`

## 使用建议

### 日常使用流程

1. **首次启动**:
   ```
   1. 运行 start_monitor_system.bat (启动24小时监控)
   2. 运行 start_web_ui.bat (启动Web界面)
   3. 打开浏览器访问 http://127.0.0.1:5000
   ```

2. **获取股票推荐**:
   ```
   1. 运行 start_main_system.bat
   2. 查看生成的推荐结果
   ```

3. **监控系统状态**:
   ```
   1. 通过Web界面查看实时状态
   2. 查看日志文件了解详细信息
   ```

### 系统依赖关系

- **主分析系统**: 独立运行，可以单独使用
- **监控系统**: 独立运行，为其他系统提供数据支持
- **Web界面**: 依赖数据存储，展示监控系统收集的数据

### 数据流向

```
监控系统 → 数据收集 → 数据存储 → Web界面展示
                    ↓
                主分析系统 → 股票推荐
```

## 系统端口和访问地址

- **Web界面**: http://127.0.0.1:5000
- **本地网络访问**: http://192.168.3.7:5000 (如果需要其他设备访问)

## 日志文件位置

- **主系统日志**: `logs/policy_liquidity_volatility_YYYYMMDD_HHMMSS.log`
- **监控系统日志**: `logs/monitor_system_YYYYMMDD.log`
- **Web界面日志**: `logs/web_ui_YYYYMMDD.log`
- **异动提示日志**: `alerts/alerts_YYYYMMDD.log`

## 故障排除

### 常见问题

1. **系统启动失败**:
   - 检查Python环境是否正确
   - 确认所有依赖包已安装
   - 查看日志文件了解具体错误

2. **数据收集失败**:
   - 检查网络连接
   - 确认AKShare API可用
   - 查看监控系统日志

3. **Web界面无法访问**:
   - 确认Flask服务已启动
   - 检查端口5000是否被占用
   - 查看Web界面日志

### 重启系统

如果系统出现问题，可以按以下顺序重启：

1. 停止所有系统: `stop_all_systems.bat`
2. 等待10秒
3. 重新启动: `start_all_systems.bat`

## 系统维护

### 定期维护任务

1. **清理日志文件** (建议每月一次):
   - 删除过期的日志文件
   - 保留最近30天的日志

2. **数据备份** (建议每周一次):
   - 备份data目录下的重要数据
   - 备份配置文件

3. **系统更新** (根据需要):
   - 更新AKShare库到最新版本
   - 更新系统代码

### 性能监控

通过Web界面可以监控：
- 系统运行状态
- 数据收集情况
- 内存和存储使用情况
- 异动检测结果

## 技术支持

如遇到技术问题，请：
1. 查看相关日志文件
2. 检查系统配置文件
3. 参考本文档的故障排除部分
4. 联系技术支持团队
