{"memory_cache": {"enabled": true, "max_size_mb": 512, "ttl_seconds": 3600}, "redis": {"enabled": false, "host": "localhost", "port": 6379, "db": 0, "password": null, "ttl_seconds": 86400}, "database": {"enabled": false, "url": "sqlite:///data/storage.db", "auto_create_tables": true}, "file_storage": {"hot_dir": "data/hot", "warm_dir": "data/warm", "cold_dir": "data/cold", "backup_dir": "data/backup", "hot_to_warm_days": 7, "warm_to_cold_days": 30, "compression": true}, "cleanup": {"enabled": true, "interval_hours": 24, "max_hot_size_mb": 1024, "max_warm_size_mb": 5120, "max_memory_cache_size_mb": 512}}