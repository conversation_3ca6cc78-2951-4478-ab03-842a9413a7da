# 系统测试计划

## 1. 测试目标

本测试计划旨在验证系统各模块的功能完整性、接口兼容性和整体集成效果，确保系统能够按照设计要求正常运行。测试将覆盖核心模块的基本功能、模块间的数据流转以及系统的稳定性和性能。

## 2. 测试环境

- **操作系统**：Windows 10/11 或 Linux
- **Python版本**：Python 3.8+
- **依赖库**：akshare, pandas, numpy, loguru, pytz, tqdm, torch, transformers, pyyaml, feedparser, arch, requests-html, beautifulsoup4, jieba等
- **数据源**：AKShare API、真实市场数据

**重要原则**：
- **禁止使用模拟数据**：所有测试必须使用真实市场数据，禁止使用模拟或虚拟数据进行测试，以确保测试结果的真实性和可靠性
- **数据持久化**：测试过程中获取的数据应当持久化存储，以便后续测试复用，减少对外部API的依赖

## 3. 测试范围

### 3.1 核心基础模块测试

1. **模块接口 (ModuleInterface)**
   - 初始化测试
   - 配置加载测试
   - 任务调度测试
   - 状态获取测试
   - 健康检查测试

2. **数据存储 (DataStorage)**
   - 不同存储级别测试 (HOT, WARM, COLD)
   - 数据保存与加载测试
   - 数据清理测试
   - 备份与恢复测试

3. **任务调度器 (Scheduler)**
   - 任务创建与调度测试
   - 优先级队列测试
   - 任务状态管理测试
   - 资源分配测试
   - 异常处理测试

### 3.2 功能模块测试

1. **政策分析模块 (PolicyAnalyzer)**
   - 政策数据获取测试
   - 三层解析框架测试
   - 政策情感分析测试
   - 政策关键词提取测试
   - 行业影响矩阵测试
   - 政策时间衰减模型测试
   - 政策信号生成测试

2. **新闻监控模块 (NewsMonitor)**
   - 多源新闻获取测试
   - 新闻去重与聚类测试
   - 24小时滚动监控测试
   - 新闻情感分析测试

3. **情绪共振模型 (SentimentResonanceModel)**
   - 多维情绪分析测试
   - 情绪共振检测测试
   - 情绪指标生成测试

4. **资金流分析模块 (FundFlowAnalyzer)**
   - 北向资金数据获取测试
   - 北向资金合成信号测试
   - 游资行为模式识别测试
   - 游资目标股票预测测试
   - 五级分层资金流分析测试
   - 资金流层级联动测试

5. **波动率分析模块 (VolatilityAnalyzer)**
   - 市场波动率计算测试
   - 行业波动率计算测试
   - 个股波动率计算测试
   - 波动率锥分析测试
   - 波动率期限结构测试
   - 波动率偏度分析测试
   - 政策波动率溢价计算测试
   - 资金流-波动率耦合分析测试
   - 波动率信号生成测试
   - 波动率分析报告生成测试

### 3.3 集成测试

1. **模块间数据流测试**
   - 政策数据 -> 波动率分析流程测试
   - 新闻数据 -> 情绪共振模型流程测试
   - 资金流数据 -> 波动率分析流程测试
   - 多模块协同工作测试

2. **系统稳定性测试**
   - 长时间运行测试 (24小时)
   - 大数据量处理测试
   - 异常恢复测试
   - 数据源故障恢复测试

3. **系统操作面板测试**
   - 单模块执行测试
   - 多模块集成执行测试
   - 任务调度测试
   - 结果展示测试

## 4. 测试用例

### 4.1 政策分析模块测试用例

| 测试ID | 测试名称 | 测试步骤 | 预期结果 |
|--------|----------|----------|----------|
| PA-001 | 政策数据获取测试 | 1. 初始化PolicyAnalyzer<br>2. 调用fetch_policies方法 | 成功获取政策数据，返回状态为success |
| PA-002 | 政策三层解析测试 | 1. 提供样本政策数据<br>2. 调用parse_policy_title, parse_policy_content, evaluate_policy_impact方法 | 成功完成三层解析，提取出标题关键信息、正文要点和影响评估 |
| PA-003 | 政策情感分析测试 | 1. 提供样本政策数据<br>2. 调用analyze_policy_sentiment方法 | 成功分析政策情感，返回情感得分和方向 |
| PA-004 | 政策关键词提取测试 | 1. 提供样本政策数据<br>2. 调用extract_policy_keywords方法 | 成功提取政策关键词，返回关键词列表和权重 |
| PA-005 | 行业影响矩阵测试 | 1. 提供已解析的政策数据<br>2. 调用calculate_industry_impacts方法 | 成功计算各行业的影响方向和强度 |
| PA-006 | 政策时间衰减测试 | 1. 提供不同发布时间的政策<br>2. 调用calculate_time_decay方法 | 返回符合预期的衰减因子值 |
| PA-007 | 政策信号生成测试 | 1. 提供完整政策分析结果<br>2. 调用generate_policy_signals方法 | 成功生成政策信号，包含信号类型、强度和描述 |

### 4.2 资金流分析模块测试用例

| 测试ID | 测试名称 | 测试步骤 | 预期结果 |
|--------|----------|----------|----------|
| FF-001 | 北向资金获取测试 | 1. 初始化FundFlowAnalyzer<br>2. 调用fetch_northbound_flow方法 | 成功获取北向资金数据，返回状态为success |
| FF-002 | 北向资金合成信号测试 | 1. 提供样本北向资金数据<br>2. 调用generate_northbound_signals方法 | 成功生成北向资金多周期合成信号 |
| FF-003 | 游资行为模式识别测试 | 1. 初始化FundFlowAnalyzer<br>2. 调用analyze_hot_money_behavior方法 | 成功识别游资活跃股票和行为模式 |
| FF-004 | 游资目标股票预测测试 | 1. 提供游资行为模式数据<br>2. 调用predict_hot_money_targets方法 | 成功预测游资潜在目标股票 |
| FF-005 | 五级资金流获取测试 | 1. 初始化FundFlowAnalyzer<br>2. 调用fetch_tiered_fund_flow方法 | 成功获取五级资金流数据，返回状态为success |
| FF-006 | 资金流层级联动测试 | 1. 提供多层级资金流数据<br>2. 调用analyze_fund_flow_linkage方法 | 成功识别不同层级资金流向的联动关系 |

### 4.3 波动率分析模块测试用例

| 测试ID | 测试名称 | 测试步骤 | 预期结果 |
|--------|----------|----------|----------|
| VA-001 | 市场波动率计算测试 | 1. 初始化VolatilityAnalyzer<br>2. 调用calculate_market_volatility方法 | 成功计算市场波动率，返回状态为success |
| VA-002 | 行业波动率计算测试 | 1. 初始化VolatilityAnalyzer<br>2. 调用calculate_sector_volatility方法 | 成功计算行业波动率，返回状态为success |
| VA-003 | 个股波动率计算测试 | 1. 初始化VolatilityAnalyzer<br>2. 调用calculate_stock_volatility方法 | 成功计算个股波动率，返回状态为success |
| VA-004 | 波动率锥分析测试 | 1. 提供历史波动率数据<br>2. 调用calculate_volatility_cone方法 | 成功构建波动率锥，返回各百分位数据 |
| VA-005 | 波动率期限结构测试 | 1. 提供不同期限波动率数据<br>2. 调用analyze_term_structure方法 | 成功分析期限结构，返回期限结构特征 |
| VA-006 | 政策波动率溢价计算测试 | 1. 初始化VolatilityAnalyzer<br>2. 调用calculate_policy_volatility_premium方法 | 成功计算政策波动率溢价，返回状态为success |
| VA-007 | 资金流-波动率耦合分析测试 | 1. 初始化VolatilityAnalyzer<br>2. 调用calculate_fund_flow_volatility_coupling方法 | 成功计算资金流-波动率耦合，返回状态为success |
| VA-008 | 波动率信号生成测试 | 1. 提供样本波动率数据<br>2. 调用_generate_stock_volatility_signals等方法 | 成功生成波动率信号 |
| VA-009 | 波动率分析报告生成测试 | 1. 初始化VolatilityAnalyzer<br>2. 调用generate_volatility_report方法 | 成功生成波动率分析报告，返回状态为success |

### 4.4 系统操作面板测试用例

| 测试ID | 测试名称 | 测试步骤 | 预期结果 |
|--------|----------|----------|----------|
| OP-001 | 单模块执行测试 | 1. 打开系统操作面板<br>2. 选择单个模块执行<br>3. 设置执行参数<br>4. 点击执行按钮 | 成功执行所选模块，显示执行结果 |
| OP-002 | 多模块集成执行测试 | 1. 打开系统操作面板<br>2. 选择多个模块执行<br>3. 设置执行参数<br>4. 点击执行按钮 | 成功按顺序执行所选模块，数据正确流转，显示执行结果 |
| OP-003 | 定时任务设置测试 | 1. 打开系统操作面板<br>2. 设置定时任务<br>3. 启动定时任务 | 成功在设定时间执行任务 |
| OP-004 | 结果展示测试 | 1. 执行分析任务<br>2. 查看结果展示页面 | 成功展示分析结果，包括图表和数据表格 |

## 5. 测试执行计划

### 5.1 单元测试

1. 为每个核心模块创建单元测试脚本：
   - `tests/test_module_interface.py`
   - `tests/test_data_storage.py`
   - `tests/test_scheduler.py`
   - `tests/test_policy_analyzer.py`
   - `tests/test_news_monitor.py`
   - `tests/test_sentiment_resonance.py`
   - `tests/test_fund_flow_analyzer.py`
   - `tests/test_volatility_analyzer.py`

2. 使用pytest运行单元测试：
   ```bash
   pytest tests/
   ```

### 5.2 集成测试

1. 创建集成测试脚本：
   - `tests/integration/test_policy_volatility_integration.py`
   - `tests/integration/test_news_sentiment_integration.py`
   - `tests/integration/test_fund_flow_volatility_integration.py`
   - `tests/integration/test_system_integration.py`

2. 运行集成测试：
   ```bash
   pytest tests/integration/
   ```

### 5.3 系统测试

1. 创建系统测试脚本：
   - `tests/system/test_system_stability.py`
   - `tests/system/test_system_performance.py`
   - `tests/system/test_operation_panel.py`

2. 运行系统测试：
   ```bash
   pytest tests/system/
   ```

## 6. 测试数据准备

**真实数据获取与存储原则**：

1. **政策数据**：
   - 从国务院、发改委等官方网站获取真实政策数据
   - 建立政策数据库，存储不同时间、不同类型的政策数据
   - 确保数据包含足够的历史记录，用于测试时间衰减效应

2. **新闻数据**：
   - 通过AKShare API获取真实财经新闻数据
   - 从多个新闻源获取数据，确保数据多样性
   - 建立新闻数据库，用于测试去重和聚类功能

3. **北向南向资金数据**：
   - 使用AKShare的`stock_hsgt_fund_flow_summary_em`接口获取真实沪深港通资金流向数据
   - 建立资金流数据库，存储历史数据
   - 确保数据包含足够的历史记录，用于测试多周期信号生成

4. **市场数据**：
   - 获取真实的指数和个股历史价格数据
   - 计算并存储波动率历史数据
   - 确保数据覆盖不同市场状态（牛市、熊市、震荡市等）

## 7. 测试报告

测试完成后，将生成以下报告：

1. **测试覆盖率报告**：显示代码覆盖率情况
2. **测试结果报告**：显示通过/失败的测试用例
3. **性能测试报告**：显示系统在不同负载下的性能表现
4. **集成测试报告**：显示模块间数据流转的正确性
5. **系统操作面板测试报告**：显示操作面板功能的可用性

## 8. 缺陷跟踪

发现的缺陷将记录在缺陷跟踪系统中，包括以下信息：

1. 缺陷ID
2. 缺陷描述
3. 复现步骤
4. 严重程度
5. 优先级
6. 状态
7. 责任人

## 9. 测试完成标准

测试完成需满足以下条件：

1. 所有关键功能测试用例通过率达到95%以上
2. 所有高优先级缺陷已修复
3. 系统在模拟环境下稳定运行24小时无异常
4. 集成测试通过率达到90%以上
5. 系统操作面板所有功能正常工作
