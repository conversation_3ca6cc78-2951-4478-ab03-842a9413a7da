"""
数据同步服务

负责维护跨数据库的数据一致性
"""

import logging
import json
import os
from typing import Dict, Any, List, Optional, Union

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('data_sync_service')

class DataSyncService:
    """数据同步服务"""
    
    def __init__(self, data_access):
        """
        初始化数据同步服务
        
        Args:
            data_access: 统一数据访问接口
        """
        self.data_access = data_access
        self.sync_rules = self._load_sync_rules()
        logger.info("数据同步服务初始化完成")
    
    def _load_sync_rules(self) -> Dict[str, Dict[str, Any]]:
        """
        加载同步规则
        
        Returns:
            同步规则
        """
        # 尝试从配置文件加载
        config_path = os.path.join(os.path.dirname(__file__), 'config', 'sync_rules.json')
        if os.path.exists(config_path):
            try:
                with open(config_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                logger.error(f"加载同步规则配置文件失败: {str(e)}")
        
        # 返回默认同步规则
        return {
            # 股票基本信息同步规则
            'stocks': {
                'targets': [
                    {
                        'type': 'memory_db',
                        'data_type': 'stock_info',
                        'fields': ['code', 'name', 'industry_code', 'sector_code', 'is_st', 'is_suspended'],
                        'key_template': 'stock:info:{code}'
                    },
                    {
                        'type': 'metadata_index',
                        'data_type': 'metadata_index',
                        'fields': ['code', 'name', 'updated_at'],
                        'reference_template': 'stock:{code}'
                    }
                ]
            },
            # 政策解析结果同步规则
            'policy_analysis': {
                'targets': [
                    {
                        'type': 'memory_db',
                        'data_type': 'latest_policies',
                        'fields': ['policy_id', 'subject', 'action', 'object', 'sentiment_score', 'industry_impacts'],
                        'key_template': 'policy:latest'
                    },
                    {
                        'type': 'metadata_index',
                        'data_type': 'metadata_index',
                        'fields': ['policy_id', 'parsed_at', 'version'],
                        'reference_template': 'policy_analysis:{policy_id}'
                    }
                ]
            },
            # 新闻解析结果同步规则
            'news_analysis': {
                'targets': [
                    {
                        'type': 'memory_db',
                        'data_type': 'latest_news',
                        'fields': ['news_id', 'sentiment_score', 'importance_score', 'keywords'],
                        'key_template': 'news:latest'
                    },
                    {
                        'type': 'metadata_index',
                        'data_type': 'metadata_index',
                        'fields': ['news_id', 'processed_at'],
                        'reference_template': 'news_analysis:{news_id}'
                    }
                ]
            },
            # 热点话题同步规则
            'hot_topics': {
                'targets': [
                    {
                        'type': 'memory_db',
                        'data_type': 'hot_topics',
                        'fields': ['date', 'topics'],
                        'key_template': 'hot_topics:{date}'
                    }
                ]
            },
            # 北向资金数据同步规则
            'cross_border_flow': {
                'targets': [
                    {
                        'type': 'memory_db',
                        'data_type': 'calculation_results',
                        'fields': ['date', 'direction', 'net_flow', 'accumulated_flow'],
                        'key_template': 'calc:fund_flow:northbound:{date}'
                    }
                ]
            },
            # 波动率数据同步规则
            'volatility': {
                'targets': [
                    {
                        'type': 'memory_db',
                        'data_type': 'calculation_results',
                        'fields': ['date', 'code', 'type', 'value'],
                        'key_template': 'calc:volatility:{type}:{code}:{date}'
                    }
                ]
            }
        }
    
    def sync_data(self, data_type: str, data: Any, result: Any) -> None:
        """
        同步数据
        
        Args:
            data_type: 数据类型
            data: 原始数据
            result: 保存结果
        """
        if data_type not in self.sync_rules:
            return
        
        rule = self.sync_rules[data_type]
        for target in rule.get('targets', []):
            try:
                target_type = target.get('type')
                target_data_type = target.get('data_type')
                fields = target.get('fields', [])
                
                # 提取需要同步的字段
                sync_data = {}
                if isinstance(data, dict):
                    sync_data = {k: v for k, v in data.items() if k in fields}
                elif isinstance(data, list) and all(isinstance(item, dict) for item in data):
                    sync_data = [{k: v for k, v in item.items() if k in fields} for item in data]
                
                # 根据目标类型执行同步
                if target_type == 'memory_db' and self.data_access.memory_db:
                    # 生成键
                    key_template = target.get('key_template', '')
                    if key_template and isinstance(data, dict):
                        key = key_template.format(**data)
                        self.data_access.memory_db.set(key, sync_data, {'ttl': 3600})
                        logger.debug(f"同步数据到内存数据库: {key}")
                
                elif target_type == 'metadata_index' and self.data_access.relational_db:
                    # 生成引用ID
                    reference_template = target.get('reference_template', '')
                    if reference_template and isinstance(data, dict):
                        reference_id = reference_template.format(**data)
                        metadata = {
                            'data_type': data_type,
                            'reference_id': reference_id,
                            'storage_type': self._get_storage_type(data_type),
                            'storage_location': self._get_storage_location(data_type, data)
                        }
                        self.data_access.relational_db.save('metadata_index', metadata)
                        logger.debug(f"同步元数据索引: {reference_id}")
            
            except Exception as e:
                logger.error(f"同步数据失败: {data_type} -> {target_type}, {str(e)}")
    
    def _get_storage_type(self, data_type: str) -> str:
        """
        获取存储类型
        
        Args:
            data_type: 数据类型
        
        Returns:
            存储类型
        """
        from .data_types import (
            RELATIONAL_DATA_TYPES,
            TIME_SERIES_DATA_TYPES,
            DOCUMENT_DATA_TYPES,
            MEMORY_DATA_TYPES
        )
        
        if data_type in RELATIONAL_DATA_TYPES:
            return 'relational'
        elif data_type in TIME_SERIES_DATA_TYPES:
            return 'time_series'
        elif data_type in DOCUMENT_DATA_TYPES:
            return 'document'
        elif data_type in MEMORY_DATA_TYPES:
            return 'memory'
        else:
            return 'unknown'
    
    def _get_storage_location(self, data_type: str, data: Dict[str, Any]) -> str:
        """
        获取存储位置
        
        Args:
            data_type: 数据类型
            data: 数据
        
        Returns:
            存储位置
        """
        from .data_types import (
            RELATIONAL_DATA_TYPES,
            TIME_SERIES_DATA_TYPES,
            DOCUMENT_DATA_TYPES,
            MEMORY_DATA_TYPES
        )
        
        if data_type in RELATIONAL_DATA_TYPES:
            table = RELATIONAL_DATA_TYPES[data_type].get('table', '')
            primary_key = RELATIONAL_DATA_TYPES[data_type].get('primary_key', '')
            if primary_key in data:
                return f"{table}:{data[primary_key]}"
            return table
        
        elif data_type in TIME_SERIES_DATA_TYPES:
            measurement = TIME_SERIES_DATA_TYPES[data_type].get('measurement', '')
            tags = TIME_SERIES_DATA_TYPES[data_type].get('tags', [])
            tag_values = ':'.join(str(data.get(tag, '')) for tag in tags if tag in data)
            return f"{measurement}:{tag_values}"
        
        elif data_type in DOCUMENT_DATA_TYPES:
            collection = DOCUMENT_DATA_TYPES[data_type].get('collection', '')
            if '_id' in data:
                return f"{collection}:{data['_id']}"
            return collection
        
        elif data_type in MEMORY_DATA_TYPES:
            key_pattern = MEMORY_DATA_TYPES[data_type].get('key_pattern', '')
            try:
                return key_pattern.format(**data)
            except:
                return key_pattern
        
        return 'unknown'
