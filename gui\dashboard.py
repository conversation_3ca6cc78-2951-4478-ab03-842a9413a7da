"""
系统操作面板

提供图形化界面，用于操作和监控系统
"""

import os
import sys
import json
import logging
import threading
import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
from datetime import datetime
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('dashboard')

class Dashboard:
    """系统操作面板"""
    
    def __init__(self, system_manager):
        """
        初始化操作面板
        
        Args:
            system_manager: 系统管理器
        """
        self.system_manager = system_manager
        self.root = None
        self.notebook = None
        self.status_bar = None
        self.log_text = None
        self.update_thread = None
        self.running = False
        
        # 创建图形界面
        self._create_gui()
        
        logger.info("操作面板初始化完成")
    
    def _create_gui(self):
        """创建图形界面"""
        # 创建主窗口
        self.root = tk.Tk()
        self.root.title(f"{self.system_manager.config.get('system_name', '政策-流动性-波动率套利系统')} v{self.system_manager.config.get('version', '1.0.0')}")
        self.root.geometry("1200x800")
        self.root.protocol("WM_DELETE_WINDOW", self.stop)
        
        # 创建主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建顶部工具栏
        self._create_toolbar(main_frame)
        
        # 创建选项卡
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 创建仪表盘选项卡
        self._create_dashboard_tab()
        
        # 创建模块选项卡
        self._create_module_tabs()
        
        # 创建日志选项卡
        self._create_log_tab()
        
        # 创建状态栏
        self._create_status_bar(main_frame)
        
        logger.info("图形界面创建完成")
    
    def _create_toolbar(self, parent):
        """
        创建顶部工具栏
        
        Args:
            parent: 父容器
        """
        toolbar = ttk.Frame(parent)
        toolbar.pack(fill=tk.X, padx=5, pady=5)
        
        # 刷新按钮
        refresh_button = ttk.Button(toolbar, text="刷新数据", command=self._refresh_data)
        refresh_button.pack(side=tk.LEFT, padx=5)
        
        # 运行所有模块按钮
        run_all_button = ttk.Button(toolbar, text="运行所有模块", command=self._run_all_modules)
        run_all_button.pack(side=tk.LEFT, padx=5)
        
        # 停止所有任务按钮
        stop_all_button = ttk.Button(toolbar, text="停止所有任务", command=self._stop_all_tasks)
        stop_all_button.pack(side=tk.LEFT, padx=5)
        
        # 系统状态标签
        self.system_status_label = ttk.Label(toolbar, text="系统状态: 正常", foreground="green")
        self.system_status_label.pack(side=tk.RIGHT, padx=5)
    
    def _create_dashboard_tab(self):
        """创建仪表盘选项卡"""
        dashboard_frame = ttk.Frame(self.notebook)
        self.notebook.add(dashboard_frame, text="仪表盘")
        
        # 创建网格布局
        dashboard_frame.columnconfigure(0, weight=1)
        dashboard_frame.columnconfigure(1, weight=1)
        dashboard_frame.rowconfigure(0, weight=1)
        dashboard_frame.rowconfigure(1, weight=1)
        
        # 创建市场概览卡片
        market_frame = ttk.LabelFrame(dashboard_frame, text="市场概览")
        market_frame.grid(row=0, column=0, padx=5, pady=5, sticky="nsew")
        
        # 创建政策热点卡片
        policy_frame = ttk.LabelFrame(dashboard_frame, text="政策热点")
        policy_frame.grid(row=0, column=1, padx=5, pady=5, sticky="nsew")
        
        # 创建资金流向卡片
        fund_frame = ttk.LabelFrame(dashboard_frame, text="资金流向")
        fund_frame.grid(row=1, column=0, padx=5, pady=5, sticky="nsew")
        
        # 创建波动率监控卡片
        volatility_frame = ttk.LabelFrame(dashboard_frame, text="波动率监控")
        volatility_frame.grid(row=1, column=1, padx=5, pady=5, sticky="nsew")
        
        # 添加示例图表
        self._add_sample_chart(market_frame, "市场指数走势")
        self._add_sample_chart(fund_frame, "北向资金流向")
        self._add_sample_chart(volatility_frame, "波动率走势")
        
        # 添加示例列表
        self._add_sample_list(policy_frame, ["关于促进经济稳定增长的意见", "关于加强金融支持实体经济的通知", "关于推动制造业高质量发展的指导意见"])
    
    def _add_sample_chart(self, parent, title):
        """
        添加示例图表
        
        Args:
            parent: 父容器
            title: 图表标题
        """
        # 创建图表
        fig = plt.Figure(figsize=(5, 3), dpi=100)
        ax = fig.add_subplot(111)
        
        # 添加示例数据
        x = list(range(10))
        y = [i**2 for i in x]
        ax.plot(x, y)
        
        # 设置标题和标签
        ax.set_title(title)
        ax.set_xlabel("日期")
        ax.set_ylabel("数值")
        
        # 创建画布
        canvas = FigureCanvasTkAgg(fig, parent)
        canvas.draw()
        canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
    
    def _add_sample_list(self, parent, items):
        """
        添加示例列表
        
        Args:
            parent: 父容器
            items: 列表项
        """
        # 创建列表框
        listbox = tk.Listbox(parent)
        listbox.pack(fill=tk.BOTH, expand=True)
        
        # 添加列表项
        for item in items:
            listbox.insert(tk.END, item)
    
    def _create_module_tabs(self):
        """创建模块选项卡"""
        # 政策分析选项卡
        if "policy_analyzer" in self.system_manager.modules:
            policy_frame = ttk.Frame(self.notebook)
            self.notebook.add(policy_frame, text="政策分析")
            self._create_module_tab(policy_frame, "policy_analyzer")
        
        # 新闻监控选项卡
        if "news_monitor" in self.system_manager.modules:
            news_frame = ttk.Frame(self.notebook)
            self.notebook.add(news_frame, text="新闻监控")
            self._create_module_tab(news_frame, "news_monitor")
        
        # 资金流分析选项卡
        if "fund_flow_analyzer" in self.system_manager.modules:
            fund_frame = ttk.Frame(self.notebook)
            self.notebook.add(fund_frame, text="资金流分析")
            self._create_module_tab(fund_frame, "fund_flow_analyzer")
        
        # 波动率分析选项卡
        if "volatility_analyzer" in self.system_manager.modules:
            volatility_frame = ttk.Frame(self.notebook)
            self.notebook.add(volatility_frame, text="波动率分析")
            self._create_module_tab(volatility_frame, "volatility_analyzer")
        
        # 情绪共振选项卡
        if "sentiment_resonance" in self.system_manager.modules:
            sentiment_frame = ttk.Frame(self.notebook)
            self.notebook.add(sentiment_frame, text="情绪共振")
            self._create_module_tab(sentiment_frame, "sentiment_resonance")
        
        # 套利检测选项卡
        if "arbitrage_detector" in self.system_manager.modules:
            arbitrage_frame = ttk.Frame(self.notebook)
            self.notebook.add(arbitrage_frame, text="套利检测")
            self._create_module_tab(arbitrage_frame, "arbitrage_detector")
    
    def _create_module_tab(self, parent, module_name):
        """
        创建模块选项卡
        
        Args:
            parent: 父容器
            module_name: 模块名称
        """
        # 创建工具栏
        toolbar = ttk.Frame(parent)
        toolbar.pack(fill=tk.X, padx=5, pady=5)
        
        # 运行模块按钮
        run_button = ttk.Button(toolbar, text="运行模块", command=lambda: self._run_module(module_name))
        run_button.pack(side=tk.LEFT, padx=5)
        
        # 刷新数据按钮
        refresh_button = ttk.Button(toolbar, text="刷新数据", command=lambda: self._refresh_module_data(module_name))
        refresh_button.pack(side=tk.LEFT, padx=5)
        
        # 模块状态标签
        status_label = ttk.Label(toolbar, text="状态: 就绪")
        status_label.pack(side=tk.RIGHT, padx=5)
        
        # 创建内容区域
        content_frame = ttk.Frame(parent)
        content_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 创建示例内容（根据模块类型添加不同内容）
        if module_name == "policy_analyzer":
            self._create_policy_analyzer_content(content_frame)
        elif module_name == "news_monitor":
            self._create_news_monitor_content(content_frame)
        elif module_name == "fund_flow_analyzer":
            self._create_fund_flow_analyzer_content(content_frame)
        elif module_name == "volatility_analyzer":
            self._create_volatility_analyzer_content(content_frame)
        elif module_name == "sentiment_resonance":
            self._create_sentiment_resonance_content(content_frame)
        elif module_name == "arbitrage_detector":
            self._create_arbitrage_detector_content(content_frame)
    
    def _create_policy_analyzer_content(self, parent):
        """
        创建政策分析模块内容
        
        Args:
            parent: 父容器
        """
        # 创建分割窗口
        paned = ttk.PanedWindow(parent, orient=tk.HORIZONTAL)
        paned.pack(fill=tk.BOTH, expand=True)
        
        # 创建政策列表框架
        list_frame = ttk.Frame(paned)
        paned.add(list_frame, weight=1)
        
        # 创建政策列表
        ttk.Label(list_frame, text="政策列表").pack(anchor=tk.W)
        policy_list = ttk.Treeview(list_frame, columns=("date", "source"), show="headings")
        policy_list.heading("date", text="日期")
        policy_list.heading("source", text="来源")
        policy_list.pack(fill=tk.BOTH, expand=True)
        
        # 添加示例数据
        policy_list.insert("", tk.END, values=("2025-05-15", "国务院"))
        policy_list.insert("", tk.END, values=("2025-05-14", "央行"))
        policy_list.insert("", tk.END, values=("2025-05-13", "发改委"))
        
        # 创建政策详情框架
        detail_frame = ttk.Frame(paned)
        paned.add(detail_frame, weight=2)
        
        # 创建政策详情
        ttk.Label(detail_frame, text="政策详情").pack(anchor=tk.W)
        policy_detail = scrolledtext.ScrolledText(detail_frame)
        policy_detail.pack(fill=tk.BOTH, expand=True)
        policy_detail.insert(tk.END, "这里显示政策详情...")
    
    def _create_news_monitor_content(self, parent):
        """
        创建新闻监控模块内容
        
        Args:
            parent: 父容器
        """
        # 创建分割窗口
        paned = ttk.PanedWindow(parent, orient=tk.HORIZONTAL)
        paned.pack(fill=tk.BOTH, expand=True)
        
        # 创建新闻列表框架
        list_frame = ttk.Frame(paned)
        paned.add(list_frame, weight=1)
        
        # 创建新闻列表
        ttk.Label(list_frame, text="新闻列表").pack(anchor=tk.W)
        news_list = ttk.Treeview(list_frame, columns=("date", "source"), show="headings")
        news_list.heading("date", text="日期")
        news_list.heading("source", text="来源")
        news_list.pack(fill=tk.BOTH, expand=True)
        
        # 添加示例数据
        news_list.insert("", tk.END, values=("2025-05-15", "东方财富"))
        news_list.insert("", tk.END, values=("2025-05-15", "新浪财经"))
        news_list.insert("", tk.END, values=("2025-05-15", "财联社"))
        
        # 创建新闻详情框架
        detail_frame = ttk.Frame(paned)
        paned.add(detail_frame, weight=2)
        
        # 创建新闻详情
        ttk.Label(detail_frame, text="新闻详情").pack(anchor=tk.W)
        news_detail = scrolledtext.ScrolledText(detail_frame)
        news_detail.pack(fill=tk.BOTH, expand=True)
        news_detail.insert(tk.END, "这里显示新闻详情...")
    
    def _create_fund_flow_analyzer_content(self, parent):
        """
        创建资金流分析模块内容
        
        Args:
            parent: 父容器
        """
        # 创建选项卡
        notebook = ttk.Notebook(parent)
        notebook.pack(fill=tk.BOTH, expand=True)
        
        # 创建北向南向资金选项卡
        north_frame = ttk.Frame(notebook)
        notebook.add(north_frame, text="北向南向资金")
        
        # 添加示例图表
        self._add_sample_chart(north_frame, "北向资金流向")
        
        # 创建行业资金流选项卡
        sector_frame = ttk.Frame(notebook)
        notebook.add(sector_frame, text="行业资金流")
        
        # 添加示例图表
        self._add_sample_chart(sector_frame, "行业资金流向")
        
        # 创建个股资金流选项卡
        stock_frame = ttk.Frame(notebook)
        notebook.add(stock_frame, text="个股资金流")
        
        # 添加示例图表
        self._add_sample_chart(stock_frame, "个股资金流向")
    
    def _create_volatility_analyzer_content(self, parent):
        """
        创建波动率分析模块内容
        
        Args:
            parent: 父容器
        """
        # 创建选项卡
        notebook = ttk.Notebook(parent)
        notebook.pack(fill=tk.BOTH, expand=True)
        
        # 创建市场波动率选项卡
        market_frame = ttk.Frame(notebook)
        notebook.add(market_frame, text="市场波动率")
        
        # 添加示例图表
        self._add_sample_chart(market_frame, "市场波动率")
        
        # 创建行业波动率选项卡
        sector_frame = ttk.Frame(notebook)
        notebook.add(sector_frame, text="行业波动率")
        
        # 添加示例图表
        self._add_sample_chart(sector_frame, "行业波动率")
        
        # 创建个股波动率选项卡
        stock_frame = ttk.Frame(notebook)
        notebook.add(stock_frame, text="个股波动率")
        
        # 添加示例图表
        self._add_sample_chart(stock_frame, "个股波动率")
    
    def _create_sentiment_resonance_content(self, parent):
        """
        创建情绪共振模块内容
        
        Args:
            parent: 父容器
        """
        # 创建选项卡
        notebook = ttk.Notebook(parent)
        notebook.pack(fill=tk.BOTH, expand=True)
        
        # 创建政策情绪选项卡
        policy_frame = ttk.Frame(notebook)
        notebook.add(policy_frame, text="政策情绪")
        
        # 添加示例图表
        self._add_sample_chart(policy_frame, "政策情绪趋势")
        
        # 创建新闻情绪选项卡
        news_frame = ttk.Frame(notebook)
        notebook.add(news_frame, text="新闻情绪")
        
        # 添加示例图表
        self._add_sample_chart(news_frame, "新闻情绪趋势")
        
        # 创建情绪共振选项卡
        resonance_frame = ttk.Frame(notebook)
        notebook.add(resonance_frame, text="情绪共振")
        
        # 添加示例图表
        self._add_sample_chart(resonance_frame, "情绪共振分析")
    
    def _create_arbitrage_detector_content(self, parent):
        """
        创建套利检测模块内容
        
        Args:
            parent: 父容器
        """
        # 创建套利机会列表
        ttk.Label(parent, text="套利机会列表").pack(anchor=tk.W)
        
        # 创建表格
        columns = ("code", "name", "score", "policy", "fund", "volatility")
        tree = ttk.Treeview(parent, columns=columns, show="headings")
        
        # 设置列标题
        tree.heading("code", text="代码")
        tree.heading("name", text="名称")
        tree.heading("score", text="综合得分")
        tree.heading("policy", text="政策得分")
        tree.heading("fund", text="资金得分")
        tree.heading("volatility", text="波动率得分")
        
        # 设置列宽
        for col in columns:
            tree.column(col, width=100)
        
        tree.pack(fill=tk.BOTH, expand=True)
        
        # 添加示例数据
        tree.insert("", tk.END, values=("000001", "平安银行", "0.85", "0.9", "0.8", "0.85"))
        tree.insert("", tk.END, values=("600000", "浦发银行", "0.82", "0.85", "0.8", "0.8"))
        tree.insert("", tk.END, values=("600036", "招商银行", "0.8", "0.85", "0.75", "0.8"))
    
    def _create_log_tab(self):
        """创建日志选项卡"""
        log_frame = ttk.Frame(self.notebook)
        self.notebook.add(log_frame, text="系统日志")
        
        # 创建日志文本框
        self.log_text = scrolledtext.ScrolledText(log_frame)
        self.log_text.pack(fill=tk.BOTH, expand=True)
        
        # 添加示例日志
        self.log_text.insert(tk.END, f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 系统启动\n")
        self.log_text.insert(tk.END, f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 加载配置文件\n")
        self.log_text.insert(tk.END, f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 初始化模块\n")
        self.log_text.insert(tk.END, f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 系统就绪\n")
    
    def _create_status_bar(self, parent):
        """
        创建状态栏
        
        Args:
            parent: 父容器
        """
        self.status_bar = ttk.Frame(parent)
        self.status_bar.pack(fill=tk.X, side=tk.BOTTOM, padx=5, pady=5)
        
        # 数据更新时间
        self.data_update_label = ttk.Label(self.status_bar, text="数据更新: 未更新")
        self.data_update_label.pack(side=tk.LEFT, padx=5)
        
        # CPU使用率
        self.cpu_label = ttk.Label(self.status_bar, text="CPU: 0%")
        self.cpu_label.pack(side=tk.LEFT, padx=5)
        
        # 内存使用率
        self.memory_label = ttk.Label(self.status_bar, text="内存: 0%")
        self.memory_label.pack(side=tk.LEFT, padx=5)
        
        # 任务队列
        self.task_label = ttk.Label(self.status_bar, text="任务: 0/0")
        self.task_label.pack(side=tk.LEFT, padx=5)
        
        # 数据库连接状态
        self.db_label = ttk.Label(self.status_bar, text="DB: 未连接")
        self.db_label.pack(side=tk.LEFT, padx=5)
    
    def _refresh_data(self):
        """刷新数据"""
        logger.info("刷新数据")
        messagebox.showinfo("刷新数据", "数据刷新已启动")
        
        # 更新状态栏
        self.data_update_label.config(text=f"数据更新: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    def _run_all_modules(self):
        """运行所有模块"""
        logger.info("运行所有模块")
        messagebox.showinfo("运行所有模块", "所有模块已启动")
    
    def _stop_all_tasks(self):
        """停止所有任务"""
        logger.info("停止所有任务")
        messagebox.showinfo("停止所有任务", "所有任务已停止")
    
    def _run_module(self, module_name):
        """
        运行模块
        
        Args:
            module_name: 模块名称
        """
        logger.info(f"运行模块: {module_name}")
        messagebox.showinfo("运行模块", f"模块 {module_name} 已启动")
    
    def _refresh_module_data(self, module_name):
        """
        刷新模块数据
        
        Args:
            module_name: 模块名称
        """
        logger.info(f"刷新模块数据: {module_name}")
        messagebox.showinfo("刷新模块数据", f"模块 {module_name} 数据刷新已启动")
    
    def _update_status(self):
        """更新状态信息"""
        while self.running:
            try:
                # 获取系统状态
                status = self.system_manager.get_status()
                
                # 更新CPU和内存使用率（示例数据）
                import random
                cpu_usage = random.randint(10, 50)
                memory_usage = random.randint(20, 60)
                
                # 更新状态栏
                self.cpu_label.config(text=f"CPU: {cpu_usage}%")
                self.memory_label.config(text=f"内存: {memory_usage}%")
                
                # 更新任务队列
                task_count = status.get('scheduler', {}).get('tasks', 0)
                self.task_label.config(text=f"任务: {task_count}/10")
                
                # 更新数据库连接状态
                if self.system_manager.data_access and self.system_manager.data_access.is_connected():
                    self.db_label.config(text="DB: 已连接")
                else:
                    self.db_label.config(text="DB: 未连接")
                
                # 更新日志
                self.log_text.insert(tk.END, f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 系统状态更新\n")
                self.log_text.see(tk.END)
                
                # 每5秒更新一次
                import time
                time.sleep(5)
            
            except Exception as e:
                logger.error(f"更新状态信息失败: {str(e)}")
                import time
                time.sleep(5)
    
    def start(self):
        """启动操作面板"""
        self.running = True
        
        # 启动状态更新线程
        self.update_thread = threading.Thread(target=self._update_status)
        self.update_thread.daemon = True
        self.update_thread.start()
        
        # 启动主循环
        self.root.mainloop()
    
    def stop(self):
        """停止操作面板"""
        if messagebox.askokcancel("退出", "确定要退出系统吗？"):
            self.running = False
            
            # 停止系统
            self.system_manager.stop()
            
            # 销毁窗口
            self.root.destroy()

# 测试代码
if __name__ == "__main__":
    # 创建模拟系统管理器
    class MockSystemManager:
        def __init__(self):
            self.config = {
                "system_name": "政策-流动性-波动率套利系统",
                "version": "1.0.0"
            }
            self.modules = {
                "policy_analyzer": None,
                "news_monitor": None,
                "fund_flow_analyzer": None,
                "volatility_analyzer": None
            }
        
        def get_status(self):
            return {
                "scheduler": {
                    "tasks": 5
                }
            }
        
        def stop(self):
            print("系统停止")
        
        def data_access(self):
            return None
    
    # 创建操作面板
    dashboard = Dashboard(MockSystemManager())
    dashboard.start()
