"""
政策-流动性分层-波动率套利系统全天候监控框架

负责启动和管理全天候监控系统
"""

import os
import sys
import time
import logging
import signal
import argparse
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/monitor_system.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('monitor_system')

# 导入中央调度器
from core.scheduler import CentralScheduler, TaskPriority

# 导入模块
from data_sources.unified_news_monitor import UnifiedNewsMonitor
from data_sources.policy_analyzer import PolicyAnalyzer
from data_sources.sentiment_resonance import SentimentResonance
from data_sources.news_clustering import NewsClusteringModule
from core.data_storage import DataStorage

class MonitorSystem:
    """全天候监控系统类"""

    def __init__(self):
        """初始化监控系统"""
        # 创建必要的目录
        os.makedirs('logs', exist_ok=True)
        os.makedirs('data', exist_ok=True)
        os.makedirs('config', exist_ok=True)

        # 初始化中央调度器
        self.scheduler = CentralScheduler()

        # 初始化模块
        self.modules = {}

        # 初始化数据存储
        self.data_storage = DataStorage()

        logger.info("监控系统初始化完成")

    def register_module(self, module_name, module_instance):
        """
        注册模块

        Args:
            module_name: 模块名称
            module_instance: 模块实例
        """
        # 注册到模块字典
        self.modules[module_name] = module_instance

        # 设置模块的调度器
        module_instance.set_scheduler(self.scheduler)

        # 注册到调度器
        self.scheduler.register_module(module_name, module_instance)

        logger.info(f"模块注册成功: {module_name}")

    def initialize_modules(self):
        """初始化所有模块"""
        logger.info("开始初始化所有模块...")

        for module_name, module in self.modules.items():
            try:
                module.initialize()
                logger.info(f"模块初始化成功: {module_name}")
            except Exception as e:
                logger.error(f"模块初始化失败: {module_name}, 错误: {str(e)}")

        logger.info("所有模块初始化完成")

    def start(self):
        """启动系统"""
        logger.info("开始启动监控系统...")

        # 启动调度器
        self.scheduler.start()

        # 初始化模块
        self.initialize_modules()

        logger.info("监控系统启动成功")

    def stop(self):
        """停止系统"""
        logger.info("开始停止监控系统...")

        # 停止调度器
        self.scheduler.stop()

        logger.info("监控系统停止成功")

    def health_check(self):
        """系统健康检查"""
        logger.info("开始系统健康检查...")

        health_status = {
            'scheduler': {
                'is_running': self.scheduler.is_running,
                'task_count': len(self.scheduler.tasks),
                'queue_size': self.scheduler.task_queue.qsize(),
                'worker_count': len(self.scheduler.workers)
            },
            'modules': {}
        }

        # 检查各模块健康状态
        for module_name, module in self.modules.items():
            try:
                module_health = module.health_check()
                health_status['modules'][module_name] = module_health
            except Exception as e:
                logger.error(f"模块健康检查失败: {module_name}, 错误: {str(e)}")
                health_status['modules'][module_name] = {
                    'status': 'error',
                    'message': f'健康检查失败: {str(e)}',
                    'error': str(e)
                }

        # 判断整体健康状态
        if not self.scheduler.is_running:
            health_status['status'] = 'critical'
            health_status['message'] = '调度器未运行'
        elif any(m.get('status') == 'error' for m in health_status['modules'].values()):
            health_status['status'] = 'error'
            health_status['message'] = '一个或多个模块出错'
        elif any(m.get('status') == 'warning' for m in health_status['modules'].values()):
            health_status['status'] = 'warning'
            health_status['message'] = '一个或多个模块有警告'
        else:
            health_status['status'] = 'healthy'
            health_status['message'] = '系统运行正常'

        logger.info(f"系统健康检查完成: {health_status['status']}")

        return health_status

    def run_command(self, command, **kwargs):
        """
        运行命令

        Args:
            command: 命令名称
            **kwargs: 命令参数
        """
        if command == 'start':
            self.start()
        elif command == 'stop':
            self.stop()
        elif command == 'restart':
            self.stop()
            time.sleep(1)
            self.start()
        elif command == 'status':
            return self.health_check()
        elif command == 'module_status':
            module_name = kwargs.get('module_name')
            if module_name in self.modules:
                return self.modules[module_name].get_status()
            else:
                logger.error(f"模块不存在: {module_name}")
                return {'status': 'error', 'message': f'模块不存在: {module_name}'}
        else:
            logger.error(f"未知命令: {command}")
            return {'status': 'error', 'message': f'未知命令: {command}'}

def signal_handler(sig, frame):
    """信号处理函数"""
    logger.info(f"接收到信号: {sig}")
    if 'monitor' in globals():
        monitor.stop()
    sys.exit(0)

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='政策-流动性分层-波动率套利系统全天候监控框架')
    parser.add_argument('command', choices=['start', 'stop', 'restart', 'status'], help='要执行的命令')
    parser.add_argument('--module', help='指定模块')
    return parser.parse_args()

def main():
    """主函数"""
    # 解析命令行参数
    args = parse_args()

    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    # 创建监控系统
    global monitor
    monitor = MonitorSystem()

    # 注册模块
    monitor.register_module('unified_news_monitor', UnifiedNewsMonitor())
    monitor.register_module('policy_analyzer', PolicyAnalyzer())
    monitor.register_module('sentiment_resonance', SentimentResonance())
    monitor.register_module('news_clustering', NewsClusteringModule())

    # 未来可以在这里添加更多模块
    logger.info("已注册所有模块")

    # 运行命令
    result = monitor.run_command(args.command, module_name=args.module)

    # 如果有结果，打印出来
    if result:
        import json
        print(json.dumps(result, indent=4))

    # 如果是启动命令，保持程序运行
    if args.command == 'start':
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            monitor.stop()

if __name__ == '__main__':
    main()
