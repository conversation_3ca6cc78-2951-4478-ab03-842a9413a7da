"""
政策-流动性分层-波动率套利系统全天候监控框架

负责启动和管理全天候监控系统
"""

import os
import sys
import time
import logging
import signal
import argparse
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/monitor_system.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('monitor_system')

# 导入中央调度器
from core.scheduler import CentralScheduler, TaskPriority, Task

# 导入模块
from engines.news_policy.fetcher import NewsPolicyFetcher
from engines.news_policy.analyzer import PolicyAnalyzer
from engines.sentiment.analyzer import SentimentAnalyzer
from engines.tiered_fund_flow.analyzer import TieredFundFlowAnalyzer
from engines.volatility.analyzer import VolatilityAnalyzer
from core.data_storage import DataStorage
from core.anomaly_detector import AnomalyDetector
from core.realtime_alert_system import RealTimeAlertSystem
from core.unified_data_collector import UnifiedDataCollector

class MonitorSystem:
    """全天候监控系统类"""

    def __init__(self):
        """初始化监控系统"""
        # 创建必要的目录
        os.makedirs('logs', exist_ok=True)
        os.makedirs('data', exist_ok=True)
        os.makedirs('config', exist_ok=True)
        os.makedirs('alerts', exist_ok=True)

        # 初始化中央调度器
        self.scheduler = CentralScheduler()

        # 初始化模块
        self.modules = {}

        # 初始化数据存储
        self.data_storage = DataStorage()

        # 初始化异动检测器
        self.anomaly_detector = AnomalyDetector()

        # 初始化实时提示系统
        self.alert_system = RealTimeAlertSystem()

        # 初始化统一数据收集器
        self.data_collector = UnifiedDataCollector()

        logger.info("监控系统初始化完成")

    def register_module(self, module_name, module_instance):
        """
        注册模块

        Args:
            module_name: 模块名称
            module_instance: 模块实例
        """
        # 注册到模块字典
        self.modules[module_name] = module_instance

        # 设置模块的调度器（如果模块支持）
        if hasattr(module_instance, 'set_scheduler'):
            module_instance.set_scheduler(self.scheduler)

        # 注册到调度器（如果调度器支持）
        if hasattr(self.scheduler, 'register_module'):
            self.scheduler.register_module(module_name, module_instance)

        logger.info(f"模块注册成功: {module_name}")

    def initialize_modules(self):
        """初始化所有模块"""
        logger.info("开始初始化所有模块...")

        for module_name, module in self.modules.items():
            try:
                # 只有模块有initialize方法时才调用
                if hasattr(module, 'initialize'):
                    module.initialize()
                    logger.info(f"模块初始化成功: {module_name}")
                else:
                    logger.info(f"模块无需初始化: {module_name}")
            except Exception as e:
                logger.error(f"模块初始化失败: {module_name}, 错误: {str(e)}")

        logger.info("所有模块初始化完成")

    def start(self):
        """启动系统"""
        logger.info("开始启动监控系统...")

        # 启动调度器
        self.scheduler.start()

        # 注册监控系统自身为模块
        self.scheduler.register_module('monitor_system', self)

        # 初始化模块
        self.initialize_modules()

        # 添加数据收集任务
        data_collection_task = Task(
            task_id='data_collection_' + datetime.now().strftime('%Y%m%d_%H%M%S'),
            task_type='data_collection',
            module='monitor_system',
            function='run_data_collection',
            priority=TaskPriority.MEDIUM
        )
        self.scheduler.add_task(data_collection_task)

        # 添加异动检测任务
        anomaly_detection_task = Task(
            task_id='anomaly_detection_' + datetime.now().strftime('%Y%m%d_%H%M%S'),
            task_type='anomaly_detection',
            module='monitor_system',
            function='run_anomaly_detection',
            priority=TaskPriority.HIGH
        )
        self.scheduler.add_task(anomaly_detection_task)

        logger.info("监控系统启动成功")

    def stop(self):
        """停止系统"""
        logger.info("开始停止监控系统...")

        # 停止调度器
        self.scheduler.stop()

        logger.info("监控系统停止成功")

    def health_check(self):
        """系统健康检查"""
        logger.info("开始系统健康检查...")

        health_status = {
            'scheduler': {
                'is_running': self.scheduler.is_running,
                'task_count': len(self.scheduler.tasks),
                'queue_size': self.scheduler.task_queue.qsize(),
                'worker_count': len(self.scheduler.workers)
            },
            'modules': {}
        }

        # 检查各模块健康状态
        for module_name, module in self.modules.items():
            try:
                module_health = module.health_check()
                health_status['modules'][module_name] = module_health
            except Exception as e:
                logger.error(f"模块健康检查失败: {module_name}, 错误: {str(e)}")
                health_status['modules'][module_name] = {
                    'status': 'error',
                    'message': f'健康检查失败: {str(e)}',
                    'error': str(e)
                }

        # 判断整体健康状态
        if not self.scheduler.is_running:
            health_status['status'] = 'critical'
            health_status['message'] = '调度器未运行'
        elif any(m.get('status') == 'error' for m in health_status['modules'].values()):
            health_status['status'] = 'error'
            health_status['message'] = '一个或多个模块出错'
        elif any(m.get('status') == 'warning' for m in health_status['modules'].values()):
            health_status['status'] = 'warning'
            health_status['message'] = '一个或多个模块有警告'
        else:
            health_status['status'] = 'healthy'
            health_status['message'] = '系统运行正常'

        logger.info(f"系统健康检查完成: {health_status['status']}")

        return health_status

    def run_data_collection(self):
        """运行数据收集"""
        try:
            logger.info("开始数据收集任务...")

            # 运行数据收集
            success = self.data_collector.run_collection_sync()

            if success:
                logger.info("数据收集任务完成")
            else:
                logger.warning("数据收集任务部分失败")

            # 获取收集状态
            status = self.data_collector.get_data_collection_status()
            logger.info(f"数据收集状态: {status.get('collection_summary', {})}")

        except Exception as e:
            logger.error(f"数据收集任务失败: {str(e)}")

    def run_anomaly_detection(self):
        """运行异动检测"""
        try:
            logger.info("开始异动检测...")

            # 获取最新数据
            latest_data = self._get_latest_data()

            # 检测政策异动
            if 'policy_data' in latest_data:
                policy_anomaly = self.anomaly_detector.detect_policy_anomaly(latest_data['policy_data'])
                if policy_anomaly.get('has_anomaly'):
                    self.alert_system.send_policy_alert(policy_anomaly)

            # 检测新闻异动
            if 'news_data' in latest_data:
                news_anomaly = self.anomaly_detector.detect_news_anomaly(latest_data['news_data'])
                if news_anomaly.get('has_anomaly'):
                    self.alert_system.send_market_alert(news_anomaly)

            # 检测资金流异动
            if 'fund_flow_data' in latest_data:
                fund_flow_anomaly = self.anomaly_detector.detect_fund_flow_anomaly(latest_data['fund_flow_data'])
                if fund_flow_anomaly.get('has_anomaly'):
                    self.alert_system.send_market_alert(fund_flow_anomaly)

            # 检测波动率异动
            if 'volatility_data' in latest_data:
                volatility_anomaly = self.anomaly_detector.detect_volatility_anomaly(latest_data['volatility_data'])
                if volatility_anomaly.get('has_anomaly'):
                    self.alert_system.send_market_alert(volatility_anomaly)

            logger.info("异动检测完成")

        except Exception as e:
            logger.error(f"异动检测失败: {str(e)}")

    def _get_latest_data(self):
        """获取最新数据"""
        try:
            # 从数据存储中获取最新数据
            latest_data = {}

            # 获取最新政策数据
            try:
                policy_data = self.data_storage.get_latest_data('policy', limit=1)
                if policy_data:
                    latest_data['policy_data'] = policy_data[0]
            except Exception as e:
                logger.warning(f"获取政策数据失败: {str(e)}")

            # 获取最新新闻数据
            try:
                news_data = self.data_storage.get_latest_data('news', limit=10)
                if news_data:
                    # 计算综合情绪分数
                    sentiment_scores = [item.get('sentiment_score', 0) for item in news_data]
                    avg_sentiment = sum(sentiment_scores) / len(sentiment_scores) if sentiment_scores else 0
                    latest_data['news_data'] = {
                        'sentiment_score': avg_sentiment,
                        'news_count': len(news_data),
                        'latest_news': news_data[0] if news_data else None
                    }
            except Exception as e:
                logger.warning(f"获取新闻数据失败: {str(e)}")

            # 获取最新资金流数据
            try:
                fund_flow_data = self.data_storage.get_latest_data('fund_flow', limit=1)
                if fund_flow_data:
                    latest_data['fund_flow_data'] = fund_flow_data[0]
            except Exception as e:
                logger.warning(f"获取资金流数据失败: {str(e)}")

            # 获取最新波动率数据
            try:
                volatility_data = self.data_storage.get_latest_data('volatility', limit=1)
                if volatility_data:
                    latest_data['volatility_data'] = volatility_data[0]
            except Exception as e:
                logger.warning(f"获取波动率数据失败: {str(e)}")

            return latest_data

        except Exception as e:
            logger.error(f"获取最新数据失败: {str(e)}")
            return {}

    def run_command(self, command, **kwargs):
        """
        运行命令

        Args:
            command: 命令名称
            **kwargs: 命令参数
        """
        if command == 'start':
            self.start()
        elif command == 'stop':
            self.stop()
        elif command == 'restart':
            self.stop()
            time.sleep(1)
            self.start()
        elif command == 'status':
            return self.health_check()
        elif command == 'module_status':
            module_name = kwargs.get('module_name')
            if module_name in self.modules:
                return self.modules[module_name].get_status()
            else:
                logger.error(f"模块不存在: {module_name}")
                return {'status': 'error', 'message': f'模块不存在: {module_name}'}
        else:
            logger.error(f"未知命令: {command}")
            return {'status': 'error', 'message': f'未知命令: {command}'}

def signal_handler(sig, frame):
    """信号处理函数"""
    logger.info(f"接收到信号: {sig}")
    if 'monitor' in globals():
        monitor.stop()
    sys.exit(0)

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='政策-流动性分层-波动率套利系统全天候监控框架')
    parser.add_argument('command', choices=['start', 'stop', 'restart', 'status'], help='要执行的命令')
    parser.add_argument('--module', help='指定模块')
    return parser.parse_args()

def main():
    """主函数"""
    # 解析命令行参数
    args = parse_args()

    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    # 创建监控系统
    global monitor
    monitor = MonitorSystem()

    # 注册模块
    monitor.register_module('news_policy_fetcher', NewsPolicyFetcher())
    monitor.register_module('news_policy_analyzer', PolicyAnalyzer())
    monitor.register_module('sentiment_analyzer', SentimentAnalyzer())
    monitor.register_module('fund_flow_analyzer', TieredFundFlowAnalyzer())
    monitor.register_module('volatility_analyzer', VolatilityAnalyzer())

    # 未来可以在这里添加更多模块
    logger.info("已注册所有模块")

    # 运行命令
    result = monitor.run_command(args.command, module_name=args.module)

    # 如果有结果，打印出来
    if result:
        import json
        print(json.dumps(result, indent=4))

    # 如果是启动命令，保持程序运行
    if args.command == 'start':
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            monitor.stop()

if __name__ == '__main__':
    main()
