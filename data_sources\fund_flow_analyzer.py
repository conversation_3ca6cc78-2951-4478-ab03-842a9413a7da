"""
资金流分析模块

提供北向资金合成信号、游资行为模式识别和五级分层资金流分析功能
"""

import os
import logging
import json
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import time
import re
from typing import Dict, List, Any, Optional, Union, Tuple
import math
from collections import defaultdict
import akshare as ak

# 导入模块接口
from core.module_interface import ModuleInterface

# 导入数据存储
from core.data_storage import DataStorage, StorageLevel

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/fund_flow_analyzer.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('fund_flow_analyzer')

class FundFlowAnalyzer(ModuleInterface):
    """资金流分析模块类"""

    def __init__(self):
        """初始化资金流分析模块"""
        super().__init__(module_name='fund_flow_analyzer')

        # 创建数据存储
        self.data_storage = DataStorage()

        # 上次更新时间
        self.last_update_time = datetime.now()

        # 北向南向资金数据
        self.cross_border_flow_data = self.data_storage.load('fund_flow_analyzer', 'cross_border_flow_data', {
            'northbound': {
                'daily': [],
                'signals': [],
                'cumulative': {}
            },
            'southbound': {
                'daily': [],
                'signals': [],
                'cumulative': {}
            },
            'last_update': None
        })

        # 游资行为数据
        self.hot_money_data = self.data_storage.load('fund_flow_analyzer', 'hot_money_data', {
            'patterns': [],
            'targets': [],
            'history': []
        })

        # 五级资金流数据
        self.tiered_flow_data = self.data_storage.load('fund_flow_analyzer', 'tiered_flow_data', {
            'market': {},
            'sector': {},
            'stock': {},
            'signals': []
        })

        # 股票代码映射
        self.stock_code_map = self._load_stock_code_map()

        # 行业板块映射
        self.sector_map = self._load_sector_map()

        # 游资识别参数
        self.hot_money_params = {
            'turnover_threshold': 15,  # 换手率阈值(%)
            'volume_increase_threshold': 2.0,  # 成交量增幅阈值
            'price_volatility_threshold': 5.0,  # 价格波动阈值(%)
            'concentration_threshold': 0.6,  # 集中度阈值
            'pattern_recognition_window': 20  # 模式识别窗口(天)
        }

        # 北向资金信号参数
        self.northbound_params = {
            'short_window': 5,  # 短期窗口(天)
            'medium_window': 10,  # 中期窗口(天)
            'long_window': 20,  # 长期窗口(天)
            'flow_threshold': 5000,  # 资金流阈值(百万元)
            'reversal_threshold': 0.5,  # 反转阈值
            'persistence_threshold': 3  # 持续性阈值(天)
        }

        # 五级资金流参数
        self.tiered_flow_params = {
            'super_large_threshold': 5000000,  # 超大单阈值(元)
            'large_threshold': 1000000,  # 大单阈值(元)
            'medium_threshold': 300000,  # 中单阈值(元)
            'small_threshold': 40000,  # 小单阈值(元)
            'flow_ratio_threshold': 0.7,  # 流入流出比例阈值
            'signal_window': 3  # 信号窗口(天)
        }

        logger.info("资金流分析模块初始化完成")

    def _load_stock_code_map(self) -> Dict[str, str]:
        """
        加载股票代码映射

        Returns:
            stock_code_map: 股票代码映射
        """
        # 尝试从数据存储加载
        stock_map = self.data_storage.load('fund_flow_analyzer', 'stock_code_map')
        if stock_map:
            logger.info(f"从数据存储加载了{len(stock_map)}个股票代码")
            return stock_map

        # 如果数据存储中没有，尝试从文件加载
        try:
            stock_file = os.path.join('data', 'fund_flow_analyzer', 'stock_code_map.json')
            if os.path.exists(stock_file):
                with open(stock_file, 'r', encoding='utf-8') as f:
                    stock_map = json.load(f)
                logger.info(f"从文件加载了{len(stock_map)}个股票代码")

                # 保存到数据存储
                self.data_storage.save('fund_flow_analyzer', 'stock_code_map', stock_map)

                return stock_map
        except Exception as e:
            logger.error(f"加载股票代码映射文件失败: {str(e)}")

        # 如果都没有，尝试从akshare获取
        try:
            # 获取A股股票代码和名称
            stock_info_df = ak.stock_info_a_code_name()

            # 创建映射
            stock_map = {}
            for _, row in stock_info_df.iterrows():
                code = row['code']
                name = row['name']
                stock_map[name] = code

            logger.info(f"从akshare获取了{len(stock_map)}个股票代码")

            # 保存到数据存储
            self.data_storage.save('fund_flow_analyzer', 'stock_code_map', stock_map)

            return stock_map

        except Exception as e:
            logger.error(f"从akshare获取股票代码失败: {str(e)}")

        # 如果都失败了，使用默认映射
        stock_map = {
            '贵州茅台': '600519',
            '宁德时代': '300750',
            '招商银行': '600036',
            '中国平安': '601318',
            '比亚迪': '002594',
            '五粮液': '000858',
            '隆基绿能': '601012',
            '美的集团': '000333',
            '腾讯控股': '00700',
            '阿里巴巴': '09988'
        }

        # 保存到数据存储
        self.data_storage.save('fund_flow_analyzer', 'stock_code_map', stock_map)

        logger.info(f"使用默认股票代码映射，包含{len(stock_map)}个股票")
        return stock_map

    def _load_sector_map(self) -> Dict[str, List[str]]:
        """
        加载行业板块映射

        Returns:
            sector_map: 行业板块映射
        """
        # 尝试从数据存储加载
        sector_map = self.data_storage.load('fund_flow_analyzer', 'sector_map')
        if sector_map:
            logger.info(f"从数据存储加载了{len(sector_map)}个行业板块")
            return sector_map

        # 如果数据存储中没有，尝试从文件加载
        try:
            sector_file = os.path.join('data', 'fund_flow_analyzer', 'sector_map.json')
            if os.path.exists(sector_file):
                with open(sector_file, 'r', encoding='utf-8') as f:
                    sector_map = json.load(f)
                logger.info(f"从文件加载了{len(sector_map)}个行业板块")

                # 保存到数据存储
                self.data_storage.save('fund_flow_analyzer', 'sector_map', sector_map)

                return sector_map
        except Exception as e:
            logger.error(f"加载行业板块映射文件失败: {str(e)}")

        # 如果都没有，使用默认映射
        sector_map = {
            '白酒': ['贵州茅台', '五粮液', '泸州老窖', '洋河股份', '山西汾酒'],
            '新能源': ['宁德时代', '比亚迪', '隆基绿能', '阳光电源', '亿纬锂能'],
            '银行': ['招商银行', '工商银行', '建设银行', '农业银行', '中国银行'],
            '保险': ['中国平安', '中国人寿', '中国太保', '新华保险', '中国人保'],
            '家电': ['美的集团', '格力电器', '海尔智家', '海信家电', '老板电器'],
            '医药': ['恒瑞医药', '迈瑞医疗', '药明康德', '爱尔眼科', '通策医疗'],
            '科技': ['腾讯控股', '阿里巴巴', '百度', '京东', '网易'],
            '半导体': ['中芯国际', '韦尔股份', '北方华创', '兆易创新', '紫光国微']
        }

        # 保存到数据存储
        self.data_storage.save('fund_flow_analyzer', 'sector_map', sector_map)

        logger.info(f"使用默认行业板块映射，包含{len(sector_map)}个行业")
        return sector_map

    def initialize(self):
        """初始化模块"""
        logger.info("初始化资金流分析模块...")

        # 注册定时任务
        if self.scheduler:
            # 每天获取北向资金数据
            self.schedule_task(
                function='fetch_northbound_flow',
                params={},
                priority='medium',
                schedule_time=datetime.now() + timedelta(minutes=5)
            )

            # 每天分析游资行为
            self.schedule_task(
                function='analyze_hot_money_behavior',
                params={},
                priority='medium',
                schedule_time=datetime.now() + timedelta(minutes=10)
            )

            # 每天获取五级资金流数据
            self.schedule_task(
                function='fetch_tiered_fund_flow',
                params={},
                priority='medium',
                schedule_time=datetime.now() + timedelta(minutes=15)
            )

            # 每周生成资金流分析报告
            self.schedule_task(
                function='generate_fund_flow_report',
                params={},
                priority='low',
                schedule_time=datetime.now() + timedelta(days=1)
            )

        logger.info("资金流分析模块初始化完成")

    def get_status(self) -> Dict[str, Any]:
        """获取模块状态"""
        return {
            'module_name': self.module_name,
            'enabled': self.config.get('enabled', True),
            'last_update_time': self.last_update_time.isoformat() if self.last_update_time else None,
            'northbound_data_count': len(self.northbound_data.get('daily', [])),
            'northbound_signals_count': len(self.northbound_data.get('signals', [])),
            'hot_money_patterns_count': len(self.hot_money_data.get('patterns', [])),
            'hot_money_targets_count': len(self.hot_money_data.get('targets', [])),
            'tiered_flow_signals_count': len(self.tiered_flow_data.get('signals', []))
        }

    def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        try:
            # 检查上次更新时间
            time_since_last_update = datetime.now() - self.last_update_time

            if time_since_last_update > timedelta(days=2):
                status = 'warning'
                message = f'上次更新时间超过2天: {self.last_update_time.isoformat()}'
            else:
                status = 'healthy'
                message = f'上次更新时间: {self.last_update_time.isoformat()}'

            return {
                'status': status,
                'message': message,
                'last_update_time': self.last_update_time.isoformat(),
                'time_since_last_update': str(time_since_last_update),
                'northbound_data_count': len(self.northbound_data.get('daily', [])),
                'hot_money_patterns_count': len(self.hot_money_data.get('patterns', [])),
                'tiered_flow_signals_count': len(self.tiered_flow_data.get('signals', []))
            }

        except Exception as e:
            logger.error(f"健康检查失败: {str(e)}")
            return {
                'status': 'error',
                'message': f'健康检查失败: {str(e)}',
                'error': str(e)
            }

    def fetch_cross_border_flow(self, days_to_fetch=90, **kwargs) -> Dict[str, Any]:
        """
        获取北向南向资金流向数据

        Args:
            days_to_fetch: 获取历史数据的天数，默认90天

        Returns:
            result: 获取结果
        """
        try:
            logger.info("开始获取北向南向资金流向数据...")

            # 记录当前时间
            current_time = datetime.now()

            # 使用接口获取沪深港通资金流向数据
            hsgt_df = ak.stock_hsgt_fund_flow_summary_em()

            if not hsgt_df.empty:
                # 转换日期格式
                hsgt_df.loc[:, 'date'] = pd.to_datetime(hsgt_df['交易日']).dt.strftime('%Y-%m-%d')

                # 提取北向资金数据（沪股通和深股通）
                north_df = hsgt_df[hsgt_df['资金方向'] == '北向'].copy()

                # 提取南向资金数据（港股通(沪)和港股通(深)）
                south_df = hsgt_df[hsgt_df['资金方向'] == '南向'].copy()

                # 处理北向资金数据
                northbound_data = self._process_northbound_data(north_df)

                # 处理南向资金数据
                southbound_data = self._process_southbound_data(south_df)

                # 更新数据库
                self._update_cross_border_flow_data(northbound_data, southbound_data)

                # 生成北向资金信号
                north_signals = self._generate_northbound_signals()

                # 生成南向资金信号
                south_signals = self._generate_southbound_signals()

                # 更新上次更新时间
                self.last_update_time = current_time
                self.cross_border_flow_data['last_update'] = current_time.isoformat()

                # 保存数据到数据库
                self._save_to_database()

                logger.info(f"北向南向资金流向数据获取成功，北向{len(northbound_data)}条，南向{len(southbound_data)}条")

                return {
                    'status': 'success',
                    'message': f'北向南向资金流向数据获取成功，北向{len(northbound_data)}条，南向{len(southbound_data)}条',
                    'northbound_count': len(northbound_data),
                    'southbound_count': len(southbound_data),
                    'northbound_signals_count': len(north_signals),
                    'southbound_signals_count': len(south_signals)
                }

            logger.warning("未获取到北向南向资金流向数据")
            return {
                'status': 'warning',
                'message': '未获取到北向南向资金流向数据',
                'count': 0
            }

        except Exception as e:
            logger.error(f"获取北向南向资金流向数据失败: {str(e)}")
            return {
                'status': 'error',
                'message': f'获取北向南向资金流向数据失败: {str(e)}',
                'error': str(e)
            }

    def _process_northbound_data(self, north_df: pd.DataFrame) -> List[Dict[str, Any]]:
        """
        处理北向资金数据

        Args:
            north_df: 北向资金数据DataFrame

        Returns:
            daily_data: 处理后的北向资金数据列表
        """
        if north_df.empty:
            return []

        # 提取沪股通数据
        sh_df = north_df[north_df['板块'] == '沪股通'].copy()
        sh_df = sh_df.rename(columns={
            '成交净买额': 'net_flow_sh',
            '资金净流入': 'net_inflow_sh',
            '当日资金余额': 'balance_sh'
        })

        # 提取深股通数据
        sz_df = north_df[north_df['板块'] == '深股通'].copy()
        sz_df = sz_df.rename(columns={
            '成交净买额': 'net_flow_sz',
            '资金净流入': 'net_inflow_sz',
            '当日资金余额': 'balance_sz'
        })

        # 合并数据
        if not sh_df.empty and not sz_df.empty:
            # 按日期合并
            merged_df = pd.merge(
                sh_df[['date', 'net_flow_sh', 'net_inflow_sh', 'balance_sh']],
                sz_df[['date', 'net_flow_sz', 'net_inflow_sz', 'balance_sz']],
                on='date'
            )

            # 计算北向资金净流入
            merged_df['net_flow'] = merged_df['net_flow_sh'] + merged_df['net_flow_sz']
            merged_df['inflow_sh'] = merged_df['net_flow_sh'] + merged_df['net_flow_sh'].abs() / 2
            merged_df['outflow_sh'] = merged_df['inflow_sh'] - merged_df['net_flow_sh']
            merged_df['inflow_sz'] = merged_df['net_flow_sz'] + merged_df['net_flow_sz'].abs() / 2
            merged_df['outflow_sz'] = merged_df['inflow_sz'] - merged_df['net_flow_sz']

            # 转换为列表
            daily_data = merged_df.to_dict('records')
            return daily_data

        return []

    def _process_southbound_data(self, south_df: pd.DataFrame) -> List[Dict[str, Any]]:
        """
        处理南向资金数据

        Args:
            south_df: 南向资金数据DataFrame

        Returns:
            daily_data: 处理后的南向资金数据列表
        """
        if south_df.empty:
            return []

        # 提取港股通(沪)数据
        sh_df = south_df[south_df['板块'] == '港股通(沪)'].copy()
        sh_df = sh_df.rename(columns={
            '成交净买额': 'net_flow_sh',
            '资金净流入': 'net_inflow_sh',
            '当日资金余额': 'balance_sh'
        })

        # 提取港股通(深)数据
        sz_df = south_df[south_df['板块'] == '港股通(深)'].copy()
        sz_df = sz_df.rename(columns={
            '成交净买额': 'net_flow_sz',
            '资金净流入': 'net_inflow_sz',
            '当日资金余额': 'balance_sz'
        })

        # 合并数据
        if not sh_df.empty and not sz_df.empty:
            # 按日期合并
            merged_df = pd.merge(
                sh_df[['date', 'net_flow_sh', 'net_inflow_sh', 'balance_sh']],
                sz_df[['date', 'net_flow_sz', 'net_inflow_sz', 'balance_sz']],
                on='date'
            )

            # 计算南向资金净流入
            merged_df['net_flow'] = merged_df['net_flow_sh'] + merged_df['net_flow_sz']
            merged_df['inflow_sh'] = merged_df['net_flow_sh'] + merged_df['net_flow_sh'].abs() / 2
            merged_df['outflow_sh'] = merged_df['inflow_sh'] - merged_df['net_flow_sh']
            merged_df['inflow_sz'] = merged_df['net_flow_sz'] + merged_df['net_flow_sz'].abs() / 2
            merged_df['outflow_sz'] = merged_df['inflow_sz'] - merged_df['net_flow_sz']

            # 转换为列表
            daily_data = merged_df.to_dict('records')
            return daily_data

        return []

    def _update_cross_border_flow_data(self, northbound_data: List[Dict[str, Any]], southbound_data: List[Dict[str, Any]]):
        """
        更新北向南向资金数据

        Args:
            northbound_data: 北向资金数据
            southbound_data: 南向资金数据
        """
        # 更新北向资金数据
        if northbound_data:
            self._update_northbound_data(northbound_data)

        # 更新南向资金数据
        if southbound_data:
            self._update_southbound_data(southbound_data)

    def _save_to_database(self):
        """保存数据到数据库"""
        # 保存北向南向资金数据
        self.data_storage.save('fund_flow_analyzer', 'cross_border_flow_data', self.cross_border_flow_data, StorageLevel.WARM)

        # 保存到CSV文件作为备份
        try:
            # 确保目录存在
            os.makedirs('data/fund_flow_analyzer', exist_ok=True)

            # 保存北向资金数据
            northbound_df = pd.DataFrame(self.cross_border_flow_data['northbound']['daily'])
            if not northbound_df.empty:
                northbound_df.to_csv('data/fund_flow_analyzer/northbound_data.csv', index=False)

            # 保存南向资金数据
            southbound_df = pd.DataFrame(self.cross_border_flow_data['southbound']['daily'])
            if not southbound_df.empty:
                southbound_df.to_csv('data/fund_flow_analyzer/southbound_data.csv', index=False)

            logger.info("北向南向资金数据已保存到数据库和CSV文件")
        except Exception as e:
            logger.error(f"保存数据到CSV文件失败: {str(e)}")

    # 已删除 _fetch_northbound_sh 和 _fetch_northbound_sz 方法，使用 stock_hsgt_fund_flow_summary_em 接口替代

    def _update_northbound_data(self, daily_data: List[Dict[str, Any]]):
        """
        更新北向资金数据

        Args:
            daily_data: 每日北向资金数据
        """
        # 获取现有数据
        existing_data = self.cross_border_flow_data['northbound'].get('daily', [])

        # 创建日期集合，用于去重
        existing_dates = {item['date'] for item in existing_data}

        # 添加新数据
        new_data = []
        for item in daily_data:
            if item['date'] not in existing_dates:
                new_data.append(item)
                existing_dates.add(item['date'])

        # 合并数据
        combined_data = existing_data + new_data

        # 按日期排序
        combined_data = sorted(combined_data, key=lambda x: x['date'], reverse=True)

        # 更新北向资金数据
        self.cross_border_flow_data['northbound']['daily'] = combined_data

        # 计算累计流入
        self._calculate_northbound_cumulative_flow()

        logger.info(f"北向资金数据更新成功，共{len(combined_data)}条")

    def _calculate_northbound_cumulative_flow(self):
        """计算北向资金累计流入"""
        daily_data = self.cross_border_flow_data['northbound'].get('daily', [])

        if not daily_data:
            return

        # 按日期排序（从旧到新）
        sorted_data = sorted(daily_data, key=lambda x: x['date'])

        # 计算累计流入
        cumulative = {
            'net_flow': 0,
            'inflow_sh': 0,
            'outflow_sh': 0,
            'inflow_sz': 0,
            'outflow_sz': 0
        }

        for item in sorted_data:
            cumulative['net_flow'] += item['net_flow']
            cumulative['inflow_sh'] += item['inflow_sh']
            cumulative['outflow_sh'] += item['outflow_sh']
            cumulative['inflow_sz'] += item['inflow_sz']
            cumulative['outflow_sz'] += item['outflow_sz']

        # 更新累计数据
        self.cross_border_flow_data['northbound']['cumulative'] = cumulative

    def _calculate_southbound_cumulative_flow(self):
        """计算南向资金累计流入"""
        daily_data = self.cross_border_flow_data['southbound'].get('daily', [])

        if not daily_data:
            return

        # 按日期排序（从旧到新）
        sorted_data = sorted(daily_data, key=lambda x: x['date'])

        # 计算累计流入
        cumulative = {
            'net_flow': 0,
            'inflow_sh': 0,
            'outflow_sh': 0,
            'inflow_sz': 0,
            'outflow_sz': 0
        }

        for item in sorted_data:
            cumulative['net_flow'] += item['net_flow']
            cumulative['inflow_sh'] += item['inflow_sh']
            cumulative['outflow_sh'] += item['outflow_sh']
            cumulative['inflow_sz'] += item['inflow_sz']
            cumulative['outflow_sz'] += item['outflow_sz']

        # 更新累计数据
        self.cross_border_flow_data['southbound']['cumulative'] = cumulative

    def _generate_northbound_signals(self) -> List[Dict[str, Any]]:
        """
        生成北向资金信号

        Returns:
            signals: 北向资金信号列表
        """
        daily_data = self.cross_border_flow_data['northbound'].get('daily', [])

        if len(daily_data) < self.northbound_params['long_window']:
            logger.warning(f"数据不足，无法生成北向资金信号，需要至少{self.northbound_params['long_window']}天数据")
            return []

        # 按日期排序（从新到旧）
        sorted_data = sorted(daily_data, key=lambda x: x['date'], reverse=True)

        # 获取参数
        short_window = self.northbound_params['short_window']
        medium_window = self.northbound_params['medium_window']
        long_window = self.northbound_params['long_window']
        flow_threshold = self.northbound_params['flow_threshold']
        reversal_threshold = self.northbound_params['reversal_threshold']
        persistence_threshold = self.northbound_params['persistence_threshold']

        # 计算短期、中期和长期平均净流入
        short_avg = sum(item['net_flow'] for item in sorted_data[:short_window]) / short_window
        medium_avg = sum(item['net_flow'] for item in sorted_data[:medium_window]) / medium_window
        long_avg = sum(item['net_flow'] for item in sorted_data[:long_window]) / long_window

        # 计算短期、中期和长期累计净流入
        short_sum = sum(item['net_flow'] for item in sorted_data[:short_window])
        medium_sum = sum(item['net_flow'] for item in sorted_data[:medium_window])
        long_sum = sum(item['net_flow'] for item in sorted_data[:long_window])

        # 计算连续流入/流出天数
        consecutive_inflow = 0
        consecutive_outflow = 0
        for item in sorted_data:
            if item['net_flow'] > 0:
                consecutive_inflow += 1
                consecutive_outflow = 0
            elif item['net_flow'] < 0:
                consecutive_outflow += 1
                consecutive_inflow = 0
            else:
                consecutive_inflow = 0
                consecutive_outflow = 0

            if consecutive_inflow >= persistence_threshold or consecutive_outflow >= persistence_threshold:
                break

        # 生成信号
        signals = []

        # 1. 大额流入信号
        if short_sum > flow_threshold:
            signals.append({
                'type': 'large_inflow',
                'date': sorted_data[0]['date'],
                'strength': short_sum / flow_threshold,
                'description': f'北向资金短期大额流入，{short_window}日累计流入{short_sum:.2f}亿元',
                'created_at': datetime.now().isoformat()
            })

        # 2. 大额流出信号
        if short_sum < -flow_threshold:
            signals.append({
                'type': 'large_outflow',
                'date': sorted_data[0]['date'],
                'strength': abs(short_sum) / flow_threshold,
                'description': f'北向资金短期大额流出，{short_window}日累计流出{abs(short_sum):.2f}亿元',
                'created_at': datetime.now().isoformat()
            })

        # 3. 趋势反转信号
        if short_avg * long_avg < 0 and abs(short_avg) > reversal_threshold * abs(long_avg):
            if short_avg > 0:
                signals.append({
                    'type': 'trend_reversal_bullish',
                    'date': sorted_data[0]['date'],
                    'strength': abs(short_avg / long_avg),
                    'description': f'北向资金趋势反转（看多），短期平均流入{short_avg:.2f}亿元，长期平均流出{long_avg:.2f}亿元',
                    'created_at': datetime.now().isoformat()
                })
            else:
                signals.append({
                    'type': 'trend_reversal_bearish',
                    'date': sorted_data[0]['date'],
                    'strength': abs(short_avg / long_avg),
                    'description': f'北向资金趋势反转（看空），短期平均流出{abs(short_avg):.2f}亿元，长期平均流入{long_avg:.2f}亿元',
                    'created_at': datetime.now().isoformat()
                })

        # 4. 持续流入信号
        if consecutive_inflow >= persistence_threshold:
            signals.append({
                'type': 'persistent_inflow',
                'date': sorted_data[0]['date'],
                'strength': consecutive_inflow / persistence_threshold,
                'description': f'北向资金持续流入，已连续{consecutive_inflow}个交易日净流入',
                'created_at': datetime.now().isoformat()
            })

        # 5. 持续流出信号
        if consecutive_outflow >= persistence_threshold:
            signals.append({
                'type': 'persistent_outflow',
                'date': sorted_data[0]['date'],
                'strength': consecutive_outflow / persistence_threshold,
                'description': f'北向资金持续流出，已连续{consecutive_outflow}个交易日净流出',
                'created_at': datetime.now().isoformat()
            })

        # 6. 加速流入信号
        if short_avg > medium_avg > long_avg > 0:
            signals.append({
                'type': 'accelerating_inflow',
                'date': sorted_data[0]['date'],
                'strength': short_avg / long_avg,
                'description': f'北向资金加速流入，短期平均{short_avg:.2f}亿元，中期平均{medium_avg:.2f}亿元，长期平均{long_avg:.2f}亿元',
                'created_at': datetime.now().isoformat()
            })

        # 7. 加速流出信号
        if short_avg < medium_avg < long_avg < 0:
            signals.append({
                'type': 'accelerating_outflow',
                'date': sorted_data[0]['date'],
                'strength': abs(short_avg / long_avg),
                'description': f'北向资金加速流出，短期平均{short_avg:.2f}亿元，中期平均{medium_avg:.2f}亿元，长期平均{long_avg:.2f}亿元',
                'created_at': datetime.now().isoformat()
            })

        # 更新信号
        self.cross_border_flow_data['northbound']['signals'] = signals

        logger.info(f"生成北向资金信号{len(signals)}个")

        return signals

    def _generate_southbound_signals(self) -> List[Dict[str, Any]]:
        """
        生成南向资金信号

        Returns:
            signals: 南向资金信号列表
        """
        daily_data = self.cross_border_flow_data['southbound'].get('daily', [])

        if len(daily_data) < self.northbound_params['long_window']:
            logger.warning(f"数据不足，无法生成南向资金信号，需要至少{self.northbound_params['long_window']}天数据")
            return []

        # 按日期排序（从新到旧）
        sorted_data = sorted(daily_data, key=lambda x: x['date'], reverse=True)

        # 获取参数（使用与北向资金相同的参数）
        short_window = self.northbound_params['short_window']
        medium_window = self.northbound_params['medium_window']
        long_window = self.northbound_params['long_window']
        flow_threshold = self.northbound_params['flow_threshold']
        reversal_threshold = self.northbound_params['reversal_threshold']
        persistence_threshold = self.northbound_params['persistence_threshold']

        # 计算短期、中期和长期平均净流入
        short_avg = sum(item['net_flow'] for item in sorted_data[:short_window]) / short_window
        medium_avg = sum(item['net_flow'] for item in sorted_data[:medium_window]) / medium_window
        long_avg = sum(item['net_flow'] for item in sorted_data[:long_window]) / long_window

        # 计算短期、中期和长期累计净流入
        short_sum = sum(item['net_flow'] for item in sorted_data[:short_window])
        medium_sum = sum(item['net_flow'] for item in sorted_data[:medium_window])
        long_sum = sum(item['net_flow'] for item in sorted_data[:long_window])

        # 计算连续流入/流出天数
        consecutive_inflow = 0
        consecutive_outflow = 0
        for item in sorted_data:
            if item['net_flow'] > 0:
                consecutive_inflow += 1
                consecutive_outflow = 0
            elif item['net_flow'] < 0:
                consecutive_outflow += 1
                consecutive_inflow = 0
            else:
                consecutive_inflow = 0
                consecutive_outflow = 0

            if consecutive_inflow >= persistence_threshold or consecutive_outflow >= persistence_threshold:
                break

        # 生成信号
        signals = []

        # 1. 大额流入信号
        if short_sum > flow_threshold:
            signals.append({
                'type': 'large_inflow',
                'date': sorted_data[0]['date'],
                'strength': short_sum / flow_threshold,
                'description': f'南向资金短期大额流入，{short_window}日累计流入{short_sum:.2f}亿元',
                'created_at': datetime.now().isoformat()
            })

        # 2. 大额流出信号
        if short_sum < -flow_threshold:
            signals.append({
                'type': 'large_outflow',
                'date': sorted_data[0]['date'],
                'strength': abs(short_sum) / flow_threshold,
                'description': f'南向资金短期大额流出，{short_window}日累计流出{abs(short_sum):.2f}亿元',
                'created_at': datetime.now().isoformat()
            })

        # 3. 趋势反转信号
        if short_avg * long_avg < 0 and abs(short_avg) > reversal_threshold * abs(long_avg):
            if short_avg > 0:
                signals.append({
                    'type': 'trend_reversal_bullish',
                    'date': sorted_data[0]['date'],
                    'strength': abs(short_avg / long_avg),
                    'description': f'南向资金趋势反转（看多），短期平均流入{short_avg:.2f}亿元，长期平均流出{long_avg:.2f}亿元',
                    'created_at': datetime.now().isoformat()
                })
            else:
                signals.append({
                    'type': 'trend_reversal_bearish',
                    'date': sorted_data[0]['date'],
                    'strength': abs(short_avg / long_avg),
                    'description': f'南向资金趋势反转（看空），短期平均流出{abs(short_avg):.2f}亿元，长期平均流入{long_avg:.2f}亿元',
                    'created_at': datetime.now().isoformat()
                })

        # 4. 持续流入信号
        if consecutive_inflow >= persistence_threshold:
            signals.append({
                'type': 'persistent_inflow',
                'date': sorted_data[0]['date'],
                'strength': consecutive_inflow / persistence_threshold,
                'description': f'南向资金持续流入，已连续{consecutive_inflow}个交易日净流入',
                'created_at': datetime.now().isoformat()
            })

        # 5. 持续流出信号
        if consecutive_outflow >= persistence_threshold:
            signals.append({
                'type': 'persistent_outflow',
                'date': sorted_data[0]['date'],
                'strength': consecutive_outflow / persistence_threshold,
                'description': f'南向资金持续流出，已连续{consecutive_outflow}个交易日净流出',
                'created_at': datetime.now().isoformat()
            })

        # 6. 加速流入信号
        if short_avg > medium_avg > long_avg > 0:
            signals.append({
                'type': 'accelerating_inflow',
                'date': sorted_data[0]['date'],
                'strength': short_avg / long_avg,
                'description': f'南向资金加速流入，短期平均{short_avg:.2f}亿元，中期平均{medium_avg:.2f}亿元，长期平均{long_avg:.2f}亿元',
                'created_at': datetime.now().isoformat()
            })

        # 7. 加速流出信号
        if short_avg < medium_avg < long_avg < 0:
            signals.append({
                'type': 'accelerating_outflow',
                'date': sorted_data[0]['date'],
                'strength': abs(short_avg / long_avg),
                'description': f'南向资金加速流出，短期平均{short_avg:.2f}亿元，中期平均{medium_avg:.2f}亿元，长期平均{long_avg:.2f}亿元',
                'created_at': datetime.now().isoformat()
            })

        # 更新信号
        self.cross_border_flow_data['southbound']['signals'] = signals

        logger.info(f"生成南向资金信号{len(signals)}个")

        return signals

    def get_northbound_flow_analysis(self, days: int = 30) -> Dict[str, Any]:
        """
        获取北向资金流向分析

        Args:
            days: 分析天数

        Returns:
            analysis: 北向资金流向分析
        """
        try:
            daily_data = self.cross_border_flow_data['northbound'].get('daily', [])
            signals = self.cross_border_flow_data['northbound'].get('signals', [])
            cumulative = self.cross_border_flow_data['northbound'].get('cumulative', {})

            if not daily_data:
                return {
                    'status': 'warning',
                    'message': '没有北向资金数据',
                    'data': None
                }

            # 按日期排序（从新到旧）
            sorted_data = sorted(daily_data, key=lambda x: x['date'], reverse=True)

            # 只保留指定天数的数据
            recent_data = sorted_data[:days]

            # 计算统计数据
            total_net_flow = sum(item['net_flow'] for item in recent_data)
            avg_net_flow = total_net_flow / len(recent_data) if recent_data else 0
            inflow_days = sum(1 for item in recent_data if item['net_flow'] > 0)
            outflow_days = sum(1 for item in recent_data if item['net_flow'] < 0)
            max_inflow = max((item['net_flow'] for item in recent_data), default=0)
            max_outflow = min((item['net_flow'] for item in recent_data), default=0)

            # 计算趋势
            if len(recent_data) >= 5:
                recent_5d = recent_data[:5]
                recent_5d_flow = sum(item['net_flow'] for item in recent_5d)

                if recent_5d_flow > 0:
                    trend = '流入'
                    trend_strength = recent_5d_flow / (5 * avg_net_flow) if avg_net_flow > 0 else 1.0
                elif recent_5d_flow < 0:
                    trend = '流出'
                    trend_strength = abs(recent_5d_flow) / (5 * abs(avg_net_flow)) if avg_net_flow < 0 else 1.0
                else:
                    trend = '平衡'
                    trend_strength = 0.0
            else:
                trend = '数据不足'
                trend_strength = 0.0

            # 创建分析结果
            analysis = {
                'period': f"最近{len(recent_data)}个交易日",
                'total_net_flow': total_net_flow,
                'avg_net_flow': avg_net_flow,
                'inflow_days': inflow_days,
                'outflow_days': outflow_days,
                'inflow_ratio': inflow_days / len(recent_data) if recent_data else 0,
                'max_inflow': max_inflow,
                'max_outflow': max_outflow,
                'trend': trend,
                'trend_strength': trend_strength,
                'signals': signals,
                'cumulative': cumulative,
                'daily_data': recent_data
            }

            return {
                'status': 'success',
                'message': f'北向资金流向分析完成，共{len(recent_data)}天数据',
                'data': analysis
            }

        except Exception as e:
            logger.error(f"获取北向资金流向分析失败: {str(e)}")
            return {
                'status': 'error',
                'message': f'获取北向资金流向分析失败: {str(e)}',
                'error': str(e),
                'data': None
            }

    def get_southbound_flow_analysis(self, days: int = 30) -> Dict[str, Any]:
        """
        获取南向资金流向分析

        Args:
            days: 分析天数

        Returns:
            analysis: 南向资金流向分析
        """
        try:
            daily_data = self.cross_border_flow_data['southbound'].get('daily', [])
            signals = self.cross_border_flow_data['southbound'].get('signals', [])
            cumulative = self.cross_border_flow_data['southbound'].get('cumulative', {})

            if not daily_data:
                return {
                    'status': 'warning',
                    'message': '没有南向资金数据',
                    'data': None
                }

            # 按日期排序（从新到旧）
            sorted_data = sorted(daily_data, key=lambda x: x['date'], reverse=True)

            # 只保留指定天数的数据
            recent_data = sorted_data[:days]

            # 计算统计数据
            total_net_flow = sum(item['net_flow'] for item in recent_data)
            avg_net_flow = total_net_flow / len(recent_data) if recent_data else 0
            inflow_days = sum(1 for item in recent_data if item['net_flow'] > 0)
            outflow_days = sum(1 for item in recent_data if item['net_flow'] < 0)
            max_inflow = max((item['net_flow'] for item in recent_data), default=0)
            max_outflow = min((item['net_flow'] for item in recent_data), default=0)

            # 计算趋势
            if len(recent_data) >= 5:
                recent_5d = recent_data[:5]
                recent_5d_flow = sum(item['net_flow'] for item in recent_5d)

                if recent_5d_flow > 0:
                    trend = '流入'
                    trend_strength = recent_5d_flow / (5 * avg_net_flow) if avg_net_flow > 0 else 1.0
                elif recent_5d_flow < 0:
                    trend = '流出'
                    trend_strength = abs(recent_5d_flow) / (5 * abs(avg_net_flow)) if avg_net_flow < 0 else 1.0
                else:
                    trend = '平衡'
                    trend_strength = 0.0
            else:
                trend = '数据不足'
                trend_strength = 0.0

            # 创建分析结果
            analysis = {
                'period': f"最近{len(recent_data)}个交易日",
                'total_net_flow': total_net_flow,
                'avg_net_flow': avg_net_flow,
                'inflow_days': inflow_days,
                'outflow_days': outflow_days,
                'inflow_ratio': inflow_days / len(recent_data) if recent_data else 0,
                'max_inflow': max_inflow,
                'max_outflow': max_outflow,
                'trend': trend,
                'trend_strength': trend_strength,
                'signals': signals,
                'cumulative': cumulative,
                'daily_data': recent_data
            }

            return {
                'status': 'success',
                'message': f'南向资金流向分析完成，共{len(recent_data)}天数据',
                'data': analysis
            }

        except Exception as e:
            logger.error(f"获取南向资金流向分析失败: {str(e)}")
            return {
                'status': 'error',
                'message': f'获取南向资金流向分析失败: {str(e)}',
                'error': str(e),
                'data': None
            }

    def get_cross_border_flow_analysis(self, days: int = 30) -> Dict[str, Any]:
        """
        获取北向南向资金流向综合分析

        Args:
            days: 分析天数

        Returns:
            analysis: 北向南向资金流向综合分析
        """
        try:
            # 获取北向资金分析
            northbound_analysis = self.get_northbound_flow_analysis(days)

            # 获取南向资金分析
            southbound_analysis = self.get_southbound_flow_analysis(days)

            # 检查数据是否可用
            if northbound_analysis['status'] != 'success' and southbound_analysis['status'] != 'success':
                return {
                    'status': 'warning',
                    'message': '没有北向南向资金数据',
                    'data': None
                }

            # 创建综合分析结果
            analysis = {
                'northbound': northbound_analysis.get('data'),
                'southbound': southbound_analysis.get('data'),
                'last_update': self.cross_border_flow_data.get('last_update')
            }

            # 计算北向南向资金流向相关性
            if northbound_analysis['status'] == 'success' and southbound_analysis['status'] == 'success':
                north_data = northbound_analysis['data']['daily_data']
                south_data = southbound_analysis['data']['daily_data']

                # 创建日期映射
                north_flow_by_date = {item['date']: item['net_flow'] for item in north_data}
                south_flow_by_date = {item['date']: item['net_flow'] for item in south_data}

                # 找出共同日期
                common_dates = set(north_flow_by_date.keys()) & set(south_flow_by_date.keys())

                if common_dates:
                    # 计算相关性
                    north_flows = [north_flow_by_date[date] for date in common_dates]
                    south_flows = [south_flow_by_date[date] for date in common_dates]

                    # 计算相关系数
                    if len(north_flows) > 1:
                        correlation = self._calculate_correlation(north_flows, south_flows)
                        analysis['correlation'] = correlation

                        # 判断相关性类型
                        if correlation > 0.7:
                            analysis['correlation_type'] = '强正相关'
                        elif correlation > 0.3:
                            analysis['correlation_type'] = '中等正相关'
                        elif correlation > -0.3:
                            analysis['correlation_type'] = '弱相关'
                        elif correlation > -0.7:
                            analysis['correlation_type'] = '中等负相关'
                        else:
                            analysis['correlation_type'] = '强负相关'

            return {
                'status': 'success',
                'message': '北向南向资金流向综合分析完成',
                'data': analysis
            }

        except Exception as e:
            logger.error(f"获取北向南向资金流向综合分析失败: {str(e)}")
            return {
                'status': 'error',
                'message': f'获取北向南向资金流向综合分析失败: {str(e)}',
                'error': str(e),
                'data': None
            }

    def _calculate_correlation(self, x: List[float], y: List[float]) -> float:
        """
        计算两个序列的相关系数

        Args:
            x: 序列1
            y: 序列2

        Returns:
            correlation: 相关系数
        """
        if len(x) != len(y) or len(x) < 2:
            return 0.0

        n = len(x)

        # 计算均值
        mean_x = sum(x) / n
        mean_y = sum(y) / n

        # 计算协方差和标准差
        cov_xy = sum((x[i] - mean_x) * (y[i] - mean_y) for i in range(n))
        var_x = sum((x[i] - mean_x) ** 2 for i in range(n))
        var_y = sum((y[i] - mean_y) ** 2 for i in range(n))

        # 计算相关系数
        if var_x > 0 and var_y > 0:
            correlation = cov_xy / ((var_x * var_y) ** 0.5)
            return max(-1.0, min(1.0, correlation))  # 确保结果在 [-1, 1] 范围内
        else:
            return 0.0

    def analyze_hot_money_behavior(self, **kwargs) -> Dict[str, Any]:
        """
        分析游资行为模式

        Returns:
            result: 分析结果
        """
        try:
            logger.info("开始分析游资行为模式...")

            # 记录当前时间
            current_time = datetime.now()

            # 获取游资活跃股票
            active_stocks = self._identify_hot_money_active_stocks()

            if not active_stocks:
                logger.warning("未识别到游资活跃股票")
                return {
                    'status': 'warning',
                    'message': '未识别到游资活跃股票',
                    'count': 0
                }

            # 识别游资行为模式
            patterns = self._identify_hot_money_patterns(active_stocks)

            # 预测游资目标股票
            targets = self._predict_hot_money_targets(patterns)

            # 更新游资行为数据
            self._update_hot_money_data(active_stocks, patterns, targets)

            # 更新上次更新时间
            self.last_update_time = current_time

            logger.info(f"游资行为模式分析成功，识别{len(active_stocks)}只活跃股票，{len(patterns)}种行为模式，预测{len(targets)}只目标股票")

            return {
                'status': 'success',
                'message': f'游资行为模式分析成功，识别{len(active_stocks)}只活跃股票，{len(patterns)}种行为模式，预测{len(targets)}只目标股票',
                'active_stocks_count': len(active_stocks),
                'patterns_count': len(patterns),
                'targets_count': len(targets)
            }

        except Exception as e:
            logger.error(f"分析游资行为模式失败: {str(e)}")
            return {
                'status': 'error',
                'message': f'分析游资行为模式失败: {str(e)}',
                'error': str(e)
            }

    def _identify_hot_money_active_stocks(self) -> List[Dict[str, Any]]:
        """
        识别游资活跃股票

        Returns:
            active_stocks: 游资活跃股票列表
        """
        try:
            # 获取A股涨幅排行
            top_stocks_df = self._fetch_top_stocks()

            if top_stocks_df.empty:
                logger.warning("未获取到A股涨幅排行数据")
                return []

            # 获取参数
            turnover_threshold = self.hot_money_params['turnover_threshold']
            volume_increase_threshold = self.hot_money_params['volume_increase_threshold']

            # 筛选游资活跃股票
            active_stocks = []

            for _, row in top_stocks_df.iterrows():
                # 获取股票代码和名称
                code = row['代码']
                name = row['名称']

                # 获取股票详细数据
                stock_data = self._fetch_stock_data(code)

                if stock_data.empty:
                    continue

                # 计算换手率和成交量增幅
                if len(stock_data) >= 2:
                    # 当日换手率
                    turnover_rate = stock_data.iloc[0]['换手率']

                    # 成交量增幅
                    current_volume = stock_data.iloc[0]['成交量']
                    prev_volume = stock_data.iloc[1]['成交量']
                    volume_increase = current_volume / prev_volume if prev_volume > 0 else 1.0

                    # 价格波动
                    price_change = stock_data.iloc[0]['涨跌幅']

                    # 判断是否符合游资特征
                    if (turnover_rate > turnover_threshold and
                        volume_increase > volume_increase_threshold):

                        # 计算大单成交比例
                        large_order_ratio = self._calculate_large_order_ratio(code)

                        # 创建活跃股票数据
                        active_stock = {
                            'code': code,
                            'name': name,
                            'turnover_rate': turnover_rate,
                            'volume_increase': volume_increase,
                            'price_change': price_change,
                            'large_order_ratio': large_order_ratio,
                            'date': datetime.now().strftime('%Y-%m-%d'),
                            'identified_at': datetime.now().isoformat()
                        }

                        active_stocks.append(active_stock)

            logger.info(f"识别到{len(active_stocks)}只游资活跃股票")
            return active_stocks

        except Exception as e:
            logger.error(f"识别游资活跃股票失败: {str(e)}")
            return []

    def _fetch_top_stocks(self) -> pd.DataFrame:
        """
        获取A股涨幅排行

        Returns:
            top_stocks_df: A股涨幅排行数据
        """
        try:
            # 使用akshare获取A股涨幅排行
            top_stocks_df = ak.stock_zh_a_spot_em()

            # 按涨跌幅排序
            top_stocks_df = top_stocks_df.sort_values('涨跌幅', ascending=False)

            # 只保留前100只股票
            top_stocks_df = top_stocks_df.head(100)

            return top_stocks_df

        except Exception as e:
            logger.error(f"获取A股涨幅排行失败: {str(e)}")
            return pd.DataFrame()

    def _fetch_stock_data(self, code: str) -> pd.DataFrame:
        """
        获取股票详细数据

        Args:
            code: 股票代码

        Returns:
            stock_data: 股票详细数据
        """
        try:
            # 使用akshare获取股票日线数据
            stock_data = ak.stock_zh_a_hist(symbol=code, period="daily", adjust="qfq")

            # 只保留最近30天的数据
            stock_data = stock_data.head(30)

            return stock_data

        except Exception as e:
            logger.error(f"获取股票详细数据失败: {str(e)}, 股票代码: {code}")
            return pd.DataFrame()

    def _calculate_large_order_ratio(self, code: str) -> float:
        """
        计算大单成交比例

        Args:
            code: 股票代码

        Returns:
            large_order_ratio: 大单成交比例
        """
        try:
            # 使用akshare获取股票资金流向数据
            fund_flow_df = ak.stock_individual_fund_flow(stock=code)

            if fund_flow_df.empty:
                return 0.0

            # 获取最新一天的数据
            latest_data = fund_flow_df.iloc[0]

            # 计算大单成交比例
            large_inflow = latest_data['大单流入'] if '大单流入' in latest_data else 0
            large_outflow = latest_data['大单流出'] if '大单流出' in latest_data else 0
            total_inflow = latest_data['流入总额'] if '流入总额' in latest_data else 0
            total_outflow = latest_data['流出总额'] if '流出总额' in latest_data else 0

            total_amount = total_inflow + total_outflow
            large_amount = large_inflow + large_outflow

            large_order_ratio = large_amount / total_amount if total_amount > 0 else 0.0

            return large_order_ratio

        except Exception as e:
            logger.error(f"计算大单成交比例失败: {str(e)}, 股票代码: {code}")
            return 0.0

    def _identify_hot_money_patterns(self, active_stocks: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        识别游资行为模式

        Args:
            active_stocks: 游资活跃股票列表

        Returns:
            patterns: 游资行为模式列表
        """
        try:
            if not active_stocks:
                return []

            # 获取参数
            concentration_threshold = self.hot_money_params['concentration_threshold']
            pattern_recognition_window = self.hot_money_params['pattern_recognition_window']

            # 按行业分组
            industry_groups = {}

            for stock in active_stocks:
                code = stock['code']

                # 获取股票所属行业
                industry = self._get_stock_industry(code)

                if industry not in industry_groups:
                    industry_groups[industry] = []

                industry_groups[industry].append(stock)

            # 识别行业集中模式
            patterns = []

            # 1. 行业集中模式
            for industry, stocks in industry_groups.items():
                if len(stocks) >= 3:  # 至少3只股票才能形成集中模式
                    concentration = len(stocks) / len(active_stocks)

                    if concentration >= concentration_threshold:
                        pattern = {
                            'type': 'industry_concentration',
                            'industry': industry,
                            'stocks': stocks,
                            'concentration': concentration,
                            'date': datetime.now().strftime('%Y-%m-%d'),
                            'identified_at': datetime.now().isoformat(),
                            'description': f'游资在{industry}行业集中活动，共{len(stocks)}只股票，集中度{concentration:.2f}'
                        }

                        patterns.append(pattern)

            # 2. 连续追击模式
            for stock in active_stocks:
                code = stock['code']

                # 获取历史数据
                stock_data = self._fetch_stock_data(code)

                if len(stock_data) >= pattern_recognition_window:
                    # 计算连续上涨天数
                    consecutive_up_days = 0
                    for i in range(min(5, len(stock_data))):
                        if stock_data.iloc[i]['涨跌幅'] > 0:
                            consecutive_up_days += 1
                        else:
                            break

                    # 计算5日累计涨幅
                    cumulative_change = 0
                    for i in range(min(5, len(stock_data))):
                        cumulative_change += stock_data.iloc[i]['涨跌幅']

                    # 判断是否符合连续追击模式
                    if consecutive_up_days >= 3 and cumulative_change > 15:
                        pattern = {
                            'type': 'consecutive_chasing',
                            'stock': stock,
                            'consecutive_up_days': consecutive_up_days,
                            'cumulative_change': cumulative_change,
                            'date': datetime.now().strftime('%Y-%m-%d'),
                            'identified_at': datetime.now().isoformat(),
                            'description': f'游资连续追击{stock["name"]}，已连续上涨{consecutive_up_days}天，累计涨幅{cumulative_change:.2f}%'
                        }

                        patterns.append(pattern)

            # 3. 资金轮动模式
            # 获取历史游资活跃股票
            history = self.hot_money_data.get('history', [])

            if history:
                # 获取最近一次的活跃股票
                last_active_stocks = history[-1].get('active_stocks', [])

                # 计算行业轮动
                if last_active_stocks:
                    # 上次活跃行业
                    last_industries = {}
                    for stock in last_active_stocks:
                        code = stock['code']
                        industry = self._get_stock_industry(code)

                        if industry not in last_industries:
                            last_industries[industry] = 0

                        last_industries[industry] += 1

                    # 当前活跃行业
                    current_industries = {}
                    for stock in active_stocks:
                        code = stock['code']
                        industry = self._get_stock_industry(code)

                        if industry not in current_industries:
                            current_industries[industry] = 0

                        current_industries[industry] += 1

                    # 计算行业变化
                    industry_changes = {}
                    for industry, count in current_industries.items():
                        last_count = last_industries.get(industry, 0)
                        industry_changes[industry] = count - last_count

                    # 识别资金轮动
                    rotation_industries = []
                    for industry, change in industry_changes.items():
                        if change >= 3:  # 至少增加3只股票才算显著轮动
                            rotation_industries.append({
                                'industry': industry,
                                'change': change
                            })

                    if rotation_industries:
                        pattern = {
                            'type': 'capital_rotation',
                            'rotation_industries': rotation_industries,
                            'date': datetime.now().strftime('%Y-%m-%d'),
                            'identified_at': datetime.now().isoformat(),
                            'description': '游资资金轮动，流入多个行业'
                        }

                        patterns.append(pattern)

            logger.info(f"识别到{len(patterns)}种游资行为模式")
            return patterns

        except Exception as e:
            logger.error(f"识别游资行为模式失败: {str(e)}")
            return []

    def _get_stock_industry(self, code: str) -> str:
        """
        获取股票所属行业

        Args:
            code: 股票代码

        Returns:
            industry: 股票所属行业
        """
        try:
            # 使用akshare获取股票所属行业
            stock_info_df = ak.stock_individual_info_em(symbol=code)

            if not stock_info_df.empty:
                # 查找行业信息
                for _, row in stock_info_df.iterrows():
                    if row['item'] == '所属行业':
                        return row['value']

            # 如果没有找到行业信息，尝试从行业板块映射中查找
            for industry, stocks in self.sector_map.items():
                # 获取股票名称
                stock_name = None
                for name, code_map in self.stock_code_map.items():
                    if code_map == code:
                        stock_name = name
                        break

                if stock_name and stock_name in stocks:
                    return industry

            return '未知行业'

        except Exception as e:
            logger.error(f"获取股票所属行业失败: {str(e)}, 股票代码: {code}")
            return '未知行业'

    def _predict_hot_money_targets(self, patterns: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        预测游资目标股票

        Args:
            patterns: 游资行为模式列表

        Returns:
            targets: 游资目标股票列表
        """
        try:
            if not patterns:
                return []

            targets = []

            # 1. 基于行业集中模式预测
            industry_patterns = [p for p in patterns if p['type'] == 'industry_concentration']

            for pattern in industry_patterns:
                industry = pattern['industry']

                # 获取同行业股票
                industry_stocks = self._get_industry_stocks(industry)

                # 已活跃股票代码
                active_codes = [stock['code'] for stock in pattern['stocks']]

                # 筛选潜在目标
                for stock in industry_stocks:
                    if stock['code'] not in active_codes:
                        # 计算技术指标
                        technical_score = self._calculate_technical_score(stock['code'])

                        # 计算资金流向
                        fund_flow_score = self._calculate_fund_flow_score(stock['code'])

                        # 计算总分
                        total_score = 0.6 * technical_score + 0.4 * fund_flow_score

                        if total_score > 70:  # 只选择得分高于70的股票
                            target = {
                                'code': stock['code'],
                                'name': stock['name'],
                                'industry': industry,
                                'technical_score': technical_score,
                                'fund_flow_score': fund_flow_score,
                                'total_score': total_score,
                                'pattern_type': 'industry_concentration',
                                'date': datetime.now().strftime('%Y-%m-%d'),
                                'predicted_at': datetime.now().isoformat(),
                                'description': f'基于{industry}行业集中模式预测的游资目标股票，技术分{technical_score}，资金流分{fund_flow_score}，总分{total_score}'
                            }

                            targets.append(target)

            # 2. 基于资金轮动模式预测
            rotation_patterns = [p for p in patterns if p['type'] == 'capital_rotation']

            for pattern in rotation_patterns:
                rotation_industries = pattern['rotation_industries']

                for item in rotation_industries:
                    industry = item['industry']

                    # 获取同行业股票
                    industry_stocks = self._get_industry_stocks(industry)

                    # 筛选潜在目标
                    for stock in industry_stocks:
                        # 计算技术指标
                        technical_score = self._calculate_technical_score(stock['code'])

                        # 计算资金流向
                        fund_flow_score = self._calculate_fund_flow_score(stock['code'])

                        # 计算总分
                        total_score = 0.5 * technical_score + 0.5 * fund_flow_score

                        if total_score > 75:  # 只选择得分高于75的股票
                            target = {
                                'code': stock['code'],
                                'name': stock['name'],
                                'industry': industry,
                                'technical_score': technical_score,
                                'fund_flow_score': fund_flow_score,
                                'total_score': total_score,
                                'pattern_type': 'capital_rotation',
                                'date': datetime.now().strftime('%Y-%m-%d'),
                                'predicted_at': datetime.now().isoformat(),
                                'description': f'基于资金轮动模式预测的游资目标股票，技术分{technical_score}，资金流分{fund_flow_score}，总分{total_score}'
                            }

                            targets.append(target)

            # 按总分排序
            targets = sorted(targets, key=lambda x: x['total_score'], reverse=True)

            # 去重
            unique_targets = []
            target_codes = set()

            for target in targets:
                if target['code'] not in target_codes:
                    unique_targets.append(target)
                    target_codes.add(target['code'])

            logger.info(f"预测到{len(unique_targets)}只游资目标股票")
            return unique_targets

        except Exception as e:
            logger.error(f"预测游资目标股票失败: {str(e)}")
            return []

    def _get_industry_stocks(self, industry: str) -> List[Dict[str, Any]]:
        """
        获取行业股票

        Args:
            industry: 行业名称

        Returns:
            stocks: 行业股票列表
        """
        try:
            # 使用akshare获取行业股票
            stocks = []

            # 尝试从行业板块映射中获取
            if industry in self.sector_map:
                stock_names = self.sector_map[industry]

                for name in stock_names:
                    if name in self.stock_code_map:
                        code = self.stock_code_map[name]
                        stocks.append({
                            'code': code,
                            'name': name
                        })

                return stocks

            # 如果映射中没有，尝试从akshare获取
            try:
                # 获取申万行业分类
                sw_df = ak.stock_sector_spot(sector="申万一级")

                # 筛选指定行业
                industry_df = sw_df[sw_df['板块名称'] == industry]

                if not industry_df.empty:
                    # 获取行业成分股
                    for _, row in industry_df.iterrows():
                        sector_code = row['板块代码']

                        # 获取成分股
                        constituents_df = ak.stock_sector_detail(sector=sector_code)

                        for _, stock_row in constituents_df.iterrows():
                            code = stock_row['代码']
                            name = stock_row['名称']

                            stocks.append({
                                'code': code,
                                'name': name
                            })

                return stocks

            except Exception as e:
                logger.error(f"从akshare获取行业股票失败: {str(e)}, 行业: {industry}")

            # 如果都失败了，返回空列表
            return []

        except Exception as e:
            logger.error(f"获取行业股票失败: {str(e)}, 行业: {industry}")
            return []

    def _calculate_technical_score(self, code: str) -> float:
        """
        计算技术指标得分

        Args:
            code: 股票代码

        Returns:
            score: 技术指标得分
        """
        try:
            # 获取股票日线数据
            stock_data = self._fetch_stock_data(code)

            if stock_data.empty or len(stock_data) < 20:
                return 0.0

            # 计算技术指标
            # 1. 趋势指标 (30分)
            trend_score = self._calculate_trend_score(stock_data)

            # 2. 动量指标 (30分)
            momentum_score = self._calculate_momentum_score(stock_data)

            # 3. 成交量指标 (20分)
            volume_score = self._calculate_volume_score(stock_data)

            # 4. 波动率指标 (20分)
            volatility_score = self._calculate_volatility_score(stock_data)

            # 计算总分
            total_score = trend_score + momentum_score + volume_score + volatility_score

            return total_score

        except Exception as e:
            logger.error(f"计算技术指标得分失败: {str(e)}, 股票代码: {code}")
            return 0.0

    def _calculate_trend_score(self, stock_data: pd.DataFrame) -> float:
        """
        计算趋势指标得分

        Args:
            stock_data: 股票日线数据

        Returns:
            score: 趋势指标得分
        """
        # 计算5日、10日、20日均线
        stock_data['MA5'] = stock_data['收盘'].rolling(5).mean()
        stock_data['MA10'] = stock_data['收盘'].rolling(10).mean()
        stock_data['MA20'] = stock_data['收盘'].rolling(20).mean()

        # 获取最新数据
        latest = stock_data.iloc[0]

        # 计算均线多头排列得分 (15分)
        ma_score = 0
        if latest['MA5'] > latest['MA10'] > latest['MA20']:
            ma_score = 15
        elif latest['MA5'] > latest['MA10']:
            ma_score = 10
        elif latest['收盘'] > latest['MA5']:
            ma_score = 5

        # 计算价格位置得分 (15分)
        price_position_score = 0

        # 计算20日价格区间
        high_20d = stock_data['高开'].iloc[:20].max()
        low_20d = stock_data['低开'].iloc[:20].min()
        range_20d = high_20d - low_20d

        if range_20d > 0:
            # 当前价格在20日区间的位置 (0-1)
            position = (latest['收盘'] - low_20d) / range_20d

            # 价格在上半区加分，下半区减分
            if position > 0.8:
                price_position_score = 15
            elif position > 0.6:
                price_position_score = 12
            elif position > 0.5:
                price_position_score = 8
            elif position > 0.3:
                price_position_score = 5

        # 总趋势得分
        trend_score = ma_score + price_position_score

        return trend_score

    def _calculate_momentum_score(self, stock_data: pd.DataFrame) -> float:
        """
        计算动量指标得分

        Args:
            stock_data: 股票日线数据

        Returns:
            score: 动量指标得分
        """
        # 计算5日、10日涨跌幅
        change_5d = (stock_data['收盘'].iloc[0] / stock_data['收盘'].iloc[5] - 1) * 100 if len(stock_data) > 5 else 0
        change_10d = (stock_data['收盘'].iloc[0] / stock_data['收盘'].iloc[10] - 1) * 100 if len(stock_data) > 10 else 0

        # 计算5日涨跌幅得分 (15分)
        change_5d_score = 0
        if change_5d > 10:
            change_5d_score = 15
        elif change_5d > 5:
            change_5d_score = 12
        elif change_5d > 3:
            change_5d_score = 8
        elif change_5d > 0:
            change_5d_score = 5

        # 计算10日涨跌幅得分 (15分)
        change_10d_score = 0
        if change_10d > 15:
            change_10d_score = 15
        elif change_10d > 10:
            change_10d_score = 12
        elif change_10d > 5:
            change_10d_score = 8
        elif change_10d > 0:
            change_10d_score = 5

        # 总动量得分
        momentum_score = change_5d_score + change_10d_score

        return momentum_score

    def _calculate_volume_score(self, stock_data: pd.DataFrame) -> float:
        """
        计算成交量指标得分

        Args:
            stock_data: 股票日线数据

        Returns:
            score: 成交量指标得分
        """
        # 计算5日、10日平均成交量
        volume_5d_avg = stock_data['成交量'].iloc[:5].mean()
        volume_10d_avg = stock_data['成交量'].iloc[:10].mean()

        # 计算成交量变化
        volume_change = volume_5d_avg / volume_10d_avg if volume_10d_avg > 0 else 1

        # 计算成交量变化得分 (10分)
        volume_change_score = 0
        if volume_change > 2:
            volume_change_score = 10
        elif volume_change > 1.5:
            volume_change_score = 8
        elif volume_change > 1.2:
            volume_change_score = 5
        elif volume_change > 1:
            volume_change_score = 3

        # 计算最近3日成交量趋势 (10分)
        volume_trend_score = 0
        if len(stock_data) >= 3:
            if stock_data['成交量'].iloc[0] > stock_data['成交量'].iloc[1] > stock_data['成交量'].iloc[2]:
                volume_trend_score = 10
            elif stock_data['成交量'].iloc[0] > stock_data['成交量'].iloc[1]:
                volume_trend_score = 5

        # 总成交量得分
        volume_score = volume_change_score + volume_trend_score

        return volume_score

    def _calculate_volatility_score(self, stock_data: pd.DataFrame) -> float:
        """
        计算波动率指标得分

        Args:
            stock_data: 股票日线数据

        Returns:
            score: 波动率指标得分
        """
        # 计算5日波动率
        returns = stock_data['涨跌幅'].iloc[:5].values / 100
        volatility_5d = np.std(returns) * np.sqrt(252) * 100  # 年化波动率

        # 计算波动率得分 (10分)
        volatility_score = 0
        if 15 < volatility_5d < 40:  # 适中的波动率
            volatility_score = 10
        elif 10 < volatility_5d < 50:
            volatility_score = 8
        elif 5 < volatility_5d < 60:
            volatility_score = 5

        # 计算最近3日振幅 (10分)
        amplitude_score = 0
        if len(stock_data) >= 3:
            # 计算平均振幅
            amplitude_avg = stock_data['振幅'].iloc[:3].mean()

            if 3 < amplitude_avg < 7:  # 适中的振幅
                amplitude_score = 10
            elif 2 < amplitude_avg < 10:
                amplitude_score = 8
            elif 1 < amplitude_avg < 15:
                amplitude_score = 5

        # 总波动率得分
        volatility_score = volatility_score + amplitude_score

        return volatility_score

    def _calculate_fund_flow_score(self, code: str) -> float:
        """
        计算资金流向得分

        Args:
            code: 股票代码

        Returns:
            score: 资金流向得分
        """
        try:
            # 获取股票资金流向数据
            fund_flow_df = ak.stock_individual_fund_flow(stock=code)

            if fund_flow_df.empty or len(fund_flow_df) < 5:
                return 0.0

            # 计算资金流向指标
            # 1. 主力净流入得分 (40分)
            main_inflow_score = self._calculate_main_inflow_score(fund_flow_df)

            # 2. 资金流向趋势得分 (30分)
            flow_trend_score = self._calculate_flow_trend_score(fund_flow_df)

            # 3. 大单比例得分 (30分)
            large_order_score = self._calculate_large_order_score(fund_flow_df)

            # 计算总分
            total_score = main_inflow_score + flow_trend_score + large_order_score

            return total_score

        except Exception as e:
            logger.error(f"计算资金流向得分失败: {str(e)}, 股票代码: {code}")
            return 0.0

    def _calculate_main_inflow_score(self, fund_flow_df: pd.DataFrame) -> float:
        """
        计算主力净流入得分

        Args:
            fund_flow_df: 资金流向数据

        Returns:
            score: 主力净流入得分
        """
        # 获取最新一天的数据
        latest = fund_flow_df.iloc[0]

        # 计算主力净流入
        main_net_inflow = 0
        if '主力净流入' in latest:
            main_net_inflow = latest['主力净流入']
        elif '超大单净流入' in latest and '大单净流入' in latest:
            main_net_inflow = latest['超大单净流入'] + latest['大单净流入']

        # 计算主力净流入占比
        main_net_inflow_ratio = 0
        if '主力净流入占比' in latest:
            main_net_inflow_ratio = latest['主力净流入占比']

        # 计算主力净流入得分
        score = 0

        # 根据净流入金额评分
        if main_net_inflow > 10000000:  # 1000万以上
            score += 20
        elif main_net_inflow > 5000000:  # 500万以上
            score += 15
        elif main_net_inflow > 2000000:  # 200万以上
            score += 10
        elif main_net_inflow > 0:
            score += 5

        # 根据净流入占比评分
        if main_net_inflow_ratio > 10:  # 10%以上
            score += 20
        elif main_net_inflow_ratio > 5:  # 5%以上
            score += 15
        elif main_net_inflow_ratio > 2:  # 2%以上
            score += 10
        elif main_net_inflow_ratio > 0:
            score += 5

        return score

    def _calculate_flow_trend_score(self, fund_flow_df: pd.DataFrame) -> float:
        """
        计算资金流向趋势得分

        Args:
            fund_flow_df: 资金流向数据

        Returns:
            score: 资金流向趋势得分
        """
        # 计算连续净流入天数
        consecutive_inflow_days = 0
        for i in range(min(5, len(fund_flow_df))):
            if '主力净流入' in fund_flow_df.iloc[i] and fund_flow_df.iloc[i]['主力净流入'] > 0:
                consecutive_inflow_days += 1
            elif '超大单净流入' in fund_flow_df.iloc[i] and '大单净流入' in fund_flow_df.iloc[i] and (fund_flow_df.iloc[i]['超大单净流入'] + fund_flow_df.iloc[i]['大单净流入']) > 0:
                consecutive_inflow_days += 1
            else:
                break

        # 计算5日累计净流入
        cumulative_inflow = 0
        for i in range(min(5, len(fund_flow_df))):
            if '主力净流入' in fund_flow_df.iloc[i]:
                cumulative_inflow += fund_flow_df.iloc[i]['主力净流入']
            elif '超大单净流入' in fund_flow_df.iloc[i] and '大单净流入' in fund_flow_df.iloc[i]:
                cumulative_inflow += fund_flow_df.iloc[i]['超大单净流入'] + fund_flow_df.iloc[i]['大单净流入']

        # 计算资金流向趋势得分
        score = 0

        # 根据连续净流入天数评分
        if consecutive_inflow_days >= 3:
            score += 15
        elif consecutive_inflow_days == 2:
            score += 10
        elif consecutive_inflow_days == 1:
            score += 5

        # 根据累计净流入评分
        if cumulative_inflow > 20000000:  # 2000万以上
            score += 15
        elif cumulative_inflow > 10000000:  # 1000万以上
            score += 10
        elif cumulative_inflow > 0:
            score += 5

        return score

    def _calculate_large_order_score(self, fund_flow_df: pd.DataFrame) -> float:
        """
        计算大单比例得分

        Args:
            fund_flow_df: 资金流向数据

        Returns:
            score: 大单比例得分
        """
        # 获取最新一天的数据
        latest = fund_flow_df.iloc[0]

        # 计算大单成交比例
        large_order_ratio = 0

        if '超大单净比' in latest and '大单净比' in latest:
            large_order_ratio = latest['超大单净比'] + latest['大单净比']

        # 计算大单比例得分
        score = 0

        if large_order_ratio > 10:  # 10%以上
            score = 30
        elif large_order_ratio > 5:  # 5%以上
            score = 20
        elif large_order_ratio > 2:  # 2%以上
            score = 10
        elif large_order_ratio > 0:
            score = 5

        return score

    def _update_hot_money_data(self, active_stocks: List[Dict[str, Any]], patterns: List[Dict[str, Any]], targets: List[Dict[str, Any]]):
        """
        更新游资行为数据

        Args:
            active_stocks: 游资活跃股票列表
            patterns: 游资行为模式列表
            targets: 游资目标股票列表
        """
        # 更新游资行为模式
        self.hot_money_data['patterns'] = patterns

        # 更新游资目标股票
        self.hot_money_data['targets'] = targets

        # 更新历史记录
        history_item = {
            'date': datetime.now().strftime('%Y-%m-%d'),
            'active_stocks': active_stocks,
            'patterns': patterns,
            'targets': targets,
            'created_at': datetime.now().isoformat()
        }

        self.hot_money_data['history'].append(history_item)

        # 只保留最近30天的历史记录
        if len(self.hot_money_data['history']) > 30:
            self.hot_money_data['history'] = sorted(
                self.hot_money_data['history'],
                key=lambda x: x['date'],
                reverse=True
            )[:30]

        # 保存数据
        self.data_storage.save('fund_flow_analyzer', 'hot_money_data', self.hot_money_data, StorageLevel.WARM)

        logger.info(f"游资行为数据更新成功，共{len(active_stocks)}只活跃股票，{len(patterns)}种行为模式，{len(targets)}只目标股票")

    def fetch_tiered_fund_flow(self, **kwargs) -> Dict[str, Any]:
        """
        获取五级资金流数据

        Returns:
            result: 获取结果
        """
        try:
            logger.info("开始获取五级资金流数据...")

            # 记录当前时间
            current_time = datetime.now()

            # 获取市场整体资金流向
            market_flow = self._fetch_market_fund_flow()

            # 获取行业板块资金流向
            sector_flow = self._fetch_sector_fund_flow()

            # 获取个股资金流向
            stock_flow = self._fetch_stock_fund_flow()

            if not market_flow and not sector_flow and not stock_flow:
                logger.warning("未获取到五级资金流数据")
                return {
                    'status': 'warning',
                    'message': '未获取到五级资金流数据',
                    'count': 0
                }

            # 更新五级资金流数据
            self._update_tiered_flow_data(market_flow, sector_flow, stock_flow)

            # 生成五级资金流信号
            signals = self._generate_tiered_flow_signals()

            # 更新上次更新时间
            self.last_update_time = current_time

            logger.info(f"五级资金流数据获取成功，生成{len(signals)}个信号")

            return {
                'status': 'success',
                'message': f'五级资金流数据获取成功，生成{len(signals)}个信号',
                'market_flow_count': len(market_flow),
                'sector_flow_count': len(sector_flow),
                'stock_flow_count': len(stock_flow),
                'signals_count': len(signals)
            }

        except Exception as e:
            logger.error(f"获取五级资金流数据失败: {str(e)}")
            return {
                'status': 'error',
                'message': f'获取五级资金流数据失败: {str(e)}',
                'error': str(e)
            }

    def _fetch_market_fund_flow(self) -> Dict[str, Any]:
        """
        获取市场整体资金流向

        Returns:
            market_flow: 市场整体资金流向数据
        """
        try:
            # 使用akshare获取沪深港通资金流向
            hsgt_df = ak.stock_hsgt_fund_flow_summary_em()

            # 使用akshare获取融资融券数据
            margin_df = ak.stock_margin_sse()

            # 创建市场资金流向数据
            market_flow = {
                'date': datetime.now().strftime('%Y-%m-%d'),
                'hsgt': {},
                'north': {},
                'south': {},
                'margin': {},
                'created_at': datetime.now().isoformat()
            }

            # 处理沪深港通数据
            if not hsgt_df.empty:
                # 提取北向资金数据（沪股通和深股通）
                north_df = hsgt_df[hsgt_df['资金方向'] == '北向']

                # 提取南向资金数据（港股通(沪)和港股通(深)）
                south_df = hsgt_df[hsgt_df['资金方向'] == '南向']

                # 处理北向资金数据
                if not north_df.empty:
                    # 提取沪股通数据
                    sh_df = north_df[north_df['板块'] == '沪股通']
                    # 提取深股通数据
                    sz_df = north_df[north_df['板块'] == '深股通']

                    # 计算北向资金总流入
                    north_flow = 0
                    if not sh_df.empty and not sz_df.empty:
                        north_flow = sh_df['资金净流入'].iloc[0] + sz_df['资金净流入'].iloc[0]

                    market_flow['north'] = {
                        'sh_flow': sh_df['资金净流入'].iloc[0] if not sh_df.empty else 0,
                        'sz_flow': sz_df['资金净流入'].iloc[0] if not sz_df.empty else 0,
                        'total_flow': north_flow
                    }

                # 处理南向资金数据
                if not south_df.empty:
                    # 提取港股通(沪)数据
                    hk_sh_df = south_df[south_df['板块'] == '港股通(沪)']
                    # 提取港股通(深)数据
                    hk_sz_df = south_df[south_df['板块'] == '港股通(深)']

                    # 计算南向资金总流入
                    south_flow = 0
                    if not hk_sh_df.empty and not hk_sz_df.empty:
                        south_flow = hk_sh_df['资金净流入'].iloc[0] + hk_sz_df['资金净流入'].iloc[0]

                    market_flow['south'] = {
                        'hk_sh_flow': hk_sh_df['资金净流入'].iloc[0] if not hk_sh_df.empty else 0,
                        'hk_sz_flow': hk_sz_df['资金净流入'].iloc[0] if not hk_sz_df.empty else 0,
                        'total_flow': south_flow
                    }

                # 汇总沪深港通数据
                market_flow['hsgt'] = {
                    'north_flow': market_flow['north'].get('total_flow', 0),
                    'south_flow': market_flow['south'].get('total_flow', 0),
                    'net_flow': market_flow['north'].get('total_flow', 0) + market_flow['south'].get('total_flow', 0)
                }

            # 处理融资融券数据
            if not margin_df.empty:
                market_flow['margin'] = {
                    'margin_balance': margin_df.iloc[0]['融资余额'] if '融资余额' in margin_df.columns else 0,
                    'margin_buy': margin_df.iloc[0]['融资买入额'] if '融资买入额' in margin_df.columns else 0,
                    'margin_repay': margin_df.iloc[0]['融资偿还额'] if '融资偿还额' in margin_df.columns else 0,
                    'short_balance': margin_df.iloc[0]['融券余额'] if '融券余额' in margin_df.columns else 0,
                    'short_sell': margin_df.iloc[0]['融券卖出量'] if '融券卖出量' in margin_df.columns else 0,
                    'short_repay': margin_df.iloc[0]['融券偿还量'] if '融券偿还量' in margin_df.columns else 0,
                    'total_balance': margin_df.iloc[0]['融资融券余额'] if '融资融券余额' in margin_df.columns else 0
                }

            return market_flow

        except Exception as e:
            logger.error(f"获取市场整体资金流向失败: {str(e)}")
            return {}

    def _fetch_sector_fund_flow(self) -> Dict[str, Dict[str, Any]]:
        """
        获取行业板块资金流向

        Returns:
            sector_flow: 行业板块资金流向数据
        """
        try:
            # 使用akshare获取行业资金流向
            sector_df = ak.stock_sector_fund_flow_rank(indicator="今日", sector_type="行业资金流")

            # 创建行业板块资金流向数据
            sector_flow = {}

            # 处理行业资金流向数据
            if not sector_df.empty:
                for _, row in sector_df.iterrows():
                    sector_name = row['名称'] if '名称' in row else '未知行业'

                    sector_flow[sector_name] = {
                        'date': datetime.now().strftime('%Y-%m-%d'),
                        'name': sector_name,
                        'net_inflow': row['净流入(亿)'] if '净流入(亿)' in row else 0,
                        'net_inflow_ratio': row['净流入占比(%)'] if '净流入占比(%)' in row else 0,
                        'super_large_inflow': row['超大单净流入(亿)'] if '超大单净流入(亿)' in row else 0,
                        'large_inflow': row['大单净流入(亿)'] if '大单净流入(亿)' in row else 0,
                        'medium_inflow': row['中单净流入(亿)'] if '中单净流入(亿)' in row else 0,
                        'small_inflow': row['小单净流入(亿)'] if '小单净流入(亿)' in row else 0,
                        'created_at': datetime.now().isoformat()
                    }

            return sector_flow

        except Exception as e:
            logger.error(f"获取行业板块资金流向失败: {str(e)}")
            return {}

    def _fetch_stock_fund_flow(self) -> Dict[str, Dict[str, Any]]:
        """
        获取个股资金流向

        Returns:
            stock_flow: 个股资金流向数据
        """
        try:
            # 使用akshare获取个股资金流向
            stock_df = ak.stock_individual_fund_flow_rank(indicator="今日")

            # 创建个股资金流向数据
            stock_flow = {}

            # 处理个股资金流向数据
            if not stock_df.empty:
                for _, row in stock_df.iterrows():
                    stock_code = row['代码'] if '代码' in row else '000000'
                    stock_name = row['名称'] if '名称' in row else '未知股票'

                    stock_flow[stock_code] = {
                        'date': datetime.now().strftime('%Y-%m-%d'),
                        'code': stock_code,
                        'name': stock_name,
                        'net_inflow': row['净流入(万)'] if '净流入(万)' in row else 0,
                        'net_inflow_ratio': row['净流入占比(%)'] if '净流入占比(%)' in row else 0,
                        'super_large_inflow': row['超大单净流入(万)'] if '超大单净流入(万)' in row else 0,
                        'large_inflow': row['大单净流入(万)'] if '大单净流入(万)' in row else 0,
                        'medium_inflow': row['中单净流入(万)'] if '中单净流入(万)' in row else 0,
                        'small_inflow': row['小单净流入(万)'] if '小单净流入(万)' in row else 0,
                        'created_at': datetime.now().isoformat()
                    }

            return stock_flow

        except Exception as e:
            logger.error(f"获取个股资金流向失败: {str(e)}")
            return {}

    def _update_tiered_flow_data(self, market_flow: Dict[str, Any], sector_flow: Dict[str, Dict[str, Any]], stock_flow: Dict[str, Dict[str, Any]]):
        """
        更新五级资金流数据

        Args:
            market_flow: 市场整体资金流向数据
            sector_flow: 行业板块资金流向数据
            stock_flow: 个股资金流向数据
        """
        # 更新市场整体资金流向
        if market_flow:
            # 获取现有数据
            existing_market_flow = self.tiered_flow_data.get('market', {})

            # 添加新数据
            existing_market_flow[market_flow['date']] = market_flow

            # 只保留最近30天的数据
            if len(existing_market_flow) > 30:
                # 按日期排序
                sorted_dates = sorted(existing_market_flow.keys(), reverse=True)

                # 只保留最近30天
                new_market_flow = {}
                for date in sorted_dates[:30]:
                    new_market_flow[date] = existing_market_flow[date]

                existing_market_flow = new_market_flow

            # 更新数据
            self.tiered_flow_data['market'] = existing_market_flow

        # 更新行业板块资金流向
        if sector_flow:
            # 获取现有数据
            existing_sector_flow = self.tiered_flow_data.get('sector', {})

            # 添加新数据
            current_date = datetime.now().strftime('%Y-%m-%d')
            existing_sector_flow[current_date] = sector_flow

            # 只保留最近30天的数据
            if len(existing_sector_flow) > 30:
                # 按日期排序
                sorted_dates = sorted(existing_sector_flow.keys(), reverse=True)

                # 只保留最近30天
                new_sector_flow = {}
                for date in sorted_dates[:30]:
                    new_sector_flow[date] = existing_sector_flow[date]

                existing_sector_flow = new_sector_flow

            # 更新数据
            self.tiered_flow_data['sector'] = existing_sector_flow

        # 更新个股资金流向
        if stock_flow:
            # 获取现有数据
            existing_stock_flow = self.tiered_flow_data.get('stock', {})

            # 添加新数据
            current_date = datetime.now().strftime('%Y-%m-%d')
            existing_stock_flow[current_date] = stock_flow

            # 只保留最近30天的数据
            if len(existing_stock_flow) > 30:
                # 按日期排序
                sorted_dates = sorted(existing_stock_flow.keys(), reverse=True)

                # 只保留最近30天
                new_stock_flow = {}
                for date in sorted_dates[:30]:
                    new_stock_flow[date] = existing_stock_flow[date]

                existing_stock_flow = new_stock_flow

            # 更新数据
            self.tiered_flow_data['stock'] = existing_stock_flow

        # 保存数据
        self.data_storage.save('fund_flow_analyzer', 'tiered_flow_data', self.tiered_flow_data, StorageLevel.WARM)

        logger.info("五级资金流数据更新成功")

    def _generate_tiered_flow_signals(self) -> List[Dict[str, Any]]:
        """
        生成五级资金流信号

        Returns:
            signals: 五级资金流信号列表
        """
        signals = []

        # 获取参数
        super_large_threshold = self.tiered_flow_params['super_large_threshold']
        large_threshold = self.tiered_flow_params['large_threshold']
        flow_ratio_threshold = self.tiered_flow_params['flow_ratio_threshold']
        signal_window = self.tiered_flow_params['signal_window']

        # 1. 北向资金信号
        north_signal = self._generate_north_flow_signal()
        if north_signal:
            signals.append(north_signal)

        # 2. 行业资金流信号
        sector_signals = self._generate_sector_flow_signals(flow_ratio_threshold)
        signals.extend(sector_signals)

        # 3. 个股资金流信号
        stock_signals = self._generate_stock_flow_signals(super_large_threshold, large_threshold, signal_window)
        signals.extend(stock_signals)

        # 4. 融资融券信号
        margin_signal = self._generate_margin_signal()
        if margin_signal:
            signals.append(margin_signal)

        # 5. 资金流层级联动信号
        tier_signals = self._generate_tier_linkage_signals()
        signals.extend(tier_signals)

        # 更新信号
        self.tiered_flow_data['signals'] = signals

        # 保存数据
        self.data_storage.save('fund_flow_analyzer', 'tiered_flow_data', self.tiered_flow_data, StorageLevel.WARM)

        logger.info(f"生成{len(signals)}个五级资金流信号")

        return signals

    def _generate_north_flow_signal(self) -> Dict[str, Any]:
        """
        生成北向资金信号

        Returns:
            signal: 北向资金信号
        """
        # 获取市场资金流向数据
        market_flow = self.tiered_flow_data.get('market', {})

        if not market_flow:
            return {}

        # 获取最近的数据
        sorted_dates = sorted(market_flow.keys(), reverse=True)

        if not sorted_dates:
            return {}

        latest_date = sorted_dates[0]
        latest_flow = market_flow[latest_date]

        # 检查是否有北向资金数据
        if 'north' not in latest_flow:
            return {}

        north_flow = latest_flow['north']

        # 计算北向资金净流入
        net_flow = north_flow.get('net_flow', 0)

        # 生成信号
        if abs(net_flow) > 50:  # 净流入/流出超过50亿
            signal_type = 'north_large_inflow' if net_flow > 0 else 'north_large_outflow'
            description = f'北向资金大额{"流入" if net_flow > 0 else "流出"}，净{"流入" if net_flow > 0 else "流出"}{abs(net_flow):.2f}亿元'

            return {
                'type': signal_type,
                'date': latest_date,
                'strength': abs(net_flow) / 50,  # 相对于50亿的强度
                'description': description,
                'data': north_flow,
                'created_at': datetime.now().isoformat()
            }

        return {}

    def _generate_sector_flow_signals(self, flow_ratio_threshold: float) -> List[Dict[str, Any]]:
        """
        生成行业资金流信号

        Args:
            flow_ratio_threshold: 流入流出比例阈值

        Returns:
            signals: 行业资金流信号列表
        """
        signals = []

        # 获取行业资金流向数据
        sector_flow = self.tiered_flow_data.get('sector', {})

        if not sector_flow:
            return signals

        # 获取最近的数据
        sorted_dates = sorted(sector_flow.keys(), reverse=True)

        if not sorted_dates:
            return signals

        latest_date = sorted_dates[0]
        latest_sectors = sector_flow[latest_date]

        # 筛选资金流入/流出显著的行业
        inflow_sectors = []
        outflow_sectors = []

        for sector_name, data in latest_sectors.items():
            net_inflow = data.get('net_inflow', 0)
            net_inflow_ratio = data.get('net_inflow_ratio', 0)

            # 资金流入显著
            if net_inflow > 1 and net_inflow_ratio > flow_ratio_threshold:
                inflow_sectors.append({
                    'name': sector_name,
                    'net_inflow': net_inflow,
                    'net_inflow_ratio': net_inflow_ratio
                })

            # 资金流出显著
            elif net_inflow < -1 and net_inflow_ratio < -flow_ratio_threshold:
                outflow_sectors.append({
                    'name': sector_name,
                    'net_inflow': net_inflow,
                    'net_inflow_ratio': net_inflow_ratio
                })

        # 按资金流入/流出排序
        inflow_sectors = sorted(inflow_sectors, key=lambda x: x['net_inflow'], reverse=True)
        outflow_sectors = sorted(outflow_sectors, key=lambda x: x['net_inflow'])

        # 生成资金流入信号
        if inflow_sectors:
            top_inflow_sectors = inflow_sectors[:3]  # 取前3个

            signal = {
                'type': 'sector_large_inflow',
                'date': latest_date,
                'strength': sum(s['net_inflow'] for s in top_inflow_sectors) / 3,  # 平均流入强度
                'description': '行业资金大额流入',
                'sectors': top_inflow_sectors,
                'created_at': datetime.now().isoformat()
            }

            signals.append(signal)

        # 生成资金流出信号
        if outflow_sectors:
            top_outflow_sectors = outflow_sectors[:3]  # 取前3个

            signal = {
                'type': 'sector_large_outflow',
                'date': latest_date,
                'strength': abs(sum(s['net_inflow'] for s in top_outflow_sectors) / 3),  # 平均流出强度
                'description': '行业资金大额流出',
                'sectors': top_outflow_sectors,
                'created_at': datetime.now().isoformat()
            }

            signals.append(signal)

        return signals

    def _generate_stock_flow_signals(self, super_large_threshold: float, large_threshold: float, signal_window: int) -> List[Dict[str, Any]]:
        """
        生成个股资金流信号

        Args:
            super_large_threshold: 超大单阈值
            large_threshold: 大单阈值
            signal_window: 信号窗口

        Returns:
            signals: 个股资金流信号列表
        """
        signals = []

        # 获取个股资金流向数据
        stock_flow = self.tiered_flow_data.get('stock', {})

        if not stock_flow:
            return signals

        # 获取最近的数据
        sorted_dates = sorted(stock_flow.keys(), reverse=True)

        if not sorted_dates or len(sorted_dates) < signal_window:
            return signals

        # 获取最近几天的数据
        recent_dates = sorted_dates[:signal_window]

        # 计算个股累计资金流向
        cumulative_flow = {}

        for date in recent_dates:
            daily_stocks = stock_flow[date]

            for code, data in daily_stocks.items():
                if code not in cumulative_flow:
                    cumulative_flow[code] = {
                        'code': code,
                        'name': data.get('name', ''),
                        'net_inflow': 0,
                        'super_large_inflow': 0,
                        'large_inflow': 0,
                        'medium_inflow': 0,
                        'small_inflow': 0,
                        'days': 0
                    }

                # 累加资金流向
                cumulative_flow[code]['net_inflow'] += data.get('net_inflow', 0)
                cumulative_flow[code]['super_large_inflow'] += data.get('super_large_inflow', 0)
                cumulative_flow[code]['large_inflow'] += data.get('large_inflow', 0)
                cumulative_flow[code]['medium_inflow'] += data.get('medium_inflow', 0)
                cumulative_flow[code]['small_inflow'] += data.get('small_inflow', 0)
                cumulative_flow[code]['days'] += 1

        # 筛选资金流入/流出显著的个股
        inflow_stocks = []
        outflow_stocks = []

        for code, data in cumulative_flow.items():
            # 只考虑数据完整的个股
            if data['days'] < signal_window:
                continue

            # 超大单资金流入显著
            if data['super_large_inflow'] > super_large_threshold:
                inflow_stocks.append({
                    'code': code,
                    'name': data['name'],
                    'net_inflow': data['net_inflow'],
                    'super_large_inflow': data['super_large_inflow'],
                    'large_inflow': data['large_inflow']
                })

            # 超大单资金流出显著
            elif data['super_large_inflow'] < -super_large_threshold:
                outflow_stocks.append({
                    'code': code,
                    'name': data['name'],
                    'net_inflow': data['net_inflow'],
                    'super_large_inflow': data['super_large_inflow'],
                    'large_inflow': data['large_inflow']
                })

        # 按资金流入/流出排序
        inflow_stocks = sorted(inflow_stocks, key=lambda x: x['super_large_inflow'], reverse=True)
        outflow_stocks = sorted(outflow_stocks, key=lambda x: x['super_large_inflow'])

        # 生成超大单资金流入信号
        if inflow_stocks:
            top_inflow_stocks = inflow_stocks[:5]  # 取前5个

            signal = {
                'type': 'stock_super_large_inflow',
                'date': sorted_dates[0],
                'window': signal_window,
                'strength': sum(s['super_large_inflow'] for s in top_inflow_stocks) / (5 * super_large_threshold),
                'description': '个股超大单资金流入',
                'stocks': top_inflow_stocks,
                'created_at': datetime.now().isoformat()
            }

            signals.append(signal)

        # 生成超大单资金流出信号
        if outflow_stocks:
            top_outflow_stocks = outflow_stocks[:5]  # 取前5个

            signal = {
                'type': 'stock_super_large_outflow',
                'date': sorted_dates[0],
                'window': signal_window,
                'strength': abs(sum(s['super_large_inflow'] for s in top_outflow_stocks) / (5 * super_large_threshold)),
                'description': '个股超大单资金流出',
                'stocks': top_outflow_stocks,
                'created_at': datetime.now().isoformat()
            }

            signals.append(signal)

        # 生成大小单背离信号
        divergence_stocks = []

        for code, data in cumulative_flow.items():
            # 只考虑数据完整的个股
            if data['days'] < signal_window:
                continue

            # 大单流出但小单流入
            if data['large_inflow'] < -large_threshold and data['small_inflow'] > large_threshold:
                divergence_stocks.append({
                    'code': code,
                    'name': data['name'],
                    'large_inflow': data['large_inflow'],
                    'small_inflow': data['small_inflow'],
                    'divergence': abs(data['small_inflow'] / data['large_inflow']) if data['large_inflow'] != 0 else 0
                })

        # 按背离程度排序
        divergence_stocks = sorted(divergence_stocks, key=lambda x: x['divergence'], reverse=True)

        # 生成大小单背离信号
        if divergence_stocks:
            top_divergence_stocks = divergence_stocks[:5]  # 取前5个

            signal = {
                'type': 'stock_large_small_divergence',
                'date': sorted_dates[0],
                'window': signal_window,
                'strength': sum(s['divergence'] for s in top_divergence_stocks) / 5,
                'description': '个股大小单资金背离',
                'stocks': top_divergence_stocks,
                'created_at': datetime.now().isoformat()
            }

            signals.append(signal)

        return signals

    def _generate_margin_signal(self) -> Dict[str, Any]:
        """
        生成融资融券信号

        Returns:
            signal: 融资融券信号
        """
        # 获取市场资金流向数据
        market_flow = self.tiered_flow_data.get('market', {})

        if not market_flow:
            return {}

        # 获取最近的数据
        sorted_dates = sorted(market_flow.keys(), reverse=True)

        if len(sorted_dates) < 2:
            return {}

        latest_date = sorted_dates[0]
        prev_date = sorted_dates[1]

        latest_flow = market_flow[latest_date]
        prev_flow = market_flow[prev_date]

        # 检查是否有融资融券数据
        if 'margin' not in latest_flow or 'margin' not in prev_flow:
            return {}

        latest_margin = latest_flow['margin']
        prev_margin = prev_flow['margin']

        # 计算融资余额变化
        financing_balance = latest_margin.get('financing_balance', 0)
        prev_financing_balance = prev_margin.get('financing_balance', 0)
        financing_change = financing_balance - prev_financing_balance

        # 计算融券余额变化
        securities_balance = latest_margin.get('securities_balance', 0)
        prev_securities_balance = prev_margin.get('securities_balance', 0)
        securities_change = securities_balance - prev_securities_balance

        # 生成信号
        if abs(financing_change) > 100000000 or abs(securities_change) > 50000000:  # 融资变化超过1亿或融券变化超过5000万
            signal_type = ''
            description = ''

            if financing_change > 100000000:
                signal_type = 'margin_financing_increase'
                description = f'融资余额大幅增加，增加{financing_change/100000000:.2f}亿元'
            elif financing_change < -100000000:
                signal_type = 'margin_financing_decrease'
                description = f'融资余额大幅减少，减少{abs(financing_change)/100000000:.2f}亿元'
            elif securities_change > 50000000:
                signal_type = 'margin_securities_increase'
                description = f'融券余额大幅增加，增加{securities_change/100000000:.2f}亿元'
            elif securities_change < -50000000:
                signal_type = 'margin_securities_decrease'
                description = f'融券余额大幅减少，减少{abs(securities_change)/100000000:.2f}亿元'

            return {
                'type': signal_type,
                'date': latest_date,
                'strength': max(abs(financing_change) / 100000000, abs(securities_change) / 50000000),
                'description': description,
                'data': {
                    'financing_balance': financing_balance,
                    'financing_change': financing_change,
                    'securities_balance': securities_balance,
                    'securities_change': securities_change
                },
                'created_at': datetime.now().isoformat()
            }

        return {}

    def _generate_tier_linkage_signals(self) -> List[Dict[str, Any]]:
        """
        生成资金流层级联动信号

        Returns:
            signals: 资金流层级联动信号列表
        """
        signals = []

        # 获取市场资金流向数据
        market_flow = self.tiered_flow_data.get('market', {})

        # 获取行业资金流向数据
        sector_flow = self.tiered_flow_data.get('sector', {})

        # 获取个股资金流向数据
        stock_flow = self.tiered_flow_data.get('stock', {})

        if not market_flow or not sector_flow or not stock_flow:
            return signals

        # 获取最近的数据
        market_dates = sorted(market_flow.keys(), reverse=True)
        sector_dates = sorted(sector_flow.keys(), reverse=True)
        stock_dates = sorted(stock_flow.keys(), reverse=True)

        if not market_dates or not sector_dates or not stock_dates:
            return signals

        latest_date = market_dates[0]

        # 检查是否有北向资金数据
        if latest_date in market_flow and 'north' in market_flow[latest_date]:
            north_flow = market_flow[latest_date]['north']
            north_net_flow = north_flow.get('net_flow', 0)

            # 北向资金流入 + 行业资金流入联动
            if north_net_flow > 20 and latest_date in sector_flow:  # 北向资金净流入超过20亿
                latest_sectors = sector_flow[latest_date]

                # 筛选资金流入显著的行业
                inflow_sectors = []

                for sector_name, data in latest_sectors.items():
                    net_inflow = data.get('net_inflow', 0)

                    if net_inflow > 1:  # 净流入超过1亿
                        inflow_sectors.append({
                            'name': sector_name,
                            'net_inflow': net_inflow
                        })

                # 按资金流入排序
                inflow_sectors = sorted(inflow_sectors, key=lambda x: x['net_inflow'], reverse=True)

                if inflow_sectors:
                    top_sectors = inflow_sectors[:3]  # 取前3个

                    signal = {
                        'type': 'north_sector_linkage',
                        'date': latest_date,
                        'strength': north_net_flow / 20,  # 相对于20亿的强度
                        'description': f'北向资金流入联动行业资金流入，北向净流入{north_net_flow:.2f}亿元',
                        'north_flow': north_net_flow,
                        'sectors': top_sectors,
                        'created_at': datetime.now().isoformat()
                    }

                    signals.append(signal)

            # 北向资金流入 + 个股超大单流入联动
            if north_net_flow > 20 and latest_date in stock_flow:  # 北向资金净流入超过20亿
                latest_stocks = stock_flow[latest_date]

                # 筛选超大单资金流入显著的个股
                inflow_stocks = []

                for code, data in latest_stocks.items():
                    super_large_inflow = data.get('super_large_inflow', 0)

                    if super_large_inflow > 1000:  # 超大单净流入超过1000万
                        inflow_stocks.append({
                            'code': code,
                            'name': data.get('name', ''),
                            'super_large_inflow': super_large_inflow
                        })

                # 按超大单资金流入排序
                inflow_stocks = sorted(inflow_stocks, key=lambda x: x['super_large_inflow'], reverse=True)

                if inflow_stocks:
                    top_stocks = inflow_stocks[:5]  # 取前5个

                    signal = {
                        'type': 'north_stock_linkage',
                        'date': latest_date,
                        'strength': north_net_flow / 20,  # 相对于20亿的强度
                        'description': f'北向资金流入联动个股超大单流入，北向净流入{north_net_flow:.2f}亿元',
                        'north_flow': north_net_flow,
                        'stocks': top_stocks,
                        'created_at': datetime.now().isoformat()
                    }

                    signals.append(signal)

        return signals

    def get_hot_money_behavior_analysis(self) -> Dict[str, Any]:
        """
        获取游资行为分析

        Returns:
            analysis: 游资行为分析
        """
        try:
            patterns = self.hot_money_data.get('patterns', [])
            targets = self.hot_money_data.get('targets', [])
            history = self.hot_money_data.get('history', [])

            if not patterns and not targets and not history:
                return {
                    'status': 'warning',
                    'message': '没有游资行为数据',
                    'data': None
                }

            # 创建分析结果
            analysis = {
                'date': datetime.now().strftime('%Y-%m-%d'),
                'patterns': patterns,
                'targets': targets,
                'history_count': len(history),
                'active_industries': self._analyze_hot_money_industries(patterns),
                'target_recommendations': self._get_top_hot_money_targets(targets),
                'pattern_summary': self._summarize_hot_money_patterns(patterns)
            }

            return {
                'status': 'success',
                'message': '游资行为分析完成',
                'data': analysis
            }

        except Exception as e:
            logger.error(f"获取游资行为分析失败: {str(e)}")
            return {
                'status': 'error',
                'message': f'获取游资行为分析失败: {str(e)}',
                'error': str(e),
                'data': None
            }

    def _analyze_hot_money_industries(self, patterns: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        分析游资活跃行业

        Args:
            patterns: 游资行为模式列表

        Returns:
            active_industries: 游资活跃行业列表
        """
        # 筛选行业集中模式
        industry_patterns = [p for p in patterns if p['type'] == 'industry_concentration']

        if not industry_patterns:
            return []

        # 提取行业信息
        industries = []

        for pattern in industry_patterns:
            industry = pattern['industry']
            concentration = pattern['concentration']
            stocks = pattern['stocks']

            industries.append({
                'name': industry,
                'concentration': concentration,
                'stocks_count': len(stocks),
                'stocks': stocks
            })

        # 按集中度排序
        industries = sorted(industries, key=lambda x: x['concentration'], reverse=True)

        return industries

    def _get_top_hot_money_targets(self, targets: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        获取游资目标股票推荐

        Args:
            targets: 游资目标股票列表

        Returns:
            recommendations: 游资目标股票推荐列表
        """
        if not targets:
            return []

        # 按总分排序
        sorted_targets = sorted(targets, key=lambda x: x['total_score'], reverse=True)

        # 取前10个
        top_targets = sorted_targets[:10]

        return top_targets

    def _summarize_hot_money_patterns(self, patterns: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        总结游资行为模式

        Args:
            patterns: 游资行为模式列表

        Returns:
            summary: 游资行为模式总结
        """
        if not patterns:
            return {}

        # 统计各类模式数量
        pattern_types = {}

        for pattern in patterns:
            pattern_type = pattern['type']

            if pattern_type not in pattern_types:
                pattern_types[pattern_type] = 0

            pattern_types[pattern_type] += 1

        # 创建总结
        summary = {
            'pattern_types': pattern_types,
            'total_count': len(patterns),
            'latest_pattern': patterns[0] if patterns else None
        }

        return summary

    def get_tiered_fund_flow_analysis(self) -> Dict[str, Any]:
        """
        获取五级资金流分析

        Returns:
            analysis: 五级资金流分析
        """
        try:
            market_flow = self.tiered_flow_data.get('market', {})
            sector_flow = self.tiered_flow_data.get('sector', {})
            stock_flow = self.tiered_flow_data.get('stock', {})
            signals = self.tiered_flow_data.get('signals', [])

            if not market_flow and not sector_flow and not stock_flow:
                return {
                    'status': 'warning',
                    'message': '没有五级资金流数据',
                    'data': None
                }

            # 创建分析结果
            analysis = {
                'date': datetime.now().strftime('%Y-%m-%d'),
                'market_summary': self._analyze_market_flow(market_flow),
                'sector_summary': self._analyze_sector_flow(sector_flow),
                'stock_summary': self._analyze_stock_flow(stock_flow),
                'signals': signals,
                'signal_summary': self._summarize_fund_flow_signals(signals)
            }

            return {
                'status': 'success',
                'message': '五级资金流分析完成',
                'data': analysis
            }

        except Exception as e:
            logger.error(f"获取五级资金流分析失败: {str(e)}")
            return {
                'status': 'error',
                'message': f'获取五级资金流分析失败: {str(e)}',
                'error': str(e),
                'data': None
            }

    def _analyze_market_flow(self, market_flow: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """
        分析市场整体资金流向

        Args:
            market_flow: 市场整体资金流向数据

        Returns:
            summary: 市场整体资金流向分析
        """
        if not market_flow:
            return {}

        # 获取最近的数据
        sorted_dates = sorted(market_flow.keys(), reverse=True)

        if not sorted_dates:
            return {}

        latest_date = sorted_dates[0]
        latest_flow = market_flow[latest_date]

        # 创建分析结果
        summary = {
            'date': latest_date,
            'north': latest_flow.get('north', {}),
            'hsgt': latest_flow.get('hsgt', {}),
            'margin': latest_flow.get('margin', {})
        }

        # 计算北向资金趋势
        if len(sorted_dates) >= 5:
            north_trend = []

            for i in range(min(5, len(sorted_dates))):
                date = sorted_dates[i]
                flow = market_flow[date]

                if 'north' in flow:
                    north_trend.append({
                        'date': date,
                        'net_flow': flow['north'].get('net_flow', 0)
                    })

            summary['north_trend'] = north_trend

        return summary

    def _analyze_sector_flow(self, sector_flow: Dict[str, Dict[str, Dict[str, Any]]]) -> Dict[str, Any]:
        """
        分析行业板块资金流向

        Args:
            sector_flow: 行业板块资金流向数据

        Returns:
            summary: 行业板块资金流向分析
        """
        if not sector_flow:
            return {}

        # 获取最近的数据
        sorted_dates = sorted(sector_flow.keys(), reverse=True)

        if not sorted_dates:
            return {}

        latest_date = sorted_dates[0]
        latest_sectors = sector_flow[latest_date]

        # 筛选资金流入/流出显著的行业
        inflow_sectors = []
        outflow_sectors = []

        for sector_name, data in latest_sectors.items():
            net_inflow = data.get('net_inflow', 0)

            if net_inflow > 1:  # 净流入超过1亿
                inflow_sectors.append({
                    'name': sector_name,
                    'net_inflow': net_inflow,
                    'net_inflow_ratio': data.get('net_inflow_ratio', 0),
                    'super_large_inflow': data.get('super_large_inflow', 0),
                    'large_inflow': data.get('large_inflow', 0)
                })
            elif net_inflow < -1:  # 净流出超过1亿
                outflow_sectors.append({
                    'name': sector_name,
                    'net_inflow': net_inflow,
                    'net_inflow_ratio': data.get('net_inflow_ratio', 0),
                    'super_large_inflow': data.get('super_large_inflow', 0),
                    'large_inflow': data.get('large_inflow', 0)
                })

        # 按资金流入/流出排序
        inflow_sectors = sorted(inflow_sectors, key=lambda x: x['net_inflow'], reverse=True)
        outflow_sectors = sorted(outflow_sectors, key=lambda x: x['net_inflow'])

        # 创建分析结果
        summary = {
            'date': latest_date,
            'top_inflow_sectors': inflow_sectors[:5],  # 取前5个
            'top_outflow_sectors': outflow_sectors[:5],  # 取前5个
            'total_sectors': len(latest_sectors),
            'inflow_sectors_count': len(inflow_sectors),
            'outflow_sectors_count': len(outflow_sectors)
        }

        return summary

    def _analyze_stock_flow(self, stock_flow: Dict[str, Dict[str, Dict[str, Any]]]) -> Dict[str, Any]:
        """
        分析个股资金流向

        Args:
            stock_flow: 个股资金流向数据

        Returns:
            summary: 个股资金流向分析
        """
        if not stock_flow:
            return {}

        # 获取最近的数据
        sorted_dates = sorted(stock_flow.keys(), reverse=True)

        if not sorted_dates:
            return {}

        latest_date = sorted_dates[0]
        latest_stocks = stock_flow[latest_date]

        # 筛选超大单资金流入/流出显著的个股
        inflow_stocks = []
        outflow_stocks = []

        for code, data in latest_stocks.items():
            super_large_inflow = data.get('super_large_inflow', 0)

            if super_large_inflow > 1000:  # 超大单净流入超过1000万
                inflow_stocks.append({
                    'code': code,
                    'name': data.get('name', ''),
                    'super_large_inflow': super_large_inflow,
                    'large_inflow': data.get('large_inflow', 0),
                    'net_inflow': data.get('net_inflow', 0)
                })
            elif super_large_inflow < -1000:  # 超大单净流出超过1000万
                outflow_stocks.append({
                    'code': code,
                    'name': data.get('name', ''),
                    'super_large_inflow': super_large_inflow,
                    'large_inflow': data.get('large_inflow', 0),
                    'net_inflow': data.get('net_inflow', 0)
                })

        # 按超大单资金流入/流出排序
        inflow_stocks = sorted(inflow_stocks, key=lambda x: x['super_large_inflow'], reverse=True)
        outflow_stocks = sorted(outflow_stocks, key=lambda x: x['super_large_inflow'])

        # 创建分析结果
        summary = {
            'date': latest_date,
            'top_inflow_stocks': inflow_stocks[:10],  # 取前10个
            'top_outflow_stocks': outflow_stocks[:10],  # 取前10个
            'total_stocks': len(latest_stocks),
            'inflow_stocks_count': len(inflow_stocks),
            'outflow_stocks_count': len(outflow_stocks)
        }

        return summary

    def _summarize_fund_flow_signals(self, signals: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        总结资金流信号

        Args:
            signals: 资金流信号列表

        Returns:
            summary: 资金流信号总结
        """
        if not signals:
            return {}

        # 统计各类信号数量
        signal_types = {}

        for signal in signals:
            signal_type = signal['type']

            if signal_type not in signal_types:
                signal_types[signal_type] = 0

            signal_types[signal_type] += 1

        # 创建总结
        summary = {
            'signal_types': signal_types,
            'total_count': len(signals),
            'latest_signal': signals[0] if signals else None
        }

        return summary

    def generate_fund_flow_report(self, **kwargs) -> Dict[str, Any]:
        """
        生成资金流分析报告

        Returns:
            result: 生成结果
        """
        try:
            logger.info("开始生成资金流分析报告...")

            # 获取北向资金分析
            north_analysis = self.get_northbound_flow_analysis(days=30)

            # 获取游资行为分析
            hot_money_analysis = self.get_hot_money_behavior_analysis()

            # 获取五级资金流分析
            tiered_flow_analysis = self.get_tiered_fund_flow_analysis()

            # 创建报告
            report = {
                'date': datetime.now().strftime('%Y-%m-%d'),
                'north_analysis': north_analysis.get('data', {}),
                'hot_money_analysis': hot_money_analysis.get('data', {}),
                'tiered_flow_analysis': tiered_flow_analysis.get('data', {}),
                'created_at': datetime.now().isoformat()
            }

            # 保存报告
            self.data_storage.save('fund_flow_analyzer', 'fund_flow_report', report, StorageLevel.WARM)

            logger.info("资金流分析报告生成成功")

            return {
                'status': 'success',
                'message': '资金流分析报告生成成功',
                'report': report
            }

        except Exception as e:
            logger.error(f"生成资金流分析报告失败: {str(e)}")
            return {
                'status': 'error',
                'message': f'生成资金流分析报告失败: {str(e)}',
                'error': str(e)
            }
