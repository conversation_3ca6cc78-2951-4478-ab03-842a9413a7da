"""
测试报告生成功能
"""

import os
import time
from datetime import datetime

# 创建必要的目录
os.makedirs('logs', exist_ok=True)
os.makedirs('data/news_monitor', exist_ok=True)

# 导入新闻监控器
from news_monitor import NewsMonitor

def test_report_generation():
    """测试报告生成功能"""
    print("\n测试报告生成功能...")
    
    # 创建新闻监控器
    monitor = NewsMonitor()
    
    # 测试生成每日报告
    print("\n测试生成每日报告...")
    report_file = monitor.generate_daily_report()
    
    if report_file:
        print(f"报告生成成功: {report_file}")
        
        # 读取报告内容
        with open(report_file, 'r', encoding='utf-8') as f:
            report_content = f.read()
        
        # 打印报告前500个字符
        print("\n报告预览:")
        print(report_content[:500] + "...")
    else:
        print("报告生成失败")
    
    return report_file

if __name__ == "__main__":
    print("开始测试报告生成功能...")
    
    # 测试报告生成功能
    report_file = test_report_generation()
    
    print("\n测试完成!")
