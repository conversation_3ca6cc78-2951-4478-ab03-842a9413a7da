"""
数据库客户端基类

定义数据库客户端的通用接口
"""

import logging
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Union

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('db_client')

class DBClient(ABC):
    """数据库客户端基类"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化数据库客户端
        
        Args:
            config: 配置信息
        """
        self.config = config
        self.connection = None
    
    @abstractmethod
    def connect(self) -> bool:
        """
        连接数据库
        
        Returns:
            连接结果
        """
        pass
    
    @abstractmethod
    def disconnect(self) -> bool:
        """
        断开数据库连接
        
        Returns:
            断开结果
        """
        pass
    
    @abstractmethod
    def is_connected(self) -> bool:
        """
        检查数据库连接状态
        
        Returns:
            连接状态
        """
        pass
    
    @abstractmethod
    def query(self, data_type: str, query_params: Dict[str, Any], options: Optional[Dict[str, Any]] = None) -> Any:
        """
        查询数据
        
        Args:
            data_type: 数据类型
            query_params: 查询参数
            options: 选项参数
        
        Returns:
            查询结果
        """
        pass
    
    @abstractmethod
    def save(self, data_type: str, data: Any, options: Optional[Dict[str, Any]] = None) -> Any:
        """
        保存数据
        
        Args:
            data_type: 数据类型
            data: 要保存的数据
            options: 选项参数
        
        Returns:
            保存结果
        """
        pass
    
    @abstractmethod
    def delete(self, data_type: str, query_params: Dict[str, Any], options: Optional[Dict[str, Any]] = None) -> bool:
        """
        删除数据
        
        Args:
            data_type: 数据类型
            query_params: 查询参数
            options: 选项参数
        
        Returns:
            删除结果
        """
        pass
    
    def _log_error(self, message: str, error: Exception) -> None:
        """
        记录错误日志
        
        Args:
            message: 错误消息
            error: 异常对象
        """
        logger.error(f"{message}: {str(error)}")
    
    def _log_info(self, message: str) -> None:
        """
        记录信息日志
        
        Args:
            message: 信息消息
        """
        logger.info(message)
    
    def _log_debug(self, message: str) -> None:
        """
        记录调试日志
        
        Args:
            message: 调试消息
        """
        logger.debug(message)
