"""
政策数据源模块
提供政策和新闻相关数据
"""

import os
import pandas as pd
from datetime import datetime, timedelta
import time
import requests
from bs4 import BeautifulSoup
import akshare as ak
import logging
import re
import json

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/policy_data.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('policy_data')

class PolicyDataSource:
    """政策数据源类"""

    def __init__(self, cache_dir='data/cache/policy'):
        """
        初始化政策数据源

        Args:
            cache_dir (str): 缓存目录
        """
        self.cache_dir = cache_dir
        os.makedirs(cache_dir, exist_ok=True)
        logger.info(f"PolicyDataSource initialized with cache_dir: {cache_dir}")

        # 设置请求头
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Connection': 'keep-alive',
            'Cache-Control': 'max-age=0'
        }

    def _check_cache(self, cache_file, cache_days):
        """检查缓存是否有效"""
        if os.path.exists(cache_file):
            file_time = datetime.fromtimestamp(os.path.getmtime(cache_file))
            if datetime.now() - file_time < timedelta(days=cache_days):
                logger.info(f"Using cache: {cache_file}")
                try:
                    return pd.read_csv(cache_file)
                except Exception as e:
                    logger.warning(f"Error reading cache: {str(e)}")
        return None

    def _save_cache(self, df, cache_file):
        """保存数据到缓存"""
        if df is not None and not df.empty:
            try:
                df.to_csv(cache_file, index=False)
                logger.info(f"Saved to cache: {cache_file}")
            except Exception as e:
                logger.warning(f"Error saving cache: {str(e)}")

    # 政策数据获取方法
    def get_gov_policy(self, page=1, limit=20, use_cache=True, cache_days=1):
        """
        获取国务院政策文件库政策

        Args:
            page (int): 页码
            limit (int): 每页条数
            use_cache (bool): 是否使用缓存
            cache_days (int): 缓存有效期（天）

        Returns:
            pandas.DataFrame: 政策数据
        """
        # 生成缓存文件名
        cache_file = os.path.join(self.cache_dir, f'gov_policy_p{page}_l{limit}.csv')

        # 检查缓存
        if use_cache:
            cached_data = self._check_cache(cache_file, cache_days)
            if cached_data is not None:
                return cached_data

        try:
            logger.info(f"Fetching government policy, page: {page}, limit: {limit}")
            start_time = time.time()

            # 构建URL
            url = f"https://sousuo.www.gov.cn/search-gov/data?t=zhengcelibrary_gw&q=&timetype=&mintime=&maxtime=&sort=pubtime&sortType=1&searchfield=title:content:summary&pcodeJiguan=&childtype=&subchildtype=&tsbq=&pubtimeyear=&puborg=&pcodeYear=&pcodeNum=&filetype=&p={page}&n={limit}&inpro=&bmfl=&dup=&orpro=&bmpubyear="

            # 发送请求
            response = requests.get(url, headers=self.headers, timeout=10)
            response.raise_for_status()

            # 解析JSON
            data = response.json()

            # 提取政策列表
            policy_list = []

            if 'searchVO' in data and 'catMap' in data['searchVO'] and 'gongwen' in data['searchVO']['catMap']:
                items = data['searchVO']['catMap']['gongwen']['listVO']

                for item in items:
                    # 提取标题和链接
                    title = item.get('title', '')
                    link = item.get('url', '')

                    # 提取日期
                    publish_date = item.get('pubtimeStr', '')

                    # 提取文号
                    pcode = item.get('pcode', '')

                    # 添加到列表
                    policy_list.append({
                        'title': title,
                        'url': link,
                        'publish_date': publish_date,
                        'pcode': pcode,
                        'source': '国务院'
                    })

            # 创建DataFrame
            policy_df = pd.DataFrame(policy_list) if policy_list else pd.DataFrame(columns=['title', 'url', 'publish_date', 'pcode', 'source'])

            end_time = time.time()
            logger.info(f"Government policy fetched successfully in {end_time - start_time:.2f}s. Shape: {policy_df.shape}")

            # 保存缓存
            if use_cache:
                self._save_cache(policy_df, cache_file)

            return policy_df

        except Exception as e:
            logger.error(f"Error fetching government policy: {str(e)}")
            return pd.DataFrame(columns=['title', 'url', 'publish_date', 'pcode', 'source'])

    def get_ndrc_policy(self, page=1, limit=20, use_cache=True, cache_days=1):
        """
        获取发改委政策

        Args:
            page (int): 页码
            limit (int): 每页条数
            use_cache (bool): 是否使用缓存
            cache_days (int): 缓存有效期（天）

        Returns:
            pandas.DataFrame: 发改委政策数据
        """
        # 生成缓存文件名
        cache_file = os.path.join(self.cache_dir, f'ndrc_policy_p{page}_l{limit}.csv')

        # 检查缓存
        if use_cache:
            cached_data = self._check_cache(cache_file, cache_days)
            if cached_data is not None:
                return cached_data

        try:
            logger.info(f"Fetching NDRC policy, page: {page}, limit: {limit}")
            start_time = time.time()

            # 发改委政策网址
            url = "https://www.ndrc.gov.cn/xxgk/"

            # 发送请求
            response = requests.get(url, headers=self.headers, timeout=10)
            response.raise_for_status()
            response.encoding = 'utf-8'

            # 解析HTML
            soup = BeautifulSoup(response.text, 'html.parser')

            # 提取政策列表
            policy_list = []

            # 查找政策列表
            policy_items = soup.select('.u-list li')

            for item in policy_items[:limit]:
                # 提取标题和链接
                title_tag = item.select_one('a')
                if title_tag:
                    title = title_tag.get_text().strip()
                    link = title_tag.get('href')
                    if not link.startswith('http'):
                        link = 'https://www.ndrc.gov.cn' + link
                else:
                    continue

                # 提取日期
                date_tag = item.select_one('.date')
                publish_date = date_tag.get_text().strip() if date_tag else ''

                # 添加到列表
                policy_list.append({
                    'title': title,
                    'url': link,
                    'publish_date': publish_date,
                    'source': '发改委'
                })

            # 创建DataFrame
            policy_df = pd.DataFrame(policy_list) if policy_list else pd.DataFrame(columns=['title', 'url', 'publish_date', 'source'])

            end_time = time.time()
            logger.info(f"NDRC policy fetched successfully in {end_time - start_time:.2f}s. Shape: {policy_df.shape}")

            # 保存缓存
            if use_cache:
                self._save_cache(policy_df, cache_file)

            return policy_df

        except Exception as e:
            logger.error(f"Error fetching NDRC policy: {str(e)}")
            return pd.DataFrame(columns=['title', 'url', 'publish_date', 'source'])

    def get_policy_content(self, url, use_cache=True, cache_days=365):
        """
        获取政策内容

        Args:
            url (str): 政策URL
            use_cache (bool): 是否使用缓存
            cache_days (int): 缓存有效期（天）

        Returns:
            dict: 政策内容
        """
        # 生成缓存文件名
        cache_key = re.sub(r'[\\/:*?"<>|]', '_', url)
        cache_file = os.path.join(self.cache_dir, f'policy_content_{cache_key}.json')

        # 检查缓存
        if use_cache and os.path.exists(cache_file):
            # 检查缓存是否过期
            file_time = datetime.fromtimestamp(os.path.getmtime(cache_file))
            if datetime.now() - file_time < timedelta(days=cache_days):
                logger.info(f"Loading policy content from cache: {cache_file}")
                try:
                    with open(cache_file, 'r', encoding='utf-8') as f:
                        return json.load(f)
                except Exception as e:
                    logger.warning(f"Error reading cache: {str(e)}")

        try:
            logger.info(f"Fetching policy content: {url}")
            start_time = time.time()

            # 发送请求
            response = requests.get(url, headers=self.headers, timeout=10)
            response.raise_for_status()
            response.encoding = 'utf-8'

            # 解析HTML
            soup = BeautifulSoup(response.text, 'html.parser')

            # 提取标题
            title = ''
            title_tag = soup.select_one('h1')
            if title_tag:
                title = title_tag.get_text().strip()

            # 提取内容
            content = ''
            content_tags = soup.select('p[style*="text-indent"]')
            if content_tags:
                content = '\n'.join([tag.get_text().strip() for tag in content_tags])

            # 如果没有找到内容，尝试其他选择器
            if not content:
                content_div = soup.select_one('#UCAP-CONTENT')
                if content_div:
                    content = content_div.get_text().strip()

            result = {
                'title': title,
                'content': content,
                'url': url
            }

            end_time = time.time()
            logger.info(f"Policy content fetched successfully in {end_time - start_time:.2f}s.")

            # 保存缓存
            if use_cache:
                try:
                    with open(cache_file, 'w', encoding='utf-8') as f:
                        json.dump(result, f, ensure_ascii=False, indent=2)
                    logger.info(f"Saved policy content to cache: {cache_file}")
                except Exception as e:
                    logger.warning(f"Error saving cache: {str(e)}")

            return result

        except Exception as e:
            logger.error(f"Error fetching policy content: {str(e)}")
            return {'title': '', 'content': '', 'url': url}

    # 新闻数据获取方法
    def get_financial_breakfast(self, use_cache=True, cache_days=1):
        """
        获取财经早餐-东财财富

        Args:
            use_cache (bool): 是否使用缓存
            cache_days (int): 缓存有效期（天）

        Returns:
            pandas.DataFrame: 财经早餐数据
        """
        # 生成缓存文件名
        cache_file = os.path.join(self.cache_dir, 'financial_breakfast.csv')

        # 检查缓存
        if use_cache:
            cached_data = self._check_cache(cache_file, cache_days)
            if cached_data is not None:
                return cached_data

        try:
            logger.info("Fetching financial breakfast news")
            start_time = time.time()

            # 使用akshare获取财经早餐
            news_df = ak.stock_info_cjzc_em()

            # 重命名列
            news_df = news_df.rename(columns={
                '标题': 'title',
                '摘要': 'summary',
                '发布时间': 'publish_date',
                '链接': 'url'
            })

            # 添加来源列
            news_df['source'] = '东方财富财经早餐'

            end_time = time.time()
            logger.info(f"Financial breakfast news fetched successfully in {end_time - start_time:.2f}s. Shape: {news_df.shape}")

            # 保存缓存
            if use_cache:
                self._save_cache(news_df, cache_file)

            return news_df

        except Exception as e:
            logger.error(f"Error fetching financial breakfast news: {str(e)}")
            return pd.DataFrame(columns=['title', 'summary', 'publish_date', 'url', 'source'])

    def get_global_news_em(self, use_cache=True, cache_days=1):
        """
        获取全球财经快讯-东财财富

        Args:
            use_cache (bool): 是否使用缓存
            cache_days (int): 缓存有效期（天）

        Returns:
            pandas.DataFrame: 全球财经快讯数据
        """
        # 生成缓存文件名
        cache_file = os.path.join(self.cache_dir, 'global_news_em.csv')

        # 检查缓存
        if use_cache:
            cached_data = self._check_cache(cache_file, cache_days)
            if cached_data is not None:
                return cached_data

        try:
            logger.info("Fetching global financial news from EastMoney")
            start_time = time.time()

            # 使用akshare获取全球财经快讯
            news_df = ak.stock_info_global_em()

            # 重命名列
            news_df = news_df.rename(columns={
                '标题': 'title',
                '摘要': 'summary',
                '发布时间': 'publish_date',
                '链接': 'url'
            })

            # 添加来源列
            news_df['source'] = '东方财富全球财经快讯'

            end_time = time.time()
            logger.info(f"Global financial news from EastMoney fetched successfully in {end_time - start_time:.2f}s. Shape: {news_df.shape}")

            # 保存缓存
            if use_cache:
                self._save_cache(news_df, cache_file)

            return news_df

        except Exception as e:
            logger.error(f"Error fetching global financial news from EastMoney: {str(e)}")
            return pd.DataFrame(columns=['title', 'summary', 'publish_date', 'url', 'source'])

    def get_global_news_sina(self, use_cache=True, cache_days=1):
        """
        获取全球财经快讯-新浪财经

        Args:
            use_cache (bool): 是否使用缓存
            cache_days (int): 缓存有效期（天）

        Returns:
            pandas.DataFrame: 全球财经快讯数据
        """
        # 生成缓存文件名
        cache_file = os.path.join(self.cache_dir, 'global_news_sina.csv')

        # 检查缓存
        if use_cache:
            cached_data = self._check_cache(cache_file, cache_days)
            if cached_data is not None:
                return cached_data

        try:
            logger.info("Fetching global financial news from Sina")
            start_time = time.time()

            # 使用akshare获取全球财经快讯
            news_df = ak.stock_info_global_sina()

            # 重命名列
            news_df = news_df.rename(columns={
                '时间': 'publish_date',
                '内容': 'content'
            })

            # 添加来源列和URL列
            news_df['source'] = '新浪财经全球财经快讯'
            news_df['url'] = 'https://finance.sina.com.cn/7x24'

            end_time = time.time()
            logger.info(f"Global financial news from Sina fetched successfully in {end_time - start_time:.2f}s. Shape: {news_df.shape}")

            # 保存缓存
            if use_cache:
                self._save_cache(news_df, cache_file)

            return news_df

        except Exception as e:
            logger.error(f"Error fetching global financial news from Sina: {str(e)}")
            return pd.DataFrame(columns=['publish_date', 'content', 'source', 'url'])

    def get_global_news_futu(self, use_cache=True, cache_days=1):
        """
        获取快讯-富途牛牛

        Args:
            use_cache (bool): 是否使用缓存
            cache_days (int): 缓存有效期（天）

        Returns:
            pandas.DataFrame: 快讯数据
        """
        # 生成缓存文件名
        cache_file = os.path.join(self.cache_dir, 'global_news_futu.csv')

        # 检查缓存
        if use_cache:
            cached_data = self._check_cache(cache_file, cache_days)
            if cached_data is not None:
                return cached_data

        try:
            logger.info("Fetching news from Futu")
            start_time = time.time()

            # 使用akshare获取富途牛牛快讯
            news_df = ak.stock_info_global_futu()

            # 重命名列
            news_df = news_df.rename(columns={
                '标题': 'title',
                '内容': 'content',
                '发布时间': 'publish_date',
                '链接': 'url'
            })

            # 添加来源列
            news_df['source'] = '富途牛牛快讯'

            end_time = time.time()
            logger.info(f"News from Futu fetched successfully in {end_time - start_time:.2f}s. Shape: {news_df.shape}")

            # 保存缓存
            if use_cache:
                self._save_cache(news_df, cache_file)

            return news_df

        except Exception as e:
            logger.error(f"Error fetching news from Futu: {str(e)}")
            return pd.DataFrame(columns=['title', 'content', 'publish_date', 'url', 'source'])

    def get_global_news_ths(self, use_cache=True, cache_days=1):
        """
        获取全球财经直播-同花顺财经

        Args:
            use_cache (bool): 是否使用缓存
            cache_days (int): 缓存有效期（天）

        Returns:
            pandas.DataFrame: 全球财经直播数据
        """
        # 生成缓存文件名
        cache_file = os.path.join(self.cache_dir, 'global_news_ths.csv')

        # 检查缓存
        if use_cache:
            cached_data = self._check_cache(cache_file, cache_days)
            if cached_data is not None:
                return cached_data

        try:
            logger.info("Fetching global financial news from THS")
            start_time = time.time()

            # 使用akshare获取同花顺财经全球财经直播
            news_df = ak.stock_info_global_ths()

            # 重命名列
            news_df = news_df.rename(columns={
                '标题': 'title',
                '内容': 'content',
                '发布时间': 'publish_date',
                '链接': 'url'
            })

            # 添加来源列
            news_df['source'] = '同花顺财经全球财经直播'

            end_time = time.time()
            logger.info(f"Global financial news from THS fetched successfully in {end_time - start_time:.2f}s. Shape: {news_df.shape}")

            # 保存缓存
            if use_cache:
                self._save_cache(news_df, cache_file)

            return news_df

        except Exception as e:
            logger.error(f"Error fetching global financial news from THS: {str(e)}")
            return pd.DataFrame(columns=['title', 'content', 'publish_date', 'url', 'source'])

    def get_global_news_cls(self, symbol="全部", use_cache=True, cache_days=1):
        """
        获取电报-财联社

        Args:
            symbol (str): 类型，可选值为"全部"或"重点"
            use_cache (bool): 是否使用缓存
            cache_days (int): 缓存有效期（天）

        Returns:
            pandas.DataFrame: 电报数据
        """
        # 生成缓存文件名
        cache_file = os.path.join(self.cache_dir, f'global_news_cls_{symbol}.csv')

        # 检查缓存
        if use_cache:
            cached_data = self._check_cache(cache_file, cache_days)
            if cached_data is not None:
                return cached_data

        try:
            logger.info(f"Fetching news from CLS, symbol: {symbol}")
            start_time = time.time()

            # 使用akshare获取财联社电报
            news_df = ak.stock_info_global_cls(symbol=symbol)

            # 检查列名
            if '标题' in news_df.columns and '内容' in news_df.columns:
                # 重命名列
                news_df = news_df.rename(columns={
                    '标题': 'title',
                    '内容': 'content'
                })

                # 处理日期和时间
                if '发布日期' in news_df.columns and '发布时间' in news_df.columns:
                    news_df = news_df.rename(columns={
                        '发布日期': 'date',
                        '发布时间': 'time'
                    })
                    # 合并日期和时间
                    news_df['publish_date'] = news_df['date'].astype(str) + ' ' + news_df['time'].astype(str)
                    news_df = news_df.drop(['date', 'time'], axis=1)
                else:
                    # 如果没有日期和时间列，添加当前时间
                    news_df['publish_date'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            else:
                # 如果列名不匹配，尝试直接使用原始列名
                if len(news_df.columns) >= 2:
                    news_df = news_df.rename(columns={
                        news_df.columns[0]: 'title',
                        news_df.columns[1]: 'content'
                    })
                    news_df['publish_date'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

            # 添加来源列和URL列
            news_df['source'] = '财联社电报'
            news_df['url'] = 'https://www.cls.cn/telegraph'

            end_time = time.time()
            logger.info(f"News from CLS fetched successfully in {end_time - start_time:.2f}s. Shape: {news_df.shape}")

            # 保存缓存
            if use_cache:
                self._save_cache(news_df, cache_file)

            return news_df

        except Exception as e:
            logger.error(f"Error fetching news from CLS: {str(e)}")
            return pd.DataFrame(columns=['title', 'content', 'publish_date', 'source', 'url'])

    def get_broker_news_sina(self, page="1", use_cache=True, cache_days=1):
        """
        获取证券原创-新浪财经

        Args:
            page (str): 页码
            use_cache (bool): 是否使用缓存
            cache_days (int): 缓存有效期（天）

        Returns:
            pandas.DataFrame: 证券原创数据
        """
        # 生成缓存文件名
        cache_file = os.path.join(self.cache_dir, f'broker_news_sina_p{page}.csv')

        # 检查缓存
        if use_cache:
            cached_data = self._check_cache(cache_file, cache_days)
            if cached_data is not None:
                return cached_data

        try:
            logger.info(f"Fetching broker news from Sina, page: {page}")
            start_time = time.time()

            # 使用网页抓取替代API
            url = f"https://finance.sina.com.cn/roll/index.d.html?cid=221431&page={page}"
            response = requests.get(url, headers=self.headers, timeout=10)
            response.raise_for_status()
            response.encoding = 'utf-8'

            # 解析HTML
            soup = BeautifulSoup(response.text, 'html.parser')

            # 提取新闻列表
            news_list = []

            # 查找新闻列表
            news_items = soup.select('.list_009 li')

            for item in news_items:
                # 提取标题和链接
                title_tag = item.select_one('a')
                if title_tag:
                    title = title_tag.get_text().strip()
                    link = title_tag.get('href')
                else:
                    continue

                # 提取日期
                date_tag = item.select_one('span')
                publish_date = date_tag.get_text().strip() if date_tag else ''

                # 添加到列表
                news_list.append({
                    'title': title,
                    'url': link,
                    'publish_date': publish_date,
                    'source': '新浪财经证券原创'
                })

            # 创建DataFrame
            news_df = pd.DataFrame(news_list) if news_list else pd.DataFrame(columns=['title', 'url', 'publish_date', 'source'])

            end_time = time.time()
            logger.info(f"Broker news from Sina fetched successfully in {end_time - start_time:.2f}s. Shape: {news_df.shape}")

            # 保存缓存
            if use_cache:
                self._save_cache(news_df, cache_file)

            return news_df

        except Exception as e:
            logger.error(f"Error fetching broker news from Sina: {str(e)}")
            return pd.DataFrame(columns=['title', 'url', 'publish_date', 'source'])

    def get_stock_news_em(self, symbol, use_cache=True, cache_days=1):
        """
        获取个股新闻-东方财富

        Args:
            symbol (str): 股票代码，如"300059"
            use_cache (bool): 是否使用缓存
            cache_days (int): 缓存有效期（天）

        Returns:
            pandas.DataFrame: 个股新闻数据
        """
        # 生成缓存文件名
        cache_file = os.path.join(self.cache_dir, f'stock_news_em_{symbol}.csv')

        # 检查缓存
        if use_cache:
            cached_data = self._check_cache(cache_file, cache_days)
            if cached_data is not None:
                return cached_data

        try:
            logger.info(f"Fetching stock news from EastMoney for symbol: {symbol}")
            start_time = time.time()

            # 使用akshare获取个股新闻
            news_df = ak.stock_news_em(symbol=symbol)

            # 重命名列
            news_df = news_df.rename(columns={
                '关键词': 'keyword',
                '新闻标题': 'title',
                '新闻内容': 'content',
                '发布时间': 'publish_date',
                '文章来源': 'source',
                '新闻链接': 'url'
            })

            end_time = time.time()
            logger.info(f"Stock news from EastMoney fetched successfully in {end_time - start_time:.2f}s. Shape: {news_df.shape}")

            # 保存缓存
            if use_cache:
                self._save_cache(news_df, cache_file)

            return news_df

        except Exception as e:
            logger.error(f"Error fetching stock news from EastMoney: {str(e)}")
            return pd.DataFrame(columns=['keyword', 'title', 'content', 'publish_date', 'source', 'url'])

    def get_news_main_cx(self, use_cache=True, cache_days=1):
        """
        获取财经内容精选-财新网

        Args:
            use_cache (bool): 是否使用缓存
            cache_days (int): 缓存有效期（天）

        Returns:
            pandas.DataFrame: 财经内容精选数据
        """
        # 生成缓存文件名
        cache_file = os.path.join(self.cache_dir, 'news_main_cx.csv')

        # 检查缓存
        if use_cache:
            cached_data = self._check_cache(cache_file, cache_days)
            if cached_data is not None:
                return cached_data

        try:
            logger.info("Fetching financial news from Caixin")
            start_time = time.time()

            # 使用akshare获取财新网财经内容精选
            news_df = ak.stock_news_main_cx()

            # 重命名列
            news_df = news_df.rename(columns={
                'tag': 'tag',
                'summary': 'summary',
                'interval_time': 'interval_time',
                'pub_time': 'publish_date',
                'url': 'url'
            })

            # 添加来源列
            news_df['source'] = '财新网'

            # 提取标题（使用summary作为标题）
            news_df['title'] = news_df['summary']

            end_time = time.time()
            logger.info(f"Financial news from Caixin fetched successfully in {end_time - start_time:.2f}s. Shape: {news_df.shape}")

            # 保存缓存
            if use_cache:
                self._save_cache(news_df, cache_file)

            return news_df

        except Exception as e:
            logger.error(f"Error fetching financial news from Caixin: {str(e)}")
            return pd.DataFrame(columns=['tag', 'summary', 'interval_time', 'publish_date', 'url', 'source', 'title'])
