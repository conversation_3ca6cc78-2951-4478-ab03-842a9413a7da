"""
新闻去重与聚类示例

展示如何使用新闻去重与聚类模块
"""

import os
import sys
import time
import json
from datetime import datetime, timedelta
import pandas as pd
import matplotlib.pyplot as plt
from pprint import pprint

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入新闻去重与聚类模块
from data_sources.news_clustering import NewsClusteringModule

# 导入统一新闻监控器
from data_sources.unified_news_monitor import UnifiedNewsMonitor

# 导入中央调度器
from core.scheduler import CentralScheduler, TaskPriority

def example_basic_usage():
    """基本使用示例"""
    print("\n=== 基本使用示例 ===")
    
    # 创建调度器
    scheduler = CentralScheduler()
    
    # 创建新闻去重与聚类模块
    clustering = NewsClusteringModule()
    
    # 设置调度器
    clustering.set_scheduler(scheduler)
    
    # 启动调度器
    scheduler.start()
    
    # 初始化模块
    clustering.initialize()
    
    # 等待初始化完成
    print("等待初始化完成...")
    time.sleep(3)
    
    # 获取模块状态
    status = clustering.get_status()
    print(f"模块状态: {json.dumps(status, indent=2, ensure_ascii=False)}")
    
    # 停止调度器
    scheduler.stop()
    
    print("基本使用示例完成")

def example_news_deduplication():
    """新闻去重示例"""
    print("\n=== 新闻去重示例 ===")
    
    # 创建新闻去重与聚类模块
    clustering = NewsClusteringModule()
    
    # 创建一些测试新闻
    test_news = [
        {
            'id': '1',
            'title': '央行宣布降息，LPR下调10个基点',
            'content': '中国人民银行今日宣布下调贷款市场报价利率（LPR），1年期LPR下调10个基点至3.45%，5年期以上LPR下调10个基点至4.2%。',
            'source': '财经网',
            'publish_date': datetime.now().isoformat()
        },
        {
            'id': '2',
            'title': '央行宣布降息，1年期LPR下调10个基点',
            'content': '央行今日宣布下调贷款市场报价利率，1年期LPR下调10个基点至3.45%，5年期以上LPR下调10个基点至4.2%。',
            'source': '金融时报',
            'publish_date': datetime.now().isoformat()
        },
        {
            'id': '3',
            'title': '国务院发布新能源汽车产业发展规划',
            'content': '国务院今日发布《新能源汽车产业发展规划（2021-2035年）》，提出到2025年，新能源汽车新车销售量达到汽车新车销售总量的20%左右。',
            'source': '中国政府网',
            'publish_date': datetime.now().isoformat()
        },
        {
            'id': '4',
            'title': '发改委：加快推进新能源汽车产业发展',
            'content': '国家发展改革委今日表示，将加快推进新能源汽车产业发展，完善充电基础设施建设，促进新能源汽车消费。',
            'source': '发改委网站',
            'publish_date': datetime.now().isoformat()
        },
        {
            'id': '5',
            'title': '央行宣布降息10个基点',
            'content': '中国人民银行宣布下调贷款市场报价利率（LPR），1年期和5年期以上LPR均下调10个基点。',
            'source': '新华社',
            'publish_date': datetime.now().isoformat()
        }
    ]
    
    print(f"原始新闻数量: {len(test_news)}")
    print("原始新闻标题:")
    for news in test_news:
        print(f"- {news['title']} (来源: {news['source']})")
    
    # 去重
    print("\n开始去重...")
    unique_news = clustering.deduplicate_news(test_news)
    
    print(f"去重后新闻数量: {len(unique_news)}")
    print("去重后新闻标题:")
    for news in unique_news:
        print(f"- {news['title']} (来源: {news['source']})")
    
    # 查看指纹
    print(f"\n新闻指纹数量: {len(clustering.news_fingerprints)}")
    
    print("新闻去重示例完成")

def example_news_clustering():
    """新闻聚类示例"""
    print("\n=== 新闻聚类示例 ===")
    
    # 创建新闻去重与聚类模块
    clustering = NewsClusteringModule()
    
    # 创建一些测试新闻
    test_news = [
        {
            'id': '1',
            'title': '央行宣布降息，LPR下调10个基点',
            'content': '中国人民银行今日宣布下调贷款市场报价利率（LPR），1年期LPR下调10个基点至3.45%，5年期以上LPR下调10个基点至4.2%。',
            'source': '财经网',
            'publish_date': datetime.now().isoformat()
        },
        {
            'id': '2',
            'title': '央行宣布降准降息组合拳',
            'content': '央行今日宣布下调存款准备金率0.5个百分点，同时下调贷款市场报价利率，1年期LPR下调10个基点至3.45%。',
            'source': '金融时报',
            'publish_date': datetime.now().isoformat()
        },
        {
            'id': '3',
            'title': '国务院发布新能源汽车产业发展规划',
            'content': '国务院今日发布《新能源汽车产业发展规划（2021-2035年）》，提出到2025年，新能源汽车新车销售量达到汽车新车销售总量的20%左右。',
            'source': '中国政府网',
            'publish_date': datetime.now().isoformat()
        },
        {
            'id': '4',
            'title': '发改委：加快推进新能源汽车产业发展',
            'content': '国家发展改革委今日表示，将加快推进新能源汽车产业发展，完善充电基础设施建设，促进新能源汽车消费。',
            'source': '发改委网站',
            'publish_date': datetime.now().isoformat()
        },
        {
            'id': '5',
            'title': '工信部：推动新能源汽车产业高质量发展',
            'content': '工业和信息化部表示，将推动新能源汽车产业高质量发展，加强关键核心技术攻关，提升产业链供应链现代化水平。',
            'source': '工信部网站',
            'publish_date': datetime.now().isoformat()
        },
        {
            'id': '6',
            'title': '财政部：继续实施新能源汽车购置补贴政策',
            'content': '财政部表示，将继续实施新能源汽车购置补贴政策，优化补贴结构，重点支持纯电动汽车和燃料电池汽车发展。',
            'source': '财政部网站',
            'publish_date': datetime.now().isoformat()
        },
        {
            'id': '7',
            'title': '央行行长：稳健的货币政策要灵活适度',
            'content': '中国人民银行行长易纲表示，稳健的货币政策要灵活适度，保持流动性合理充裕，引导金融机构加大对实体经济的支持力度。',
            'source': '央行网站',
            'publish_date': datetime.now().isoformat()
        },
        {
            'id': '8',
            'title': '证监会：推动资本市场高质量发展',
            'content': '证监会表示，将推动资本市场高质量发展，完善基础制度，提高上市公司质量，强化中介机构责任，保护投资者合法权益。',
            'source': '证监会网站',
            'publish_date': datetime.now().isoformat()
        }
    ]
    
    print(f"原始新闻数量: {len(test_news)}")
    
    # 聚类
    print("\n开始聚类...")
    clustered_news = clustering.cluster_news(test_news)
    
    print(f"聚类后新闻数量: {len(clustered_news)}")
    print(f"聚类数量: {len(clustering.clusters)}")
    
    # 显示聚类结果
    print("\n聚类结果:")
    for i, cluster in enumerate(clustering.clusters):
        print(f"\n聚类 {i+1}:")
        print(f"  ID: {cluster['id']}")
        print(f"  大小: {cluster['size']}")
        print(f"  关键词: {', '.join(cluster['keywords'])}")
        print("  新闻:")
        for news in cluster['news'][:3]:  # 只显示前3条
            print(f"    - {news['title']} (来源: {news['source']})")
    
    # 显示新闻的聚类信息
    print("\n新闻的聚类信息:")
    for i, news in enumerate(clustered_news[:3]):  # 只显示前3条
        print(f"\n新闻 {i+1}: {news['title']}")
        if 'cluster_id' in news:
            print(f"  聚类ID: {news['cluster_id']}")
            print(f"  聚类关键词: {', '.join(news.get('cluster_keywords', []))}")
            print(f"  聚类大小: {news.get('cluster_size', 0)}")
        else:
            print("  未被聚类")
    
    print("新闻聚类示例完成")

def example_integration_with_news_monitor():
    """与统一新闻监控器集成示例"""
    print("\n=== 与统一新闻监控器集成示例 ===")
    
    # 创建调度器
    scheduler = CentralScheduler()
    
    # 创建统一新闻监控器
    news_monitor = UnifiedNewsMonitor()
    
    # 创建新闻去重与聚类模块
    clustering = NewsClusteringModule()
    
    # 设置调度器
    news_monitor.set_scheduler(scheduler)
    clustering.set_scheduler(scheduler)
    
    # 启动调度器
    scheduler.start()
    
    # 获取新闻
    print("获取新闻...")
    news_result = news_monitor.fetch_news()
    
    if news_result['status'] == 'success' and news_result['count'] > 0:
        # 获取处理后的新闻
        processed_news = news_monitor.processed_news
        
        print(f"获取到{len(processed_news)}条新闻")
        
        # 去重
        print("\n对新闻进行去重...")
        unique_news = clustering.deduplicate_news(processed_news)
        
        print(f"去重后剩余{len(unique_news)}条新闻")
        
        # 聚类
        print("\n对去重后的新闻进行聚类...")
        clustered_news = clustering.cluster_news(unique_news)
        
        print(f"聚类完成，共{len(clustering.clusters)}个聚类")
        
        # 显示部分聚类结果
        if clustering.clusters:
            print("\n部分聚类结果:")
            for i, cluster in enumerate(clustering.clusters[:3]):  # 只显示前3个聚类
                print(f"\n聚类 {i+1}:")
                print(f"  大小: {cluster['size']}")
                print(f"  关键词: {', '.join(cluster['keywords'])}")
                print("  部分新闻:")
                for news in cluster['news'][:2]:  # 只显示前2条新闻
                    print(f"    - {news['title']} (来源: {news.get('source', '未知')})")
    else:
        print(f"获取新闻失败: {news_result['message']}")
    
    # 停止调度器
    scheduler.stop()
    
    print("与统一新闻监控器集成示例完成")

def example_clean_expired_fingerprints():
    """清理过期指纹示例"""
    print("\n=== 清理过期指纹示例 ===")
    
    # 创建新闻去重与聚类模块
    clustering = NewsClusteringModule()
    
    # 添加一些测试指纹
    current_time = datetime.now()
    old_time = current_time - timedelta(days=10)
    
    # 添加新指纹
    for i in range(5):
        fingerprint = f"new_fingerprint_{i}"
        clustering.news_fingerprints[fingerprint] = {
            'id': f"news_{i}",
            'title': f"新闻标题 {i}",
            'timestamp': current_time.isoformat()
        }
    
    # 添加旧指纹
    for i in range(5):
        fingerprint = f"old_fingerprint_{i}"
        clustering.news_fingerprints[fingerprint] = {
            'id': f"old_news_{i}",
            'title': f"旧新闻标题 {i}",
            'timestamp': old_time.isoformat()
        }
    
    print(f"清理前指纹数量: {len(clustering.news_fingerprints)}")
    
    # 清理过期指纹
    print("\n开始清理过期指纹...")
    result = clustering.clean_expired_fingerprints()
    
    print(f"清理结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
    print(f"清理后指纹数量: {len(clustering.news_fingerprints)}")
    
    print("清理过期指纹示例完成")

def main():
    """主函数"""
    print("=== 新闻去重与聚类示例 ===")
    
    # 创建必要的目录
    os.makedirs('logs', exist_ok=True)
    os.makedirs('data', exist_ok=True)
    os.makedirs('config', exist_ok=True)
    
    # 询问用户选择示例
    print("\n请选择要运行的示例:")
    print("1. 基本使用示例")
    print("2. 新闻去重示例")
    print("3. 新闻聚类示例")
    print("4. 与统一新闻监控器集成示例")
    print("5. 清理过期指纹示例")
    print("6. 运行所有示例")
    print("0. 退出")
    
    choice = input("\n请输入选项（0-6）: ")
    
    if choice == '1':
        example_basic_usage()
    elif choice == '2':
        example_news_deduplication()
    elif choice == '3':
        example_news_clustering()
    elif choice == '4':
        example_integration_with_news_monitor()
    elif choice == '5':
        example_clean_expired_fingerprints()
    elif choice == '6':
        example_basic_usage()
        example_news_deduplication()
        example_news_clustering()
        example_integration_with_news_monitor()
        example_clean_expired_fingerprints()
    elif choice == '0':
        print("退出")
    else:
        print("无效选项")

if __name__ == '__main__':
    main()
