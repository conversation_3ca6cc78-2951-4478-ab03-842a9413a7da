"""
Data utility functions for policy_liquidity_volatility_arbitrage.
"""

import pandas as pd
import numpy as np
import datetime
import akshare as ak
from utils.logger import logger
from utils.error_utils import retry, handle_api_error

@retry(max_retries=3, delay=2)
def get_latest_trading_date():
    """
    Get the latest trading date in the Chinese A-share market.
    
    Returns:
        datetime.date: Latest trading date.
    """
    try:
        # Get trading calendar
        calendar_df = ak.tool_trade_date_hist_sina()
        latest_date = pd.to_datetime(calendar_df.iloc[-1, 0]).date()
        return latest_date
    except Exception as e:
        logger.error(f"Error getting latest trading date: {str(e)}")
        # Fallback to current date
        return datetime.date.today()

@retry(max_retries=3, delay=2)
def get_trading_dates(start_date, end_date=None):
    """
    Get trading dates between start_date and end_date.
    
    Args:
        start_date (str or datetime): Start date in format 'YYYY-MM-DD'.
        end_date (str or datetime, optional): End date in format 'YYYY-MM-DD'. Defaults to today.
        
    Returns:
        list: List of trading dates as datetime.date objects.
    """
    try:
        # Convert dates to string format if they are datetime objects
        if isinstance(start_date, (datetime.date, datetime.datetime)):
            start_date = start_date.strftime('%Y-%m-%d')
        
        if end_date is None:
            end_date = datetime.date.today().strftime('%Y-%m-%d')
        elif isinstance(end_date, (datetime.date, datetime.datetime)):
            end_date = end_date.strftime('%Y-%m-%d')
        
        # Get trading calendar
        calendar_df = ak.tool_trade_date_hist_sina()
        calendar_df['trade_date'] = pd.to_datetime(calendar_df.iloc[:, 0]).dt.date
        
        # Filter dates
        filtered_dates = calendar_df[
            (calendar_df['trade_date'] >= pd.to_datetime(start_date).date()) & 
            (calendar_df['trade_date'] <= pd.to_datetime(end_date).date())
        ]['trade_date'].tolist()
        
        return filtered_dates
    except Exception as e:
        logger.error(f"Error getting trading dates: {str(e)}")
        return []

def convert_amount(amount_str):
    """
    Convert Chinese amount string to numeric value.
    
    Args:
        amount_str (str): Amount string, e.g., '1.2亿', '3456万', '789.1千'.
        
    Returns:
        float: Numeric value.
    """
    try:
        if not amount_str or pd.isna(amount_str):
            return 0.0
        
        amount_str = str(amount_str).strip()
        
        # Handle numeric values directly
        if amount_str.replace('.', '', 1).isdigit():
            return float(amount_str)
        
        # Extract numeric part
        numeric_part = ''.join([c for c in amount_str if c.isdigit() or c == '.'])
        if not numeric_part:
            return 0.0
        
        numeric_value = float(numeric_part)
        
        # Apply multiplier based on unit
        if '亿' in amount_str:
            return numeric_value * 100000000
        elif '万' in amount_str:
            return numeric_value * 10000
        elif '千' in amount_str:
            return numeric_value * 1000
        else:
            return numeric_value
    except Exception as e:
        logger.error(f"Error converting amount '{amount_str}': {str(e)}")
        return 0.0

def normalize_by_market_value(df, value_column, market_value_column):
    """
    Normalize values by market value.
    
    Args:
        df (pandas.DataFrame): DataFrame containing data.
        value_column (str): Column name of values to normalize.
        market_value_column (str): Column name of market values.
        
    Returns:
        pandas.DataFrame: DataFrame with normalized values.
    """
    try:
        if value_column not in df.columns or market_value_column not in df.columns:
            logger.warning(f"Columns {value_column} or {market_value_column} not found in DataFrame.")
            return df
        
        # Create a copy to avoid modifying the original DataFrame
        result_df = df.copy()
        
        # Normalize values
        result_df[f'normalized_{value_column}'] = result_df[value_column] / result_df[market_value_column]
        
        return result_df
    except Exception as e:
        logger.error(f"Error normalizing by market value: {str(e)}")
        return df

@handle_api_error
def get_stock_info(stock_code):
    """
    Get basic information for a stock.
    
    Args:
        stock_code (str): Stock code, e.g., '600000' or 'sh600000'.
        
    Returns:
        dict: Stock information.
    """
    # Ensure stock code is in the correct format
    if stock_code.startswith('sh') or stock_code.startswith('sz'):
        formatted_code = stock_code
    else:
        if stock_code.startswith('6'):
            formatted_code = f'sh{stock_code}'
        else:
            formatted_code = f'sz{stock_code}'
    
    # Get stock information
    try:
        stock_info_df = ak.stock_individual_info_em(symbol=formatted_code)
        
        # Convert DataFrame to dictionary
        stock_info = {}
        for _, row in stock_info_df.iterrows():
            stock_info[row['item']] = row['value']
        
        return stock_info
    except Exception as e:
        logger.error(f"Error getting stock info for {stock_code}: {str(e)}")
        return {}
